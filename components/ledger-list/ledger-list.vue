<template>
	<view class="ledger-list-container">
		<!-- 状态筛选 Tab -->
		<view class="tabs">
			<scroll-view scroll-x="true" class="scroll-view">
				<view
					v-for="(tab, index) in tabs"
					:key="index"
					class="tab-item"
					:class="[activeTab === tab.name ? 'active' : '']"
					@click="handleTabClick(tab.name)"
				>
					{{ tab.name }}
					<text v-if="tab.count > 0" class="badge">({{ tab.count }})</text>
				</view>
			</scroll-view>
		</view>

		<!-- 搜索与过滤区 -->
		<view class="filter-section">
			<view class="search-bar">
				<slot name="search"></slot>
			</view>
		</view>

		<!-- 数据列表 -->
		<view class="list-container">
			<view v-for="item in dataList" :key="item.F_Id" class="list-item-card" @click="$emit('item-click', item)">
				<slot name="item" :item="item"></slot>
			</view>
		</view>

		<!-- 悬浮操作按钮 -->
		<view class="fab-container">
			<button class="fab-btn" @click="$emit('fab-click')">
				<text class="icon-add">+</text>
				{{ fabText }}
			</button>
		</view>
	</view>
</template>

<script>
export default {
	name: 'LedgerList',
	components: {},
	props: {
		// 获取列表数据的API
		// 获取列表数据的API方法名
		listApiName: {
			type: String,
			required: true,
		},
		// 获取状态统计的API方法名
		statsApiName: {
			type: String,
			required: true,
		},
		// 状态Tab列表
		statusTabs: {
			type: Array,
			required: true,
		},
		// 外部传入的搜索参数
		searchParams: {
			type: Object,
			default: () => ({}),
		},
		// 悬浮按钮文本
		fabText: {
			type: String,
			default: '发起审批',
		},
	},
	data() {
		return {
			activeTab: this.statusTabs.length > 0 ? this.statusTabs[0] : '全部',
			tabs: [],
			dataList: [],
		};
	},
	watch: {
		searchParams: {
			handler() {
				this.refresh();
			},
			deep: true,
		},
	},
	mounted() {
		this.refresh();
	},
	methods: {
		// 获取状态统计
		fetchStatistics() {
			this.$minApi[this.statsApiName]().then(res => {
				const stats = res.data;
				this.tabs = this.statusTabs.map(name => ({
					name: name,
					count: stats[name] || 0,
				}));
			});
		},
		// Tab点击事件
		handleTabClick(tabName) {
			if (this.activeTab !== tabName) {
				this.activeTab = tabName;
				this.refresh(); // 刷新列表
			}
		},
		// 外部调用的刷新方法
		refresh() {
			this.fetchStatistics();
			this.fetchData();
		},
		// 获取列表数据
		fetchData() {
			const params = {
				page: 1, // 简化处理，暂不分页
				rows: 100,
				status: this.activeTab === '全部' ? '' : this.activeTab,
				...this.searchParams,
			};
			console.log('params:=>', params);
			this.$minApi[this.listApiName](params)
				.then(res => {
					this.dataList = res.data || [];
				})
				.catch(err => {
					console.error('数据加载失败', err);
					this.dataList = [];
				});
		},
	},
};
</script>

<style scoped>
.ledger-list-container {
	display: flex;
	flex-direction: column;
	height: 100vh; /* 修改为视口高度 */
	background-color: #f4f4f4; /* 页面背景色 */
}

/* Tab筛选区 */
.tabs {
	background-color: #00984a; /* 绿色背景 */
	color: white;
	padding: 0 20rpx;
}
.scroll-view {
	white-space: nowrap;
}
.tab-item {
	display: inline-block;
	padding: 24rpx 30rpx;
	font-size: 30rpx;
	color: white;
	position: relative;
}
.tab-item.active {
	font-weight: bold;
	border-bottom: 6rpx solid white;
}
.badge {
	margin-left: 8rpx;
	font-size: 28rpx;
}

/* 搜索与过滤区 */
.filter-section {
	background-color: white;
	padding: 20rpx;
	margin-bottom: 20rpx;
}
.search-bar {
	/* 槽会被外部组件填充 */
}
.filter-actions {
	display: flex;
	margin-top: 20rpx;
}
.filter-btn {
	font-size: 24rpx;
	padding: 10rpx 20rpx;
	margin-right: 20rpx;
	background-color: #f0f0f0;
	border: 2rpx solid #e0e0e0;
	border-radius: 30rpx;
	line-height: 1.5;
}

/* 列表区域 */
.mescroll-body {
	flex: 1;
	height: auto; /* 覆盖mescroll的默认高度 */
}
.list-item-card {
	background-color: white;
	border-radius: 16rpx;
	margin: 0 20rpx 20rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* 悬浮操作按钮 */
.fab-container {
	position: fixed;
	bottom: 60rpx;
	right: 40rpx;
	z-index: 100;
}
.fab-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #00984a;
	color: white;
	border-radius: 100rpx;
	padding: 20rpx 40rpx;
	font-size: 32rpx;
	box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.2);
	border: none;
}
.icon-add {
	font-size: 40rpx;
	margin-right: 10rpx;
	font-weight: bold;
}
</style>