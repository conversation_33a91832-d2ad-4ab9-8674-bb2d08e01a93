/* mescroll-uni
 * version 1.1.5
 * 2019-07-25 wenju
 * http://www.mescroll.com
 */

page {
	height: 100%;
	box-sizing: border-box; /* 避免设置padding出现双滚动条的问题 */
	-webkit-overflow-scrolling: touch; /*使iOS列表滑动流畅*/
}

.mescroll-uni-warp{
	height: 100%;
}

.mescroll-uni {
	height: 100%;
	min-height: 200upx;
	overflow-y: auto;
	box-sizing: border-box; /* 避免设置padding出现双滚动条的问题 */
}

/* 定位的方式固定高度 */
.mescroll-uni.mescroll-uni-fixed{
	z-index: 1;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	width: auto; /* 使right生效 */
	height: auto; /* 使bottom生效 */
}

/* 下拉刷新区域 */
.mescroll-downwarp {
	position: relative;
	width: 100%;
	height: 0;
	overflow: hidden;
	text-align: center;
}

/* 下拉刷新--内容区,定位于区域底部 */
.mescroll-downwarp .downwarp-content {
	position: absolute;
	left: 0;
	bottom: 0;
	width: 100%;
	min-height: 60upx;
	padding: 20upx 0;
	text-align: center;
}

/* 上拉加载区域 */
.mescroll-upwarp {
	min-height: 60upx;
	padding: 30upx 0;
	text-align: center;
	clear: both;
}

/* 下拉刷新,上拉加载--提示文本 */
.mescroll-downwarp .downwarp-tip,
.mescroll-upwarp .upwarp-tip,
.mescroll-upwarp .upwarp-nodata {
	display: inline-block;
	font-size: 28upx;
	color: gray;
	vertical-align: middle;
}

.mescroll-downwarp .downwarp-tip,
.mescroll-upwarp .upwarp-tip {
	margin-left: 16upx;
}

/* 下拉刷新,上拉加载--旋转进度条 */
.mescroll-downwarp .downwarp-progress,
.mescroll-upwarp .upwarp-progress {
	display: inline-block;
	width: 32upx;
	height: 32upx;
	border-radius: 50%;
	border: 2upx solid gray;
	border-bottom-color: transparent;
	vertical-align: middle;
}

/* 旋转动画 */
.mescroll-rotate {
	-webkit-animation: mescrollRotate 0.6s linear infinite;
	animation: mescrollRotate 0.6s linear infinite;
}

@-webkit-keyframes mescrollRotate {
	0% {
		-webkit-transform: rotate(0deg);
	}

	100% {
		-webkit-transform: rotate(360deg);
	}
}

@keyframes mescrollRotate {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}

/* 无任何数据的空布局 */
.mescroll-empty {
	box-sizing: border-box;
	width: 100%;
	padding: 40upx;
	text-align: center;
}

.mescroll-empty.empty-fixed {
	z-index: 99;
	position: fixed;
	top: 20%;
	left: 0;
}

.mescroll-empty .empty-icon {
	width: 45%;
}

.mescroll-empty .empty-tip {
	margin-top: 20upx;
	font-size: 24upx;
	color: gray;
}

.mescroll-empty .empty-btn {
	display: inline-block;
	margin-top: 40upx;
	min-width: 200upx;
	padding: 18upx;
	font-size: 28upx;
	border: 1upx solid #E04B28;
	border-radius: 60upx;
	color: #E04B28;
}

.mescroll-empty .empty-btn:active {
	opacity: .75;
}

/* 回到顶部的按钮 */
.mescroll-totop {
	z-index: 9990;
	position: fixed !important; /* 避免编译到H5,在多mescroll中定位失效 */
	right: 20upx;
	bottom: 120upx;
	width: 72upx;
	height: 72upx;
	border-radius: 50%;
	opacity: 0;
}

/* 显示动画--淡入 */
.mescroll-lazy-in,
.mescroll-fade-in {
	-webkit-animation: mescrollFadeIn .3s linear forwards;
	animation: mescrollFadeIn .3s linear forwards;
}

@-webkit-keyframes mescrollFadeIn {
	0% {
		opacity: 0;
	}

	100% {
		opacity: 1;
	}
}

@keyframes mescrollFadeIn {
	0% {
		opacity: 0;
	}

	100% {
		opacity: 1;
	}
}

/* 隐藏动画--淡出 */
.mescroll-fade-out {
	pointer-events: none;
	-webkit-animation: mescrollFadeOut .5s linear forwards;
	animation: mescrollFadeOut .5s linear forwards;
}

@-webkit-keyframes mescrollFadeOut {
	0% {
		opacity: 1;
	}

	100% {
		opacity: 0;
	}
}

@keyframes mescrollFadeOut {
	0% {
		opacity: 1;
	}

	100% {
		opacity: 0;
	}
}
