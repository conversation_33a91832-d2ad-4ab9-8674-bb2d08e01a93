<template>
	<view v-if="field" class="dynamic-group-card">
		<!-- <view class="dynamic-group-title">{{ field.label }}</view> -->
		<!-- 空组占位 -->
		<template v-if="!value || !Array.isArray(value) || value.length === 0">
			<view class="dynamic-group-wrapper">
				<view class="dynamic-group-content">
					<block v-for="(innerField, innerIndex) in field.fields" :key="innerIndex">
						<view :class="['dynamic-group-item', innerField.tag === 'textarea' ? 'vertical' : '']">
							<label class="dynamic-label">
								{{ innerField.label }}<text v-if="innerField.required" class="required">*</text>
							</label>
							<view class="dynamic-picker">请先添加{{ field.label }}</view>
						</view>
					</block>
				</view>
			</view>
		</template>
		<!-- 有组渲染 -->
		<template v-else>
			<view v-for="(groupItem, groupIndex) in (value || [])" :key="`${field.id}-${groupIndex}`"
				class="dynamic-group-wrapper">
				<view class="dynamic-group-header" v-if="value.length > 1">
					<text class="group-index-text">{{ field.label }} {{ groupIndex + 1 }}</text>
					<text v-if="groupIndex > 0 && !isReadonly" class="delete-group-btn" @click="removeGroupItem(groupIndex)">删除</text>
				</view>
				<view class="dynamic-group-content">
					<block v-for="(innerField, innerIndex) in field.fields" :key="innerIndex">
						<view :class="['dynamic-group-item', innerField.tag === 'textarea' ? 'vertical' : '']"
							v-if="isGroupFieldVisible(innerField, groupItem)">
							<label class="dynamic-label">
								{{ innerField.label }}<text v-if="innerField.required" class="required">*</text>
							</label>

							<!-- Input -->
							<input v-if="innerField.tag === 'input'" class="dynamic-input"
								:placeholder="'请输入' + innerField.label"
								:value="getDynamicGroupValue(groupIndex, innerField)"
								:disabled="isReadonly"
								@input="handleDynamicGroupInput($event, groupIndex, innerField)" />
							<!-- Textarea -->
							<textarea v-if="innerField.tag === 'textarea'" class="dynamic-textarea"
								:placeholder="'请输入' + innerField.label"
								:value="getDynamicGroupValue(groupIndex, innerField)"
								:disabled="isReadonly"
								@input="handleDynamicGroupInput($event, groupIndex, innerField)" auto-height />
							<!-- Select -->
							<picker v-if="innerField.tag === 'select'" mode="selector" :range="innerField.options || []"
								range-key="text" :value="getGroupPickerIndex(groupItem, innerField)"
								:disabled="isReadonly"
								@change="handleArrayGroupPickerChange($event, groupIndex, innerField)">
								<view class="dynamic-picker">
									{{ getGroupPickerText(groupItem, innerField) || ('请选择' + innerField.label) }}
								</view>
							</picker>
							<!-- Radio -->
							<view v-if="innerField.tag === 'radio'" class="dynamic-radio-container">
								<switch v-if="innerField.options.length === 2"
									:checked="groupItem[innerField.id] === innerField.options.value"
									:disabled="isReadonly"
									@change="handleArrayGroupSwitchChange($event, groupIndex, innerField)"
									color="#00984a" />
								<view v-else class="option-button-group">
									<button v-for="option in innerField.options" :key="option.value"
										:class="['option-button', { 'active': groupItem[innerField.id] === option.value }]"
										:disabled="isReadonly"
										@click="handleArrayGroupRadioAsButton(groupIndex, innerField, option)">
										{{ option.text }}
									</button>
								</view>
							</view>
							<!-- Date -->
							<picker v-if="innerField.tag === 'date'" mode="date" :value="groupItem[innerField.id]"
								:disabled="isReadonly"
								@change="handleArrayGroupDateChange($event, groupIndex, innerField)">
								<view class="dynamic-picker">
									{{ groupItem[innerField.id] || '请选择日期' }}
								</view>
							</picker>
							<!-- Upload -->
							<view v-if="innerField.tag === 'upload'" class="dynamic-file-list">
								<view v-if="!isReadonly" class="dynamic-file-upload" @click="uploadFileInGroup(groupIndex, innerField)">
									<text class="upload-icon">+</text>
									<text class="upload-text">上传</text>
								</view>
								<view v-for="(file, fileIndex) in (getGroupFiles(groupItem, innerField) || [])" :key="fileIndex"
									class="dynamic-file-item">
									<!-- <text class="file-name-text">{{ file.name }}</text>
									<text v-if="!isReadonly" class="delete-btn"
										@click.stop="removeFileInGroup(groupIndex, innerField, fileIndex)">×</text>
										 -->
									<image v-if="isImageFile(file.url)" :src="baseUrl+file.url" mode="aspectFill" @click="previewImage(groupIndex, innerField.id, file)"></image>
									<view v-else class="file-name" @click="openFile(baseUrl+file.url)">
										<text>{{getFileName(file.url)}}</text>
									</view>
									<text v-if="!isReadonly" class="delete-btn" @click.stop="removeFileInGroup(groupIndex, innerField, fileIndex)">×</text>
								</view>
							</view>
						</view>
					</block>
				</view>
			</view>
		</template>
		<!-- Add button for dynamic groups -->
		<view v-if="!isReadonly" class="add-group-btn" @click="addGroupItem">
			<text class="add-icon">+</text>
			<text class="add-text">添加{{ field.label }}</text>
		</view>
	</view>
</template>

<script>
	export default {
		name: "DynamicFormGroup",
		props: {
			field: {
				type: Object,
				required: true
			},
			value: {
				type: Array,
				default: () => []
			},
			formData: {
				type: Object,
				default: () => ({})
			},
			baseUrl: {
				type: String,
				default: ''
			},
			isReadonly: {
				type: Boolean,
				default: false
			}
		},
		methods: {
			updateValue(newValue) {
				this.$emit('input', newValue);
			},

			getGroupPickerText(groupItem, innerField) {
				if (!groupItem || typeof groupItem !== 'object') {
					return '';
				}
				const raw = groupItem[innerField.id];
				let selectedValue = '';
				let selectedText = '';

				if (raw != null && typeof raw === 'object') {
					// 支持历史草稿把整个对象存入的情况，尽量按映射键提取
					const valueKeys = [innerField.remoteOptionValue, 'value', 'id', 'F_Id', 'Code', 'code', innerField.id].filter(Boolean);
					const textKeys  = [innerField.remoteOptionText, 'text', 'name', 'label', 'F_FullName', 'Title', 'title'].filter(Boolean);
					for (const k of valueKeys) {
						if (raw && raw[k] != null && raw[k] !== '') { selectedValue = String(raw[k]); break; }
					}
					for (const k of textKeys) {
						if (raw && raw[k] != null && raw[k] !== '') { selectedText = String(raw[k]); break; }
					}
				} else {
					selectedValue = raw != null ? String(raw) : '';
				}

				if (!selectedValue && !selectedText) {
					return '';
				}

				if (!innerField.options || innerField.options.length === 0) {
					// 选项未加载，用已有文本或值占位
					return selectedText || selectedValue;
				}

				// 优先按 value 匹配，失败则按 text 兜底（兼容字典/历史草稿按文本存值）
				let selectedOption = null;
				if (selectedValue) {
					selectedOption = innerField.options.find(opt => String(opt.value) === String(selectedValue));
				}
				if (!selectedOption && selectedText) {
					selectedOption = innerField.options.find(opt => String(opt.text) === String(selectedText));
				}

				const result = selectedOption ? selectedOption.text : (selectedText || selectedValue);
				return result;
			},

			getGroupPickerIndex(groupItem, innerField) {
				if (!groupItem || typeof groupItem !== 'object') {
					return 0;
				}
				const raw = groupItem[innerField.id];
				let selectedValue = '';
				let selectedText = '';

				if (raw != null && typeof raw === 'object') {
					const valueKeys = [innerField.remoteOptionValue, 'value', 'id', 'F_Id', 'Code', 'code', innerField.id].filter(Boolean);
					const textKeys  = [innerField.remoteOptionText, 'text', 'name', 'label', 'F_FullName', 'Title', 'title'].filter(Boolean);
					for (const k of valueKeys) {
						if (raw && raw[k] != null && raw[k] !== '') { selectedValue = String(raw[k]); break; }
					}
					for (const k of textKeys) {
						if (raw && raw[k] != null && raw[k] !== '') { selectedText = String(raw[k]); break; }
					}
				} else {
					selectedValue = raw != null ? String(raw) : '';
				}

				if ((!selectedValue && !selectedText) || !innerField.options || innerField.options.length === 0) {
					return 0;
				}

				let index = -1;
				if (selectedValue) {
					index = innerField.options.findIndex(opt => String(opt.value) === String(selectedValue));
				}
				if (index < 0 && selectedText) {
					index = innerField.options.findIndex(opt => String(opt.text) === String(selectedText));
				}

				return index >= 0 ? index : 0;
			},

			evaluateExpression(expression, context) {
				if (!expression || typeof expression !== 'string') {
					return true;
				}
				const parts = expression.match(/(.+?)\s*([=!]=)\s*(.+)/);
				if (!parts) {
					console.warn(`Unsupported expression format: ${expression}`);
					return false;
				}
				const [, key, operator, valueStr] = parts;
				const keyInContext = key.trim();
				const expectedValue = valueStr.trim().replace(/^['"]|['"]$/g, '').replace('"', '');
				const actualValue = context[keyInContext];

				if (operator === '==') {
					return String(actualValue) === String(expectedValue);
				} else if (operator === '!=') {
					return String(actualValue) !== String(expectedValue);
				}
				return false;
			},

			isGroupFieldVisible(innerField, groupItem) {
				if (!innerField.expression) {
					return true;
				}
				const context = {
					...this.formData,
					...groupItem
				};
				return this.evaluateExpression(innerField.expression, context);
			},

			getDynamicGroupValue(groupIndex, innerField) {
				if (!this.value || !this.value[groupIndex]) {
					return '';
				}
				return this.value[groupIndex][innerField.id] || '';
			},

			handleDynamicGroupInput(e, groupIndex, innerField) {
				const inputValue = e.detail.value;
				const newGroupItems = [...this.value];
				if (newGroupItems[groupIndex]) {
					const newGroupItem = {
						...newGroupItems[groupIndex],
						[innerField.id]: inputValue
					};
					newGroupItems[groupIndex] = newGroupItem;
					this.updateValue(newGroupItems);
				}
			},

			handleArrayGroupPickerChange(e, groupIndex, innerField) {
				const selectedIndex = parseInt(e.detail.value);
				if (innerField.options && innerField.options[selectedIndex]) {
					const selectedValue = String(innerField.options[selectedIndex].value);
					const selectedText = innerField.options[selectedIndex].text;
					const newGroupItems = [...this.value];
					if (newGroupItems[groupIndex]) {
						const newGroupItem = {
							...newGroupItems[groupIndex],
							[innerField.id]: selectedValue,
							[innerField.id + "_ShowText"]: selectedText
						};
						newGroupItems[groupIndex] = newGroupItem;
						this.updateValue(newGroupItems);
					}
				}
			},

			handleArrayGroupSwitchChange(e, groupIndex, innerField) {
				const isChecked = e.detail.value;
				if (innerField.options && innerField.options.length >= 2) {
					const targetValue = isChecked ? innerField.options[0].value : innerField.options[1].value;
					const newGroupItems = [...this.value];
					if (newGroupItems[groupIndex]) {
						const newGroupItem = {
							...newGroupItems[groupIndex],
							[innerField.id]: targetValue
						};
						newGroupItems[groupIndex] = newGroupItem;
						this.updateValue(newGroupItems);
					}
				}
			},

			handleArrayGroupRadioAsButton(groupIndex, innerField, option) {
				const newGroupItems = [...this.value];
				if (newGroupItems[groupIndex]) {
					const newGroupItem = {
						...newGroupItems[groupIndex],
						[innerField.id]: option.value
					};
					newGroupItems[groupIndex] = newGroupItem;
					this.updateValue(newGroupItems);
				}
			},

			handleArrayGroupDateChange(e, groupIndex, innerField) {
				const newGroupItems = [...this.value];
				if (newGroupItems[groupIndex]) {
					const newGroupItem = {
						...newGroupItems[groupIndex],
						[innerField.id]: e.detail.value
					};
					newGroupItems[groupIndex] = newGroupItem;
					this.updateValue(newGroupItems);
				}
			},
			isImageFile(file) {
				return file.toLowerCase().endsWith('.jpg') || 
					   file.toLowerCase().endsWith('.jpeg') || 
					   file.toLowerCase().endsWith('.png') || 
					   file.toLowerCase().endsWith('.gif');
			},
			getFileName(file) {
				return file.split('/').pop().split('.').slice(0, -1).join('.');
			},
			openFile(file) {
				uni.downloadFile({
					url: file,
					success: (res) => {
						if (res.statusCode === 200) {
							uni.openDocument({
								filePath: res.tempFilePath,
								fileType: 'file',
								success: () => {
									console.log('文件打开成功')
								},
								fail: (err) => {
									console.error('打开文件失败', err)
								}
							})
						}
					},
					fail: (err) => {
						console.error('下载文件失败', err)
					}
				})
			},
			getGroupFiles(groupItem, innerField) {
				if (!groupItem || !innerField || !innerField.id) {
					return [];
				}
				const files = groupItem[innerField.id];
				return Array.isArray(files) ? files : [];
			},

			previewImage(groupIndex, fieldId, file) {
				console.log('previewImage::==>', groupIndex, fieldId, file);

				// 确保value存在且groupIndex有效
				if (!this.value || !Array.isArray(this.value) || !this.value[groupIndex]) {
					console.error('Invalid group data for previewImage');
					return;
				}

				const groupItem = this.value[groupIndex];
				const files = groupItem[fieldId];

				// 确保files存在且为数组
				if (!files || !Array.isArray(files)) {
					console.error('Invalid files data for previewImage');
					return;
				}

				// 获取对应类型的文件列表
				let imageList = [];
				files.forEach(f => {
					if (this.isImageFile(f.url)) {
						imageList.push(this.baseUrl+f.url);
					}
				});

				// 找到当前图片在列表中的索引
				const currentIndex = imageList.indexOf(file.url);

				// 预览图片
				uni.previewImage({
					urls: imageList,
					current: currentIndex
				});
			},
			async uploadFileInGroup(groupIndex, innerField) {
				try {
					const res = await uni.chooseImage({
						count: 1
					}); // 或 uni.chooseFile
					const filePath = res[1].tempFilePaths[0];
					// const fileName = res[1].tempFiles[0].name || `file_${Date.now()}`;

					uni.showLoading({
						title: '上传中...'
					});

					const uploadRes = await this.$minApi.uploadFile({
						filePath: filePath,
						name: 'file',
						fileby: 'approval'
					});

					uni.hideLoading();

					if (uploadRes.code === 200) {
						// 只保存相对路径，不包含baseUrl前缀
						const relativePath = uploadRes.data;
						const fileData = {
							name: '文件',
							url: relativePath, // 用于前端显示
							relativePath: relativePath, // 用于后端提交
							type: 'image'
						};

						const newGroupItems = [...this.value];
						if (newGroupItems[groupIndex]) {
							const groupItem = newGroupItems[groupIndex];
							if (!groupItem[innerField.id]) {
								this.$set(groupItem, innerField.id, []);
							}
							groupItem[innerField.id].push(fileData);
							this.updateValue(newGroupItems);
						}
					} else {
						uni.showToast({
							title: uploadRes.msg || '上传失败',
							icon: 'none'
						});
					}
				} catch (error) {
					uni.hideLoading();
					console.error("uploadFileInGroup error:", error);
					uni.showToast({
						title: '上传失败',
						icon: 'none'
					});
				}
			},

			removeFileInGroup(groupIndex, innerField, fileIndex) {
				const newGroupItems = [...this.value];
				if (newGroupItems[groupIndex] && newGroupItems[groupIndex][innerField.id]) {
					newGroupItems[groupIndex][innerField.id].splice(fileIndex, 1);
					this.updateValue(newGroupItems);
				}
			},

			addGroupItem() {
				const newItem = {
					"id": new Date().getTime()
				};
				if (this.field.fields && Array.isArray(this.field.fields)) {
					this.field.fields.forEach(innerField => {
						if (innerField.tag === 'radio' && innerField.options && innerField.options.length === 2) {
							this.$set(newItem, innerField.id, innerField.defaultValue || innerField.options[1]
								.value);
						} else if (innerField.tag === 'upload') {
							this.$set(newItem, innerField.id, []);
						} else {
							this.$set(newItem, innerField.id, innerField.defaultValue || null);
						}
					});
				}
				const currentItems = Array.isArray(this.value) ? this.value : [];
				this.updateValue([...currentItems, newItem]);
			},

			removeGroupItem(index) {
				const currentItems = this.value;
				if (Array.isArray(currentItems) && currentItems.length > 1 && index > 0) {
					const newItems = currentItems.filter((_, i) => i !== index);
					this.updateValue(newItems);
				}
			},

			validate() {
					console.log('validate dynamic-form-group:', this.field.label);
	
					// 检查是否有字段配置
					if (!this.field || !this.field.fields || !Array.isArray(this.field.fields)) {
						console.log('No fields to validate');
						return true;
					}
	
					// 检查是否有数据
					if (!this.value || !Array.isArray(this.value) || this.value.length === 0) {
						console.log('No data to validate');
						return true;
					}
	
					// 遍历每个组项进行验证
					for (let groupIndex = 0; groupIndex < this.value.length; groupIndex++) {
						const groupItem = this.value[groupIndex];
						if (!groupItem || typeof groupItem !== 'object') {
							continue;
						}
	
						// 遍历字段配置，检查必填字段
						for (const innerField of this.field.fields) {
							// 检查字段是否可见
							if (!this.isGroupFieldVisible(innerField, groupItem)) {
								continue;
							}
	
							// 检查是否为必填字段
							if (innerField.required) {
								const fieldValue = groupItem[innerField.id];
								const isEmpty = fieldValue === null ||
												fieldValue === undefined ||
												fieldValue === '' ||
												(Array.isArray(fieldValue) && fieldValue.length === 0);
	
								if (isEmpty) {
									const groupLabel = this.field.label || '表单组';
									const groupSuffix = this.value.length > 1 ? ` ${groupIndex + 1}` : '';
									const errorMessage = `请填写[${groupLabel}${groupSuffix}]中的[${innerField.label}]`;
	
									console.log('Validation failed:', errorMessage);
									uni.showToast({
										title: errorMessage,
										icon: 'none'
									});
									return false;
								}
							}
						}
					}
	
					console.log('Validation passed for dynamic-form-group');
					return true;
			}
		}
	}
	</script>

<style lang="scss" scoped>
	.dynamic-group-card {
		width: 95vw;
		background-color: #ffffff;
		border-radius: 16rpx;
		padding: 0;
		margin-top: 20rpx;
		border: 1rpx solid #f0f0f0;
		overflow: hidden;
		box-sizing: border-box;

		.dynamic-group-title {
			color: #333;
			font-weight: bold;
			font-size: 30rpx;
			padding: 20rpx;
			background-color: #fafafa;
			border-bottom: 1rpx solid #f0f0f0;
		}

		.dynamic-group-wrapper {
			border-bottom: 2rpx solid #f0f0f0;

			&:last-of-type {
				border-bottom: none;
			}
		}

		.dynamic-group-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 15rpx 20rpx;
			background-color: #f8f8f8;

			.group-index-text {
				font-size: 26rpx;
				color: #666;
				font-weight: 500;
			}

			.delete-group-btn {
				color: #ff4444;
				font-size: 26rpx;
				padding: 5rpx 15rpx;
			}
		}

		.dynamic-group-content {
			padding: 0 20rpx;

			.required {
				color: #ff0000;
				margin-left: 4rpx;
			}

			.dynamic-group-item {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 20rpx 0;
				border-bottom: 1rpx solid #f0f0f0;

				&:last-child {
					border-bottom: none;
				}

				&.vertical {
					flex-direction: column;
					align-items: flex-start;

					.dynamic-label {
						margin-bottom: 10rpx;
					}

					.dynamic-textarea {
						width: 100%;
					}
				}
			}

			.dynamic-label {
				color: #666666;
				font-size: 28rpx;
				white-space: nowrap;
				margin-right: 20rpx;
				min-width: 120rpx;
			}

			.dynamic-input,
			.dynamic-picker {
				flex: 1;
				padding: 10rpx;
				font-size: 28rpx;
				color: #333;
				text-align: right;

				&::placeholder {
					color: #999;
				}
			}

			.dynamic-textarea {
				flex: 1;
				padding: 10rpx;
				font-size: 28rpx;
				color: #333;
				min-height: 80rpx;

				&::placeholder {
					color: #999;
				}
			}

			.dynamic-picker {
				text-align: right;
			}

			.dynamic-radio-container {
				flex: 1;
				display: flex;
				justify-content: flex-end;

				.option-button-group {
					display: flex;
					flex-wrap: wrap;
					gap: 10rpx;
					justify-content: flex-end;
				}
			}

			.dynamic-file-list {
				flex: 1;
				display: flex;
				flex-wrap: wrap;
				gap: 15rpx;
				justify-content: flex-end;

				.dynamic-file-upload {
					width: 120rpx;
					height: 120rpx;
					border: 2rpx dashed #ddd;
					border-radius: 8rpx;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					background-color: #f8f8f8;

					.upload-icon {
						font-size: 36rpx;
						color: #999;
					}

					.upload-text {
						font-size: 20rpx;
						color: #999;
						margin-top: 5rpx;
					}
				}

				.dynamic-file-item {
					position: relative;
					width: 120rpx;
					height: 120rpx;
					background-color: #f5f5f5;
					border-radius: 8rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					padding: 8rpx;

					.file-name-text {
						font-size: 20rpx;
						color: #666666;
						text-align: center;
						word-break: break-all;
						line-height: 1.2;
					}
					image{
						width: 100%;
						height: 100%;
						border-radius: 8rpx;
						object-fit: cover;
					}

					.delete-btn {
						position: absolute;
						top: -10rpx;
						right: -10rpx;
						width: 24rpx;
						height: 24rpx;
						background-color: rgba(0, 0, 0, 0.5);
						color: #fff;
						border-radius: 50%;
						display: flex;
						align-items: center;
						justify-content: center;
						font-size: 18rpx;
					}
				}
			}
		}

		.add-group-btn {
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 20rpx;
			margin: 20rpx;
			background-color: #f8f8f8;
			border: 2rpx dashed #00984a;
			border-radius: 10rpx;

			.add-icon {
				font-size: 32rpx;
				color: #00984a;
				margin-right: 10rpx;
			}

			.add-text {
				font-size: 28rpx;
				color: #00984a;
			}
		}
	}

	.option-button {
		background-color: #F6F7FC;
		color: #23262A;
		border: none;
		padding: 6rpx 20rpx;
		height: 50rpx;
		line-height: 38rpx;
		font-size: 28rpx;
		border-radius: 10rpx;
		margin: 0;
		transition: all 0.2s ease;

		&.active {
			background-color: #00984a;
			color: #FFFFFF;
		}
	}
</style>