<template>
	<view class="approver-selector-container">
		<!-- 审批流程标题 -->
		<!-- <view class="flow-title">审批流程</view> -->
		
		<!-- 审批节点卡片列表 -->
		<view v-for="(node, nodeIndex) in nodes" :key="node.id" class="node-card">
			<!-- 节点头部 -->
			<view class="node-header">
				<text class="node-name">{{ node.name }}</text>
				<text v-if="node.counterSign" class="counter-sign-tag">会签</text>
			</view>
			
			<!-- 审批类型列表 -->
			<view class="approval-types">
				<view v-for="(item, itemIndex) in node.approvalItems" :key="itemIndex" class="approval-type-row">
					<!-- 左侧：类型名称和选择状态 -->
					<view class="type-info">
						<image class="type-icon" src="/static/img/profile.svg" />
						<view class="type-content">
							<text class="type-name">
								<text class="required-mark" v-if="needsManualSelection(item)">*</text>
								{{ item.typeName }}
							</text>
							<text v-if="item.selectedUser" class="selected-info">
								{{ item.selectedUser.name }}
							</text>
							<text v-else class="placeholder-text">
								{{ item.description || '请选择审批人' }}
							</text>
						</view>
					</view>
					
					<!-- 右侧：操作按钮或头像 -->
					<view class="type-action">
						<view v-if="item.selectedUser" class="user-avatar-wrapper">
							<image class="user-avatar"
								:src="item.selectedUser.avatar || '/static/img/profile.svg'"
								@click="chooseUser(nodeIndex, itemIndex)" />
							<view class="delete-btn" @click.stop="removeUser(nodeIndex, itemIndex)">
								<text class="delete-icon">×</text>
							</view>
						</view>
						<view v-else class="add-btn"
							@click="chooseUser(nodeIndex, itemIndex)">
							<text class="add-icon">+</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 如果没有审批流程 -->
		<view v-if="!nodes || nodes.length === 0" class="empty-flow">
			<text class="empty-text">暂无审批流程</text>
		</view>
		
		<!-- 人员选择弹窗 -->
		<uni-popup ref="userSelectorPopup" type="bottom" :safe-area="false">
			<view class="user-selector-popup">
				<view class="popup-header">
					<text class="popup-title">{{ currentSelectTitle }}</text>
					<text class="popup-close" @click="closeUserSelector">取消</text>
				</view>
				
				<scroll-view class="user-list" scroll-y>
					<view v-if="loadingUsers" class="loading-container">
						<text class="loading-text">加载中...</text>
					</view>
					
					<view v-else-if="availableUsers.length === 0" class="empty-container">
						<text class="empty-text">暂无可选人员</text>
					</view>
					
					<view v-else>
						<view v-for="(user, index) in availableUsers" 
							:key="index" 
							class="user-item"
							@click="selectUser(user)">
							<image class="user-item-avatar" 
								:src="user.avatar || user.F_HeadIcon || '/static/img/profile.svg'" />
							<view class="user-item-info">
								<text class="user-item-name">{{ user.name || user.F_RealName }}</text>
								<text v-if="user.department || user.F_DepartmentName" class="user-item-dept">
									{{ user.department || user.F_DepartmentName }}
								</text>
							</view>
							<view v-if="isUserSelected(user)" class="user-item-check">
								<text class="check-icon">✓</text>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
import api from '@/api/api.js'

export default {
	name: 'ApproverSelector',
	props: {
		schemeContent: {
			type: Object,
			default: () => ({})
		},
		value: { // for v-model
			type: Object,
			default: () => ({})
		}
	},
	data() {
		return {
			nodes: [],
			// 人员选择相关
			currentSelectTitle: '选择审批人',
			currentNodeIndex: -1,
			currentItemIndex: -1,
			availableUsers: [],
			loadingUsers: false
		};
	},
	watch: {
		schemeContent: {
			handler(newVal) {
				if (newVal && newVal.nodes) {
					this.parseScheme(newVal);
				}
			},
			immediate: true,
			deep: true
		},
		value: {
			handler(newVal) {
				this.updateSelectedUsers(newVal);
			},
			immediate: true,
			deep: true
		}
	},
	methods: {
		parseScheme(scheme) {
			console.log('[ApproverSelector] 开始解析流程方案');
			// console.log('[ApproverSelector] 原始 scheme:', JSON.stringify(scheme, null, 2));
			
			const parsedNodes = scheme.nodes
				.filter(n => n.type === 'node' && n.name !== '校验')
				.map(node => {
					const approvalItems = [];
					const setInfo = node.setInfo || {};
					
					console.log(`[ApproverSelector] 处理节点 ${node.name}`);
					console.log(`[ApproverSelector] setInfo:`, JSON.stringify(setInfo, null, 2));

					// 检查是否有多类型配置 - 注意检查多种可能的数据结构
					let hasMultiType = false;
					
					// 检查 MultiTypeConfig.approvalTypes
					if (setInfo.MultiTypeConfig) {
						console.log('[ApproverSelector] 发现 MultiTypeConfig:', setInfo.MultiTypeConfig);
						
						if (setInfo.MultiTypeConfig.approvalTypes &&
							Array.isArray(setInfo.MultiTypeConfig.approvalTypes) &&
							setInfo.MultiTypeConfig.approvalTypes.length > 0) {
							hasMultiType = true;
							console.log('[ApproverSelector] 使用 MultiTypeConfig.approvalTypes');
							
							setInfo.MultiTypeConfig.approvalTypes.forEach((type, index) => {
								console.log(`[ApproverSelector] 处理审批类型 ${index}:`, type);
								const item = this.createApprovalItem(node, type, index);
								console.log(`[ApproverSelector] 创建的审批项:`, item);
								approvalItems.push(item);
							});
						}
						// 也检查直接在 MultiTypeConfig 下的数组
						else if (Array.isArray(setInfo.MultiTypeConfig) && setInfo.MultiTypeConfig.length > 0) {
							hasMultiType = true;
							console.log('[ApproverSelector] MultiTypeConfig 是数组');
							
							setInfo.MultiTypeConfig.forEach((type, index) => {
								console.log(`[ApproverSelector] 处理审批类型 ${index}:`, type);
								const item = this.createApprovalItem(node, type, index);
								approvalItems.push(item);
							});
						}
					}
					
					// 如果没有多类型配置，使用单一类型
					if (!hasMultiType) {
						console.log('[ApproverSelector] 单一类型审批');
						const item = this.createApprovalItem(node, null, 0);
						approvalItems.push(item);
					}

					const result = {
						id: node.id,
						name: node.name,
						counterSign: setInfo.counterSign || false,
						approvalItems: approvalItems
					};
					
					console.log(`[ApproverSelector] 节点 ${node.name} 解析结果:`, result);
					return result;
				});
				
			console.log('[ApproverSelector] 最终解析完成的所有节点:', parsedNodes);
			this.nodes = parsedNodes;
		},
		
		createApprovalItem(node, type, index) {
			console.log(`[ApproverSelector] createApprovalItem - node: ${node.name}, type:`, type, 'index:', index);
			
			const setInfo = node.setInfo || {};
			let permissionConfig = {};
			let typeName = '';
			let description = '';
			
			if (type) {
				// 多类型审批项
				// 检查各种可能的字段名
				typeName = type.typeName || type.name || type.TypeName || `审批类型${index + 1}`;
				console.log(`[ApproverSelector] 审批类型名称: ${typeName}`);
				
				// 获取权限数据 - 检查多种可能的字段
				const permissionData = type.permissionData || type.PermissionData || type.permission || {};
				
				permissionConfig = {
					permissionType: type.permissionType || type.PermissionType || type.type || 'RUNTIME_SPECIAL_USER',
					users: permissionData.users || permissionData.Users || [],
					roles: permissionData.roles || permissionData.Roles || [],
					orgs: permissionData.orgs || permissionData.Orgs || [],
					currentDepart: permissionData.currentDepart || permissionData.CurrentDepart || false,
					selfSelectConfig: type.selfSelectConfig || type.SelfSelectConfig,
					dept: permissionData.dept || permissionData.Dept,
					rangeType: permissionData.rangeType || permissionData.RangeType,
					rangeUsers: permissionData.rangeUsers || permissionData.RangeUsers || []
				};
				
				console.log(`[ApproverSelector] 权限配置:`, permissionConfig);
				
				// 设置描述 - 所有都需要选择
				if (permissionConfig.rangeUsers && permissionConfig.rangeUsers.length > 0) {
					description = `请从${permissionConfig.rangeUsers.length}位指定范围人员中选择`;
				} else {
					description = '请选择审批人';
				}
			} else {
				// 单一类型审批
				typeName = node.name;
				const nodeData = setInfo.NodeDesignateData || setInfo.nodeDesignateData || {};
				
				permissionConfig = {
					permissionType: setInfo.NodeDesignate || setInfo.nodeDesignate || 'RUNTIME_SPECIAL_USER',
					users: nodeData.users || nodeData.Users || [],
					roles: nodeData.roles || nodeData.Roles || [],
					orgs: nodeData.orgs || nodeData.Orgs || [],
					currentDepart: nodeData.currentDepart || nodeData.CurrentDepart || false
				};
				
				// 所有都需要选择审批人
				description = '请选择审批人';
			}
			
			const result = {
				id: type ? `${node.id}_${index}` : node.id,
				typeName: typeName,
				permissionConfig: permissionConfig,
				description: description,
				selectedUser: null,
				originalType: type
			};
			
			console.log(`[ApproverSelector] createApprovalItem 返回:`, result);
			return result;
		},
		
		// 判断是否需要手动选择 - 所有审批人都需要手动选择
		needsManualSelection(item) {
			// 所有审批项都需要手动选择
			return true;
		},
		
		// 选择用户 - 调用API获取可选人员列表
		async chooseUser(nodeIndex, itemIndex) {
			const item = this.nodes[nodeIndex].approvalItems[itemIndex];
			
			// 设置当前选择的信息
			this.currentNodeIndex = nodeIndex;
			this.currentItemIndex = itemIndex;
			this.currentSelectTitle = `选择${item.typeName}`;
			
			// 显示弹窗
			this.$refs.userSelectorPopup.open();
			
			// 加载可选人员
			this.loadingUsers = true;
			this.availableUsers = [];
			
			try {
				// 调用API获取可选人员列表
				console.log('[ApproverSelector] 请求可选人员，权限配置:', item.permissionConfig);
				
				// 正确传递参数格式
				const response = await api.apis.chooseApprover(item.permissionConfig);
				
				console.log('[ApproverSelector] 获取到可选人员:', response);
				
				if (response && response.state === 'success') {
					// 处理返回的数据 - data 是 JSON 字符串，需要解析
					let userList = [];
					
					if (typeof response.data === 'string') {
						try {
							userList = JSON.parse(response.data);
						} catch (e) {
							console.error('[ApproverSelector] 解析用户数据失败:', e);
							userList = [];
						}
					} else if (Array.isArray(response.data)) {
						userList = response.data;
					} else {
						console.warn('[ApproverSelector] 未知的数据格式:', response.data);
					}
					
					// 映射用户数据 - 使用 F_Id 作为用户ID
					this.availableUsers = userList.map(user => ({
						id: user.F_Id || user.F_UserId || user.id,
						name: user.F_RealName || user.name || '未知用户',
						avatar: user.F_HeadIcon || user.F_HeadImgUrl || '/static/img/profile.svg',
						department: user.F_OrganizeName || user.F_DepartmentName || ''
					}));
					
					console.log('[ApproverSelector] 处理后的用户列表:', this.availableUsers);
				} else {
					console.error('[ApproverSelector] 获取可选人员失败:', response);
					uni.showToast({
						title: response?.message || '获取人员列表失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('[ApproverSelector] 请求失败:', error);
				uni.showToast({
					title: '网络请求失败',
					icon: 'none'
				});
			} finally {
				this.loadingUsers = false;
			}
		},
		
		// 选择用户
		selectUser(user) {
			if (this.currentNodeIndex >= 0 && this.currentItemIndex >= 0) {
				// 更新选中的用户
				this.$set(this.nodes[this.currentNodeIndex].approvalItems[this.currentItemIndex], 'selectedUser', {
					id: user.id,
					name: user.name,
					avatar: user.avatar
				});
				
				// 发送更新事件
				this.emitUpdate();
				
				// 关闭弹窗
				this.closeUserSelector();
			}
		},
		
		// 删除已选择的用户
		removeUser(nodeIndex, itemIndex) {
			if (nodeIndex >= 0 && itemIndex >= 0) {
				// 清空选中的用户
				this.$set(this.nodes[nodeIndex].approvalItems[itemIndex], 'selectedUser', null);
				
				// 发送更新事件
				this.emitUpdate();
				
				console.log(`[ApproverSelector] 已删除 ${this.nodes[nodeIndex].approvalItems[itemIndex].typeName} 的审批人`);
			}
		},
		
		// 关闭用户选择器
		closeUserSelector() {
			this.$refs.userSelectorPopup.close();
			this.currentNodeIndex = -1;
			this.currentItemIndex = -1;
			this.availableUsers = [];
		},
		
		// 判断用户是否已选中
		isUserSelected(user) {
			if (this.currentNodeIndex >= 0 && this.currentItemIndex >= 0) {
				const item = this.nodes[this.currentNodeIndex].approvalItems[this.currentItemIndex];
				return item.selectedUser && item.selectedUser.id === user.id;
			}
			return false;
		},
		
		emitUpdate() {
			const valueToEmit = { nodes: [] };
			
			this.nodes.forEach(node => {
				const nodeInfo = {
					id: node.id,
					approvalTypes: {}
				};
				let hasSelection = false;
				
				node.approvalItems.forEach(item => {
					// 只有需要手动选择的项才需要有选中的用户
					if (this.needsManualSelection(item)) {
						if (item.selectedUser) {
							// 使用 typeName 作为 key
							nodeInfo.approvalTypes[item.typeName] = {
								selectedUserId: item.selectedUser.id,
								approverName: item.selectedUser.name,
								status: 'pending',
								approvalTime: null,
								comment: ''
							};
							hasSelection = true;
						}
					} else {
						// 不需要手动选择的项，可能需要记录但不需要用户信息
						// 根据业务需求决定是否需要记录
					}
				});
				
				if (hasSelection || node.approvalItems.some(item => !this.needsManualSelection(item))) {
					valueToEmit.nodes.push(nodeInfo);
				}
			});
			
			console.log('[ApproverSelector] 发送更新数据:', valueToEmit);
			this.$emit('input', valueToEmit);
			this.$emit('change', valueToEmit);
		},
		
		updateSelectedUsers(valueData) {
			if (!valueData || !valueData.nodes || !this.nodes || this.nodes.length === 0) return;
			
			console.log('[ApproverSelector] 更新已选择的用户:', valueData);
			
			valueData.nodes.forEach(nodeData => {
				const node = this.nodes.find(n => n.id === nodeData.id);
				if (node && nodeData.approvalTypes) {
					console.log(`[ApproverSelector] 处理节点 ${nodeData.id} 的审批类型:`, nodeData.approvalTypes);
					
					// 遍历所有审批项
					node.approvalItems.forEach(item => {
						// 根据 typeName 查找对应的审批数据
						const approvalData = nodeData.approvalTypes[item.typeName];
						
						if (approvalData && approvalData.selectedUserId) {
							console.log(`[ApproverSelector] 设置 ${item.typeName} 的选中用户:`, approvalData);
							this.$set(item, 'selectedUser', {
								id: approvalData.selectedUserId,
								name: approvalData.approverName || '未知用户',
								avatar: '/static/img/profile.svg'
							});
						} else {
							// 清空选中的用户
							this.$set(item, 'selectedUser', null);
						}
					});
				}
			});
		},
		
		validate() {
			// 验证所有需要选择的审批人是否已选择
			try {
				// 检查是否有审批流程
				if (!this.nodes || this.nodes.length === 0) {
					console.log('[ApproverSelector] 没有审批流程，验证通过');
					return { isValid: true };
				}

				// 遍历所有节点和审批项进行验证
				for (let nodeIndex = 0; nodeIndex < this.nodes.length; nodeIndex++) {
					const node = this.nodes[nodeIndex];

					// 检查节点是否有审批项
					if (!node.approvalItems || node.approvalItems.length === 0) {
						console.warn(`[ApproverSelector] 节点 ${node.name} 没有审批项，跳过验证`);
						continue;
					}

					// 验证该节点的所有审批项
					for (let itemIndex = 0; itemIndex < node.approvalItems.length; itemIndex++) {
						const item = node.approvalItems[itemIndex];

						// 检查审批项是否存在
						if (!item) {
							console.warn(`[ApproverSelector] 节点 ${node.name} 的第 ${itemIndex + 1} 个审批项不存在`);
							continue;
						}

						// 检查是否需要手动选择且未选择审批人
						if (this.needsManualSelection(item) && !item.selectedUser) {
							const errorMessage = `请为 [${node.name}] - [${item.typeName}] 选择审批人`;
							console.log(`[ApproverSelector] 验证失败:`, errorMessage);

							uni.showToast({
								title: errorMessage,
								icon: 'none',
								duration: 3000
							});

							return {
								isValid: false,
								message: errorMessage,
								nodeName: node.name,
								typeName: item.typeName,
								nodeIndex: nodeIndex,
								itemIndex: itemIndex
							};
						}
					}
				}

				console.log('[ApproverSelector] 所有审批人验证通过');
				return { isValid: true };

			} catch (error) {
				console.error('[ApproverSelector] 验证过程中发生错误:', error);
				uni.showToast({
					title: '验证失败，请重试',
					icon: 'none'
				});
				return {
					isValid: false,
					message: '验证失败，请重试',
					error: error.message
				};
			}
		},
		
		// 获取审批流程数据（供外部调用）
		getApprovalData() {
			const result = {};
			this.nodes.forEach(node => {
				node.approvalItems.forEach(item => {
					if (item.selectedUser) {
						const key = `${node.id}_${item.typeName}`;
						result[key] = {
							nodeId: node.id,
							nodeName: node.name,
							typeName: item.typeName,
							userId: item.selectedUser.id,
							userName: item.selectedUser.name
						};
					}
				});
			});
			return result;
		}
	}
}
</script>

<style lang="scss" scoped>
.approver-selector-container {
	padding: 2rpx;
	background-color: #f5f5f5;
	min-height: 200rpx;
}

// 流程标题
.flow-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 30rpx;
	padding-left: 10rpx;
}

// 节点卡片
.node-card {
	background: #ffffff;
	border-radius: 16rpx;
	margin-bottom: 24rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
	
	// 节点头部
	.node-header {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
		padding-bottom: 20rpx;
		border-bottom: 1rpx solid #f0f0f0;
		
		.node-name {
			font-size: 30rpx;
			font-weight: 500;
			color: #333;
			flex: 1;
		}
		
		.counter-sign-tag {
			font-size: 24rpx;
			color: #1890ff;
			padding: 4rpx 16rpx;
			background: rgba(24, 144, 255, 0.1);
			border-radius: 8rpx;
		}
	}
	
	// 审批类型列表
	.approval-types {
		.approval-type-row {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 20rpx 0;
			border-bottom: 1rpx solid #f8f8f8;
			
			&:last-child {
				border-bottom: none;
			}
			
			// 左侧信息
			.type-info {
				display: flex;
				align-items: center;
				flex: 1;
				
				.type-icon {
					width: 60rpx;
					height: 60rpx;
					margin-right: 20rpx;
					border-radius: 50%;
					background: #f0f0f0;
				}
				
				.type-content {
					display: flex;
					flex-direction: column;
					
					.type-name {
						font-size: 28rpx;
						color: #333;
						margin-bottom: 8rpx;
						
						.required-mark {
							color: #ff4d4f;
							margin-right: 4rpx;
						}
					}
					
					.selected-info {
						font-size: 26rpx;
						color: #666;
					}
					
					.placeholder-text {
						font-size: 26rpx;
						color: #999;
					}
					
					.auto-assign-text {
						font-size: 26rpx;
						color: #52c41a;
					}
				}
			}
			
			// 右侧操作
			.type-action {
				.user-avatar-wrapper {
					position: relative;
					display: inline-block;
					
					.user-avatar {
						width: 72rpx;
						height: 72rpx;
						border-radius: 50%;
						border: 2rpx solid #fff;
						box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
					}
					
					.delete-btn {
						position: absolute;
						top: -8rpx;
						right: -8rpx;
						width: 32rpx;
						height: 32rpx;
						border-radius: 50%;
						background: #ff4d4f;
						border: 2rpx solid #fff;
						display: flex;
						align-items: center;
						justify-content: center;
						box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
						z-index: 10;
						
						&:active {
							transform: scale(0.9);
						}
						
						.delete-icon {
							color: #fff;
							font-size: 24rpx;
							font-weight: bold;
							line-height: 1;
						}
					}
				}
				
				.add-btn {
					width: 72rpx;
					height: 72rpx;
					border-radius: 50%;
					border: 2rpx dashed #d9d9d9;
					display: flex;
					align-items: center;
					justify-content: center;
					background: #fafafa;
					transition: all 0.3s;
					
					&:active {
						background: #f0f0f0;
						transform: scale(0.95);
					}
					
					.add-icon {
						font-size: 36rpx;
						color: #999;
						font-weight: 300;
					}
				}
			}
		}
	}
}

// 空状态
.empty-flow {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 60rpx 0;
	
	.empty-text {
		font-size: 28rpx;
		color: #999;
	}
}

// 人员选择弹窗
.user-selector-popup {
	background: #fff;
	border-radius: 24rpx 24rpx 0 0;
	height: 70vh;
	display: flex;
	flex-direction: column;
	
	.popup-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
		
		.popup-title {
			font-size: 32rpx;
			font-weight: 500;
			color: #333;
		}
		
		.popup-close {
			font-size: 28rpx;
			color: #666;
			padding: 10rpx 20rpx;
		}
	}
	
	.user-list {
		flex: 1;
		padding: 0 30rpx;
		
		.loading-container,
		.empty-container {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 300rpx;
			
			.loading-text,
			.empty-text {
				font-size: 28rpx;
				color: #999;
			}
		}
		
		.user-item {
			display: flex;
			align-items: center;
			padding: 24rpx 0;
			border-bottom: 1rpx solid #f8f8f8;
			
			&:active {
				background: #f8f8f8;
			}
			
			.user-item-avatar {
				width: 80rpx;
				height: 80rpx;
				border-radius: 50%;
				margin-right: 24rpx;
				background: #f0f0f0;
			}
			
			.user-item-info {
				flex: 1;
				display: flex;
				flex-direction: column;
				
				.user-item-name {
					font-size: 30rpx;
					color: #333;
					margin-bottom: 8rpx;
				}
				
				.user-item-dept {
					font-size: 26rpx;
					color: #999;
				}
			}
			
			.user-item-check {
				width: 48rpx;
				height: 48rpx;
				border-radius: 50%;
				background: #1890ff;
				display: flex;
				align-items: center;
				justify-content: center;
				
				.check-icon {
					color: #fff;
					font-size: 28rpx;
				}
			}
		}
	}
}

// 深色模式适配
@media (prefers-color-scheme: dark) {
	.approver-selector-container {
		background-color: #1a1a1a;
		
		.flow-title {
			color: #e0e0e0;
		}
		
		.node-card {
			background: #2a2a2a;
			box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
			
			.node-header {
				border-bottom-color: #3a3a3a;
				
				.node-name {
					color: #e0e0e0;
				}
			}
			
			.approval-types {
				.approval-type-row {
					border-bottom-color: #3a3a3a;
					
					.type-info {
						.type-content {
							.type-name {
								color: #e0e0e0;
							}
							
							.selected-info {
								color: #b0b0b0;
							}
							
							.placeholder-text {
								color: #808080;
							}
							
							.auto-assign-text {
								color: #52c41a;
							}
						}
					}
					
					.type-action {
						.user-avatar-wrapper {
							.delete-btn {
								background: #ff4d4f;
								border-color: #3a3a3a;
							}
						}
						
						.add-btn {
							border-color: #4a4a4a;
							background: #2a2a2a;
							
							&:active {
								background: #3a3a3a;
							}
							
							.add-icon {
								color: #808080;
							}
						}
					}
				}
			}
		}
	}
	
	.user-selector-popup {
		background: #2a2a2a;
		
		.popup-header {
			border-bottom-color: #3a3a3a;
			
			.popup-title {
				color: #e0e0e0;
			}
			
			.popup-close {
				color: #b0b0b0;
			}
		}
		
		.user-list {
			.user-item {
				border-bottom-color: #3a3a3a;
				
				&:active {
					background: #3a3a3a;
				}
				
				.user-item-info {
					.user-item-name {
						color: #e0e0e0;
					}
					
					.user-item-dept {
						color: #808080;
					}
				}
			}
		}
	}
}
</style>