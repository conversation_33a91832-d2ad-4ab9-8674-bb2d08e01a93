<template>
  <view class="approval-tabs-container">
    <!-- Tab 切换区域 -->
    <view class="tabs-header">
      <scroll-view scroll-x="true" class="scroll-view">
        <view
          v-for="(tab, index) in tabs"
          :key="index"
          class="tab-item"
          :class="{ 'active': currentTab === index }"
          @click="switchTab(index)"
        >
          {{ tab.name }}
        </view>
      </scroll-view>
    </view>

    <!-- Tab 内容区域 -->
    <view class="tabs-content">
      <view v-for="(tab, index) in tabs" :key="index" v-show="currentTab === index" class="tab-panel">
        <view v-if="tab.setInfo && tab.setInfo.MultiTypeConfig" class="approval-types">
          <view
            v-for="(approvalType, typeIndex) in tab.setInfo.MultiTypeConfig.approvalTypes"
            :key="typeIndex"
            class="approval-type-item"
          >
            <view class="type-header">
              <text class="type-name">{{ approvalType.typeName }}</text>
              <text :class="['status', getStatusInfo(tab.id, approvalType.typeName).className]">{{ getStatusInfo(tab.id, approvalType.typeName).text }}</text>
            </view>
            <view class="approver-info">
              <text>审批人: {{ getApproverInfo(tab.id, approvalType.typeName, 'approverName') }}</text>
              <text v-if="getApproverInfo(tab.id, approvalType.typeName, 'approvalTime')">审批时间: {{ getApproverInfo(tab.id, approvalType.typeName, 'approvalTime') }}</text>
            </view>
            <view class="comment" v-if="getApproverInfo(tab.id, approvalType.typeName, 'comment')">
              审批意见: {{ getApproverInfo(tab.id, approvalType.typeName, 'comment') }}
            </view>
          </view>
        </view>
        <view v-else>
          <text>该节点无审批类型</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { formatDate } from '@/utils/datetime';

export default {
  props: {
    nodes: {
      type: Array,
      default: () => []
    },
    approvalData: {
      type: Object,
      default: null
    },
    activeId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      currentTab: 0
    };
  },
  computed: {
    tabs() {
      // 过滤掉开始和结束节点
      return this.nodes.filter(node => node.type === 'node');
    }
  },
  methods: {
    switchTab(index) {
      this.currentTab = index;
    },
    getStatusInfo(nodeId, approvalTypeName) {
      if (!this.approvalData) return { text: '未开始', className: 'status-pending' };

      const node = this.approvalData.nodes.find(n => n.id === nodeId);
      if (!node) return { text: '未开始', className: 'status-pending' };

      const approval = node.approvalTypes[approvalTypeName];
      if (!approval) return { text: '未开始', className: 'status-pending' };
      
      if (this.activeId !== nodeId && approval.status === 'pending') {
        return { text: '未开始', className: 'status-pending' };
      }

      switch (approval.status) {
        case 'approved': return { text: '已通过', className: 'status-pass' };
        case 'reject': return { text: '已拒绝', className: 'status-reject' };
        case 'pending': return { text: '未审批', className: 'status-pending' };
        default: return { text: '未开始', className: 'status-pending' };
      }
    },
    getApproverInfo(nodeId, approvalTypeName, field) {
      if (!this.approvalData) return '';
      const node = this.approvalData.nodes.find(n => n.id === nodeId);
      if (!node) return '';
      const approval = node.approvalTypes[approvalTypeName];
      if (!approval) return '';

      if (field === 'approvalTime' && approval[field]) {
        return formatDate(approval[field]);
      }
      return approval[field] || '';
    }
  }
};
</script>

<style scoped>
.approval-tabs-container {
  width: 100%;
}
.tabs-header {
  border-bottom: 1px solid #eee;
}
.scroll-view {
  white-space: nowrap;
}
.tab-item {
  display: inline-block;
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  color: #666;
}
.tab-item.active {
  color: #007aff;
  border-bottom: 2px solid #007aff;
}
.tabs-content {
  padding: 20rpx;
}
.approval-type-item {
  padding: 15rpx 0;
  border-bottom: 1px solid #f5f5f5;
}
.type-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}
.type-name {
  font-size: 30rpx;
  font-weight: bold;
}
.status {
  font-size: 26rpx;
  padding: 5rpx 15rpx;
  border-radius: 8rpx;
  color: #fff;
}
.status-pass { background-color: #18b566; }
.status-reject { background-color: #e43d33; }
.status-pending { background-color: #909399; }

.approver-info {
  font-size: 26rpx;
  color: #666;
  display: flex;
  justify-content: space-between;
}
.comment {
  font-size: 26rpx;
  color: #333;
  margin-top: 10rpx;
  padding: 10rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
}
</style>