<template>
	<view class="steps-wrapper">
		<!-- 背景灰色线条 -->
		<view class="background-line"></view>
		<!-- 高亮绿色线条 -->
		<view class="active-line" :style="{ width: activeLineWidth }"></view>

		<!-- 步骤节点 -->
		<view class="step-item" v-for="(item, index) in steps" :key="index">
			<view class="dot-wrapper">
				<view class="dot" :class="{ 'active': index <= currentStep }"></view>
			</view>
			<view class="label" :class="{ 'active': index <= currentStep }">{{ item.title }}</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "StepProgress",
		props: {
			// 所有步骤的定义数组
			steps: {
				type: Array,
				required: true,
				default: () => []
			},
			// 当前所在的步骤索引（从0开始）
			currentStep: {
				type: Number,
				required: true,
				default: 0
			}
		},
		computed: {
			// 计算高亮线条的宽度
			activeLineWidth() {
				const totalSegments = this.steps.length - 1;
				if (totalSegments <= 0) {
					return '0%';
				}
				const percentage = (this.currentStep / totalSegments) * 100;
				return `${percentage}%`;
			}
		}
	}
</script>

<style lang="scss" scoped>
	.steps-wrapper {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		position: relative;
		width: 100%;
	}

	.background-line,
	.active-line {
		position: absolute;
		top: 10rpx;
		left: 0;
		height: 2px;
		transform: translateY(-50%);
		z-index: 1;
	}

	.background-line {
		width: 100%;
		background-color: #e0e0e0;
	}

	.active-line {
		width: 0;
		background-color: #4CAF50;
		transition: width 0.4s ease;
	}

	.step-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		position: relative;
		z-index: 2;
		background-color: #FFFFFF; // 与页面背景色相同，用于遮挡线条
		padding: 0 5rpx;
	}

	.dot-wrapper {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 24rpx;
	}

	.dot {
		width: 24rpx;
		height: 24rpx;
		border-radius: 50%;
		background-color: #cccccc;
		transition: all 0.4s ease;
	}

	.dot.active {
		background-color: #4CAF50;
		width: 24rpx;
		height: 24rpx;
		box-shadow: 0 0 0 4rpx rgba(76, 175, 80, 0.2);
	}

	.label {
		margin-top: 10rpx;
		font-size: 24rpx;
		color: #4C5156;
		transition: color 0.4s ease;
		white-space: nowrap;
	}

	.label.active {
		color: #37994C;
	}
</style>