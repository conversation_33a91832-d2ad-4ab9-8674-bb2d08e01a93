# 接口定义文档

本文档根据前端页面功能，为后端开发提供接口定义参考。

## 模块一：用户管理 (Employee Management)


### 1.2 获取单个用户信息

- **功能**: 在编辑用户时，根据 ID 获取用户的详细信息。
- **HTTP 方法**: `GET`
- **URL**: `/api/user/{id}`
- **成功响应 (200 OK)**:
  ```json
  {
    "id": "string",
    "name": "string",
    "roleId": "string",
    "departmentId": "string",
    "phone": "string",
    "email": "string",
    "account": "string",
    "status": "boolean"
  }
  ```

### 1.3 新增用户

- **功能**: 创建一个新的用户。
- **HTTP 方法**: `POST`
- **URL**: `/api/user`
- **请求体 (Body)**:
  ```json
  {
    "name": "string",
    "roleId": "string",
    "departmentId": "string",
    "phone": "string",
    "email": "string",
    "account": "string",
    "password": "string",
    "status": "boolean"
  }
  ```
- **成功响应 (200 OK)**: `{ "code": 200, "message": "创建成功" }`

### 1.4 更新用户信息

- **功能**: 更新指定 ID 用户的信息。
- **HTTP 方法**: `PUT`
- **URL**: `/api/user/{id}`
- **请求体 (Body)**:
  ```json
  {
    "name": "string",
    "roleId": "string",
    "departmentId": "string",
    "phone": "string",
    "email": "string",
    "account": "string",
    "password": "string",
    "status": "boolean"
  }
  ```
- **成功响应 (200 OK)**: `{ "code": 200, "message": "更新成功" }`

### 1.5 删除用户

- **功能**: 删除指定 ID 的用户。
- **HTTP 方法**: `DELETE`
- **URL**: `/api/user/{id}`
- **成功响应 (200 OK)**: `{ "code": 200, "message": "删除成功" }`

---

## 模块二：个人中心

### 2.2 更新头像

- **功能**: 用户上传并更换自己的头像。
- **HTTP 方法**: `POST`
- **URL**: `/api/user/avatar`
- **请求体 (Body)**: `multipart/form-data` 格式的文件流。
- **成功响应 (200 OK)**:
  ```json
  {
    "url": "string"
  }
  ```

### 2.3 绑定邮箱

- **功能**: 用户为自己的账户绑定邮箱地址。
- **HTTP 方法**: `POST`
- **URL**: `/api/user/bind-email`
- **请求体 (Body)**:
  ```json
  {
    "email": "string"
  }
  ```
- **成功响应 (200 OK)**: `{ "code": 200, "message": "绑定成功" }`

### 2.4 重置密码

- **功能**: 用户修改自己的登录密码。
- **HTTP 方法**: `POST`
- **URL**: `/api/user/reset-password`
- **请求体 (Body)**:
  ```json
  {
    "oldPassword": "string",
    "newPassword": "string"
  }
  ```
- **成功响应 (200 OK)**: `{ "code": 200, "message": "密码重置成功" }`

### 2.5 验证密码

- **功能**: 在执行敏感操作（如重置密码、绑定邮箱）之前，验证用户当前密码是否正确。
- **HTTP 方法**: `POST`
- **URL**: `/api/my/verify-password`
- **请求体 (Body)**:
  ```json
  {
    "password": "string"
  }
  ```
- **成功响应 (200 OK)**: `{ "code": 200, "message": "验证成功" }`
- **失败响应 (400 Bad Request)**: `{ "code": 400, "message": "密码错误" }`