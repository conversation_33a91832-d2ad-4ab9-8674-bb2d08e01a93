<svg width="101" height="100" viewBox="0 0 101 100" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Group 6634">
<circle id="Ellipse 13" cx="50.75" cy="50" r="50" fill="#F6F7FC"/>
<g id="Group 6336">
<g id="Rectangle 2770">
<rect x="21.75" y="21" width="46.4" height="58" rx="10" fill="url(#paint0_linear_152_41)"/>
<rect x="21.75" y="21" width="46.4" height="58" rx="10" fill="url(#paint1_linear_152_41)"/>
</g>
<rect id="Rectangle 2771" x="27.55" y="29.3442" width="34.8" height="5.8" rx="2.9" fill="white"/>
<rect id="Rectangle 2773" x="27.55" y="40.543" width="22.1862" height="5.8" rx="2.9" fill="white"/>
<foreignObject x="48.55" y="47.7998" width="39.2" height="39.2002"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(4px);clip-path:url(#bgblur_0_152_41_clip_path);height:100%;width:100%"></div></foreignObject><rect id="Rectangle 2772" data-figma-bg-blur-radius="8" x="57.05" y="56.2998" width="22.2" height="22.2" rx="11.1" fill="white" fill-opacity="0.6" stroke="url(#paint2_linear_152_41)"/>
<path id="Vector 67" d="M64.6145 63.8643L71.6856 70.9353" stroke="#FD4648" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path id="Vector 68" d="M71.6855 63.8643L64.6145 70.9353" stroke="#FD4648" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
</g>
<defs>
<clipPath id="bgblur_0_152_41_clip_path" transform="translate(-48.55 -47.7998)"><rect x="57.05" y="56.2998" width="22.2" height="22.2" rx="11.1"/>
</clipPath><linearGradient id="paint0_linear_152_41" x1="44.95" y1="21" x2="44.95" y2="79" gradientUnits="userSpaceOnUse">
<stop stop-color="#4098FF"/>
<stop offset="1" stop-color="#0E7DFF"/>
</linearGradient>
<linearGradient id="paint1_linear_152_41" x1="44.95" y1="21" x2="44.95" y2="79" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF7D7E"/>
<stop offset="1" stop-color="#FD4648"/>
</linearGradient>
<linearGradient id="paint2_linear_152_41" x1="68.15" y1="55.7998" x2="68.15" y2="78.9998" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#FFBEBF"/>
</linearGradient>
</defs>
</svg>
