/**
 * ip地址或域名
 */
const ipAddress = 'http://localhost:52423/api'
// const ipAddress = 'http://***********:8012/api'
// const ipAddress = 'https://ehs.henantuanqi.cn/api'
// const ipAddress = 'https://nidecehs.com/api'
// 文件访问地址
// const fileAddr = 'http://localhost:8082/fileUpload/'
// const fileAddr = 'http://***********:8012/fileUpload/'
// const fileAddr = 'https://ehs.henantuanqi.cn/api/fileUpload/'
const fileAddr = 'https://nidecehs.com/api/fileUpload/'
/**
 * api前缀
 */
const apiPrefix = '/api'
/**
 * 针对不同平台的baseUrl
 */
const getBaseUrl = () => {
	// #ifdef H5
	return apiPrefix
	// #endif
	// #ifndef H5
	return ipAddress
	// #endif
}
export default {
	/**
	 * 针对不同平台的baseUrl
	 */
	baseUrl: getBaseUrl(),
	fileAddr
}
