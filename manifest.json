{
    "name" : "<PERSON><PERSON>",
    "appid" : "__UNI__97C2008",
    "description" : "",
    "versionName" : "2.0.4",
    "versionCode" : 204,
    "transformPx" : false,
    /* 5+App特有相关 */
    "app-plus" : {
        "usingComponents" : true,
        "nvueCompiler" : "uni-app",
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        /* 模块配置 */
        "modules" : {
            "Push" : {},
            "Webview-x5" : {}
        },
        "compatible" : {
            "ignoreVersion" : true
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ]
            },
            /* ios打包配置 */
            "ios" : {},
            /* SDK配置 */
            "sdkConfigs" : {
                "push" : {
                    "unipush" : {
                        "icons" : {
                            "push" : {
                                "ldpi" : "unpackage/res/icons/48x48.png",
                                "mdpi" : "unpackage/res/icons/48x48.png",
                                "hdpi" : "unpackage/res/icons/72x72.png",
                                "xhdpi" : "unpackage/res/icons/96x96.png",
                                "xxhdpi" : "unpackage/res/icons/144x144.png",
                                "xxxhdpi" : "unpackage/res/icons/192x192.png"
                            },
                            "small" : {
                                "ldpi" : "unpackage/res/icons/18x18.png",
                                "mdpi" : "unpackage/res/icons/24x24.png",
                                "hdpi" : "unpackage/res/icons/36x36.png",
                                "xhdpi" : "unpackage/res/icons/48x48.png",
                                "xxhdpi" : "unpackage/res/icons/72x72.png"
                            }
                        }
                    }
                },
                "ad" : {}
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            },
            "splashscreen" : {
                "android" : {
                    "hdpi" : "/Users/<USER>/work/code/design/uniapp-admin/hb.png",
                    "xhdpi" : "/Users/<USER>/work/code/design/uniapp-admin/hb.png",
                    "xxhdpi" : "/Users/<USER>/work/code/design/uniapp-admin/hb.png"
                },
                "ios" : {
                    "iphone" : {
                        "portrait-896h@3x" : "/Users/<USER>/work/code/design/uniapp-admin/hb.png",
                        "landscape-896h@3x" : "/Users/<USER>/work/code/design/uniapp-admin/hb.png",
                        "portrait-896h@2x" : "/Users/<USER>/work/code/design/uniapp-admin/hb.png",
                        "landscape-896h@2x" : "/Users/<USER>/work/code/design/uniapp-admin/hb.png",
                        "iphonex" : "/Users/<USER>/work/code/design/uniapp-admin/hb.png",
                        "iphonexl" : "/Users/<USER>/work/code/design/uniapp-admin/hb.png",
                        "retina55" : "/Users/<USER>/work/code/design/uniapp-admin/hb.png",
                        "retina55l" : "/Users/<USER>/work/code/design/uniapp-admin/hb.png",
                        "retina35" : "/Users/<USER>/work/code/design/uniapp-admin/hb.png",
                        "retina40l" : "/Users/<USER>/work/code/design/uniapp-admin/hb.png",
                        "retina40" : "/Users/<USER>/work/code/design/uniapp-admin/hb.png",
                        "retina47l" : "/Users/<USER>/work/code/design/uniapp-admin/hb.png",
                        "retina47" : "/Users/<USER>/work/code/design/uniapp-admin/hb.png"
                    },
                    "ipad" : {
                        "portrait-1366h@2x" : "/Users/<USER>/work/code/design/uniapp-admin/hb.png",
                        "landscape-1366h@2x" : "/Users/<USER>/work/code/design/uniapp-admin/hb.png",
                        "portrait-1194h@2" : "/Users/<USER>/work/code/design/uniapp-admin/hb.png",
                        "landscape-1194h@2x" : "/Users/<USER>/work/code/design/uniapp-admin/hb.png",
                        "portrait-1112h@2x" : "/Users/<USER>/work/code/design/uniapp-admin/hb.png",
                        "landscape-1112h@2x" : "/Users/<USER>/work/code/design/uniapp-admin/hb.png",
                        "portrait-retina7" : "/Users/<USER>/work/code/design/uniapp-admin/hb.png",
                        "landscape-retina7" : "/Users/<USER>/work/code/design/uniapp-admin/hb.png",
                        "portrait7" : "/Users/<USER>/work/code/design/uniapp-admin/hb.png",
                        "landscape7" : "/Users/<USER>/work/code/design/uniapp-admin/hb.png"
                    }
                }
            }
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "wx95086060734e1c48",
        "setting" : {
            "urlCheck" : false
        },
        "usingComponents" : true
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "h5" : {
        "devServer" : {
            "port" : 9090,
            "disableHostCheck" : true,
            "proxy" : {
                "/apiUA" : {
                    "target" : "http://**************:38080/app/mock/16",
                    "changeOrigin" : true,
                    "secure" : false,
                    "pathRewrite" : {
                        "^/apiUA" : ""
                    }
                }
            }
        },
        "title" : "uniapp-admin",
        "router" : {
            "mode" : "hash",
            "base" : "/ua"
        }
    },
    "vueVersion" : "2"
}
