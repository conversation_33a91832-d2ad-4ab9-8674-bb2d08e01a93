page, uni-page-body, #app {
	height: 100%!important;
}

.w-h-100 {
	height: 100%;
	width: 100%;
}

.uni-flex {
	display: flex;
}

.uni-row {
	flex-direction: row;
	justify-content: flex-start;
	align-items: flex-start;
	margin: 4rpx 0;
}

.uni-row .flex-item-20 {
	color: #8799a3;
}

.uni-row .flex-item-30 {
	color: #8799a3;
}

.flex-item-20 {
	flex-basis: 20%;
}

.flex-item-80 {
	flex-basis: 80%;
}

.flex-item-30 {
	flex-basis: 30%;
}

.flex-item-70 {
	flex-basis: 70%;
}

.my-iconfont {
	font-size: 24rpx;
}

.footer-box {
	display: flex;
	justify-content: space-between;
	width: 100%;
	padding: 0 10%;
}

.footer-box__item {
	width: 100%;
	display: flex;
	align-items: center;
}

.footer-box__item:nth-child(2) {
	justify-content: center;
}

.footer-box__item:last-child {
	justify-content: flex-end;
}

.audit-card-content {
	margin: 0 20rpx;
}

.mycard .card-item {
	margin-bottom: 20rpx;
}

.mycard {
	margin-top: 20rpx;
}

.goods-carts {
	width: 100%;
	position: fixed;
	bottom: 0;
}

/* 顶部搜索框 */
.input-view {
	width: 100%;
	display: flex;
	background-color: #e7e7e7;
	height: 30px;
	border-radius: 15px;
	padding: 0 4%;
	flex-wrap: nowrap;
	margin: 7px 10rpx;
	line-height: 30px;
	background: #f5f5f5;
}

.input-view .uni-icon {
	line-height: 30px !important;
}

.input-view .input {
	height: 30px;
	line-height: 30px;
	width: 94%;
	padding: 0 3%;
}

.my-tab-bar .cu-item {
	height: auto;
	margin: 0;
	padding: 0;
	line-height: 44px;
}

/* 底部分享 */
.uni-share {
	background: #fff;
}

.uni-share-padding-bottom {
	padding-bottom: 22%;
}

.idea-textarea {
	border: 1rpx solid #d9d9d9;
	width: 100%;
}

.uni-share-title {
	line-height: 60rpx;
	font-size: 24rpx;
	padding: 15rpx 0;
	text-align: center;
}

.uni-share-content {
	display: flex;
	flex-wrap: wrap;
	padding: 15px 15px 0 15px;
}

.uni-share-content-box {
	display: flex;
	flex-direction: column;
	align-items: center;
	width: 25%;
	box-sizing: border-box;
}

.uni-share-content-image {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 60rpx;
	height: 60rpx;
	overflow: hidden;
	border-radius: 10rpx;
}

.uni-share-content-image .image {
	width: 100%;
	height: 100%;
}

.uni-share-content-text {
	font-size: 26rpx;
	color: #333;
	padding-top: 5px;
	padding-bottom: 10px;
}

.uni-share-btn {
	height: 90rpx;
	line-height: 90rpx;
	border-top: 1px #f5f5f5 solid;
	text-align: center;
	color: #666;
}

.uni-timeline-item .uni-timeline-item-keynode {
	width: auto;
}

.uni-timeline-last-item .uni-timeline-item-divider {
	background-color: #bbb !important;
}

.uni-timeline-first-item .uni-timeline-item-divider {
	background-color: #1AAD19 !important;
}

.uni-card__header-extra-text {
	width: auto !important;
}

.scoll-y {
	height: 100%;
}

.tui-tabs-relative::before {
	border-bottom: none!important;
}
