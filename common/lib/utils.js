/**
 * 按钮权限检查
 * @param {String} buttonCode 按钮代码
 * @returns {Boolean} 是否有权限
 */
export function checkButtonPermission(buttonCode) {
    if (!buttonCode) return false;
    
    // 从vuex获取用户信息
    const userInfo = uni.getStorageSync('_userInfo');
    if (!userInfo) return false;
    
    const buttonAuthorize = userInfo.data.F_ButtonAuthorize;
    if (!buttonAuthorize) return false;
    
    return buttonAuthorize.split(',').includes(buttonCode);
} 

export function checkMenuPermission(menuCode) {
    if (!menuCode) return false;
    
    // 从vuex获取用户信息
    const userInfo = uni.getStorageSync('_userInfo');
    if (!userInfo) return false;
    
    const menuAuthorize = userInfo.data.F_MenuAuthorize;
    if (!menuAuthorize) return false;
    // console.log('menuCode',menuCode,'userInfo',userInfo.data.F_MenuAuthorize,menuAuthorize.split(',').includes(menuCode))
    return menuAuthorize.split(',').includes(menuCode);
} 