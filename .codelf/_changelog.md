## {datetime: 2024-12-19 15:45:00}

### 1. 添加微信手机号快速登录功能

**Change Type**: feature

> **Purpose**: 在登录页面添加微信手机号快速登录功能，提供更便捷的登录方式。
> **Detailed Description**: 为 `pages/login/login.vue` 页面添加了微信手机号快速登录功能。在 `store/modules/user.js` 中添加了 `wechatLogin` action 来处理微信登录逻辑。具体实现包括：在登录页面UI中添加微信登录按钮（使用条件编译确保只在微信小程序中显示），实现获取微信授权码(uni.login)、用户信息(uni.getUserProfile)、手机号(button open-type="getPhoneNumber")的完整流程，调用已有的 `/api/User/WechatLogin` 接口进行身份验证，添加了完善的错误处理和加载状态管理，修复了原有的v-on.native修饰符问题。
> **Reason for Change**: 用户需要通过微信手机号快速登录，提高用户体验和登录便捷性。
> **Impact Scope**: 影响 `pages/login/login.vue` 和 `store/modules/user.js` 文件，不影响其他模块。
> **API Changes**: 使用现有的 `/api/User/WechatLogin` 接口，无API变更。
> **Configuration Changes**: 无配置变更。
> **Performance Impact**: 添加了微信登录流程，对性能影响可忽略不计。

   ```
   root
   - pages
    - login
     - login.vue // {type: refact} {添加微信手机号快速登录功能，修复.native修饰符问题}
   - store
    - modules
     - user.js // {type: refact} {添加wechatLogin action}
   ```

## {datetime: 2024-12-19 14:30:00}

### 1. 添加事故类型多选功能

**Change Type**: feature

> **Purpose**: 在事故趋势页面添加多选事故类型筛选功能，提高数据分析的灵活性。
> **Detailed Description**: 为 `pages/event/bar.vue` 页面的筛选区域添加了事故类型多选功能。实现了自定义多选弹出层，支持用户同时选择多个事故类型（虚惊、急救、医疗处理、工作受限、损工、致残、死亡）进行数据筛选。修改内容包括：添加事故类型选择器UI组件，实现多选弹出层（包含复选框列表），添加相关的数据绑定和事件处理方法（showLevelsPopup、hideLevelsPopup、onLevelsCheckboxChange、confirmLevelsSelection），完善了样式设计并修复了原有的代码质量问题（v-for缺少key、v-if与v-for同时使用等）。
> **Reason for Change**: 用户需要能够同时选择多个事故类型进行数据筛选和分析，提高筛选的灵活性和用户体验。
> **Impact Scope**: 仅影响 `pages/event/bar.vue` 文件，不影响其他模块。
> **API Changes**: 无API变更，使用现有的数据加载接口。
> **Configuration Changes**: 无配置变更。
> **Performance Impact**: 添加了弹出层组件，对性能影响可忽略不计。

   ```
   root
   - pages
    - event
     - bar.vue // {type: refact} {添加多选事故类型筛选功能，修复代码质量问题}
   ```

## {datetime: 2024-07-26 10:30:00}

### 1. 修复编辑页面的图片样式

**Change Type**: fix

> **Purpose**: 解决在"行动措施描述"中上传的图片被拉伸变形的问题。
> **Detailed Description**: 为 `pages/event/edit.vue` 文件中行动措施部分的图片列表（`.file-list-in-measure`）及其子项（`.file-item`, `image` 等）应用了正确的样式，确保图片能够以正确的宽高比（cover）显示，类似于项目中其他文件上传组件的样式。
> **Reason for Change**: 用户反馈图片显示不正确。
> **Impact Scope**: 仅影响 `pages/event/edit.vue` 的样式。
> **API Changes**: 无。
> **Configuration Changes**: 无。
> **Performance Impact**: 无。

   ```
   root
   - pages
    - event
     - edit.vue // {type: refact} {修复了动态表单中的图片样式}
   ```

## {datetime: 2024-07-26 10:15:00}

### 1. 增强事故编辑页面

**Change Type**: feature

> **Purpose**: 在事故编辑页面同步支持新添加的事故信息字段。
> **Detailed Description**: 为 `pages/event/edit.vue` 页面添加了"高潜在风险" (`F_HighRisk`)、"是否已整改" (`F_Correction`) 字段的编辑功能（使用开关组件）。同时，实现了"行动措施描述" (`F_MeasureGroup`) 的动态增删改功能，允许用户为每个行动措施添加描述和上传多个文件。更新了相关的数据绑定、文件处理方法和提交逻辑，并修复了一个 linter 警告。
> **Reason for Change**: 与事故详情页保持同步，使用户能够编辑所有相关的事故信息。
> **Impact Scope**: 仅影响 `pages/event/edit.vue` 文件。
> **API Changes**: 无。此更改假定 `saveEvent` 接口能够正确处理 `F_HighRisk`, `F_Correction`, 和 `F_MeasureGroup` (JSON 字符串格式) 字段。
> **Configuration Changes**: 无。
> **Performance Impact**: 无。

   ```
   root
   - pages
    - event
     - edit.vue // {type: refact} {添加新字段的编辑功能和动态表单}
   ```

## {datetime: 2024-07-26 10:00:00}

### 1. 增强事故详情页面

**Change Type**: feature

> **Purpose**: 在事故详情页上展示更全面的事故信息。
> **Detailed Description**: 为 `pages/event/info.vue` 页面添加了"高潜在风险" (`F_HighRisk`)、"是否已整改" (`F_Correction`) 以及"行动措施描述" (`F_MeasureGroup`) 字段。修改内容包括：更新模板以显示新字段，调整组件数据结构，更新数据加载逻辑以处理和解析新数据，并增强了文件和图片预览功能以支持行动措施中附加的文件。
> **Reason for Change**: 根据用户需求，需要显示这些额外的事故详情。
> **Impact Scope**: 仅影响 `pages/event/info.vue` 文件。
> **API Changes**: 无。此更改假定后端接口 `/api/event/info/:id` 的返回数据中已包含 `F_HighRisk`、`F_Correction` 和 `F_MeasureGroup` 字段。
> **Configuration Changes**: 无。
> **Performance Impact**: 可忽略不计。

   ```
   root
   - pages
    - event
     - info.vue // {type: refact} {添加了新字段并更新了相关逻辑}
   ```

## {datetime: YYYY-MM-DD HH:mm:ss}

### 1. {function simple description}

**Change Type**: {type: feature/fix/improvement/refactor/docs/test/build}

> **Purpose**: {function purpose}
> **Detailed Description**: {function detailed description}
> **Reason for Change**: {why this change is needed}
> **Impact Scope**: {other modules or functions that may be affected by this change}
> **API Changes**: {if there are API changes, detail the old and new APIs}
> **Configuration Changes**: {changes to environment variables, config files, etc.}
> **Performance Impact**: {impact of the change on system performance}

   ```
   root
   - pkg    // {type: add/del/refact/-} {The role of a folder}
    - utils // {type: add/del/refact} {The function of the file}
   - xxx    // {type: add/del/refact} {The function of the file}
   ```

### 2. {function simple description}

**Change Type**: {type: feature/fix/improvement/refactor/docs/test/build}

> **Purpose**: {function purpose}
> **Detailed Description**: {function detailed description}
> **Reason for Change**: {why this change is needed}
> **Impact Scope**: {other modules or functions that may be affected by this change}
> **API Changes**: {if there are API changes, detail the old and new APIs}
> **Configuration Changes**: {changes to environment variables, config files, etc.}
> **Performance Impact**: {impact of the change on system performance}

   ```
   root
   - pkg    // {type: add/del/refact/-} {The role of a folder}
    - utils // {type: add/del/refact} {The function of the file}
   - xxx    // {type: add/del/refact} {The function of the file}
   ```

...