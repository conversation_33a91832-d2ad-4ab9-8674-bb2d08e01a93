# “双休日及节假日审批”页面 UI 验证测试用例

**测试目的:** 验证重构后的页面 UI 符合 ColorUI 设计规范，并与项目其他页面风格保持一致。

**测试范围:**
*   `pages/home/<USER>
*   `pages/home/<USER>

---

### 一、 通用 UI 规范

| 测试用例ID | 测试模块 | 测试项 | 预期结果 (ColorUI 规范) |
| :--- | :--- | :--- | :--- |
| HOLIDAY-UI-001 | 导航栏 | `cu-custom` | 1. 导航栏背景色为 `bg-gradual-blue` 渐变蓝。 <br> 2. 标题和返回按钮文字颜色、大小、位置与其他页面统一。 <br> 3. 在不同尺寸设备上表现正常，无错位或截断。 |
| HOLIDAY-UI-002 | 按钮 | `cu-btn` | 1. 按钮样式（如 `round`, `shadow-blur`, `lg`）正确应用。 <br> 2. 按钮颜色（`bg-gradual-blue`, `bg-blue`）符合设计。 <br> 3. 按钮在点击时有正确的视觉反馈。 |
| HOLIDAY-UI-003 | 字体与间距 | 全局 | 1. 页面字体、字号、行高与项目整体风格一致。 <br> 2. 各元素之间的间距（padding, margin）协调、统一，符合视觉规范。 |

---

### 二、 审批列表页 (`approval-holiday.vue`)

| 测试用例ID | 测试模块 | 测试项 | 预期结果 (ColorUI 规范) |
| :--- | :--- | :--- | :--- |
| HOLIDAY-UI-004 | 搜索栏 | `cu-bar.search` | 1. 搜索栏背景为白色 (`bg-white`)。 <br> 2. 搜索框为圆角 (`round`)。 <br> 3. “搜索”按钮样式正确，与输入框对齐。 |
| HOLIDAY-UI-005 | 列表项 | `uni-list-item` | 1. 列表项之间有清晰的分割线。 <br> 2. 标题、备注文字样式清晰，易于阅读。 <br> 3. 箭头图标位置和样式正确。 |
| HOLIDAY-UI-006 | 状态标签 | `cu-tag` | 1. 标签为圆角 (`round`)。 <br> 2. 背景色 (`bg-orange`, `bg-green`, `bg-red`) 与状态匹配，颜色鲜明。 <br> 3. 标签在列表项中垂直居中对齐。 |

---

### 三、 发起审批页 (`approval-holiday-initiate.vue`)

| 测试用例ID | 测试模块 | 测试项 | 预期结果 (ColorUI 规范) |
| :--- | :--- | :--- | :--- |
| HOLIDAY-UI-007 | 表单 | `cu-form-group` | 1. 表单组之间有分割线，布局整齐。 <br> 2. “标题”和“输入区域”左右对齐，宽度比例协调。 |
| HOLIDAY-UI-008 | 选择器 | `picker` | 1. 点击时弹出的选择器样式（背景、字体）与系统/主题一致。 <br> 2. 未选择时，占位符文本 (`placeholder`) 显示正常。 |
| HOLIDAY-UI-009 | 文本域 | `textarea` | 1. 占位符文本正常显示。 <br> 2. 输入多行文字时，文本域高度能自适应或出现滚动条，内容不会溢出。 |