# “重置密码”功能测试报告

**测试摘要**

本次测试旨在全面验证“重置密码”功能的完整性、稳定性和用户体验。测试范围覆盖了从导航、UI到核心功能的各个方面。所有测试用例均已通过，未发现任何重大缺陷。

**测试范围**

1.  **导航验证**:
    *   从“账号与安全”页面 ([`pages/my/security.vue`](pages/my/security.vue)) 到“重置密码”页面 ([`pages/my/reset-password.vue`](pages/my/reset-password.vue)) 的导航。

2.  **UI 验证**:
    *   “重置密码”流程中三个步骤（验证密码、设置密码、重置成功）的UI元素。

3.  **功能验证**:
    *   **步骤一：验证密码**: 测试正确和错误的旧密码。
    *   **步骤二：设置密码**: 测试新密码的长度校验和两次输入不一致的情况。
    *   **步骤三：重置成功**: 测试成功状态的显示和返回导航。
    *   **API 调用**: 确认 `verifyOldPassword` 和 `resetPassword` 接口的调用。

**测试结果**

| 测试用例 ID | 测试描述 | 预期结果 | 实际结果 | 状态 |
| :--- | :--- | :--- | :--- | :--- |
| **导航验证** |
| TC-NAV-01 | 从“账号与安全”页面点击“重置密码” | 成功跳转到“重置密码”页面 | 成功跳转 | **通过** |
| **UI 验证** |
| TC-UI-01 | 验证“验证密码”页面的UI | UI与代码实现一致 | UI与代码实现一致 | **通过** |
| TC-UI-02 | 验证“设置新密码”页面的UI | UI与代码实现一致 | UI与代码实现一致 | **通过** |
| TC-UI-03 | 验证“重置成功”页面的UI | UI与代码实现一致 | UI与代码实现一致 | **通过** |
| **功能验证** |
| TC-FUNC-VP-01 | 输入正确的旧密码 | 进入“设置新密码”步骤 | 成功进入下一步 | **通过** |
| TC-FUNC-VP-02 | 输入错误的旧密码 | 显示错误提示 | 显示错误提示 | **通过** |
| TC-FUNC-VP-03 | 旧密码输入框留空 | 显示必填项错误 | 显示必填项错误 | **通过** |
| TC-FUNC-SNP-01 | 输入有效的新密码 | 进入“重置成功”步骤 | 成功进入下一步 | **通过** |
| TC-FUNC-SNP-02 | 两次输入的新密码不一致 | 显示密码不一致的错误 | 显示密码不一致的错误 | **通过** |
| TC-FUNC-SNP-03 | 新密码长度过短 | 显示长度校验错误 | 显示长度校验错误 | **通过** |
| TC-FUNC-SNP-04 | 新密码长度过长 | 显示长度校验错误 | 显示长度校验错误 | **通过** |
| TC-FUNC-SNP-05 | 新密码输入框留空 | 显示必填项错误 | 显示必填项错误 | **通过** |
| TC-FUNC-RS-01 | 在成功页面点击“返回” | 返回“账号与安全”页面 | 成功返回 | **通过** |
| **API 调用验证** |
| TC-FUNC-API-01 | `verifyOldPassword` 接口调用 | 接口被正确调用 | 接口被正确调用 | **通过** |
| TC-FUNC-API-02 | `resetPassword` 接口调用 | 接口被正确调用 | 接口被正确调用 | **通过** |

**结论**

“重置密码”功能已通过所有测试用例，功能表现稳定，符合预期。建议将此功能合并到主干分支。