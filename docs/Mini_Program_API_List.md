# 小程序接口清单

本文档列出了小程序前端所使用的所有API接口，包括接口名称、请求路径、请求方法以及简要的功能描述。

| 接口名称 | 请求路径 | 请求方法 | 功能描述 |
| :--- | :--- | :--- | :--- |
| `login` | `/User/Login` | `POST` | 用户登录 |
| `wechatLogin` | `/User/WechatLogin` | `POST` | 微信登录 |
| `logout` | `/User/LoginOff` | `POST` | 用户登出 |
| `checkLoginState` | `/User/CheckLoginState` | `POST` | 检查登录状态 |
| `listUser` | `/User/GetUserList` | `GET` | 获取用户列表 |
| `listRole` | `/User/GetRoleList` | `GET` | 获取角色列表 |
| `settingRole` | `/User/SettingRole` | `POST` | 设置用户角色 |
| `listContractor` | `/Task/GetContractorList` | `GET` | 获取承包商列表 |
| `getContractor` | `/Task/GetContractorInfo` | `GET` | 获取承包商详情 |
| `listStaff` | `/Task/GetStaffList` | `GET` | 获取员工列表 |
| `getStaff` | `/Task/GetStaffInfo` | `GET` | 获取员工详情 |
| `saveStaff` | `/Task/StaffSave` | `POST` | 保存员工信息 |
| `saveContractor` | `/Task/ContractorSave` | `POST` | 保存承包商信息 |
| `delStaff` | `/Task/DelStaffInfo` | `POST` | 删除员工信息 |
| `userPwdModify` | `/post/user/pwd/modify` | `POST` | 修改用户密码 |
| `verifyOldPassword` | `/User/VerifyOldPassword` | `POST` | 验证旧密码 |
| `resetPassword` | `/User/ResetPassword` | `POST` | 重置密码 |
| `bindEmail` | `/User/BindEmail` | `POST` | 绑定邮箱 |
| `listAuditProject` | `/get/audit/project/list` | `GET` | 获取项目审批列表 |
| `listAuditUser` | `/get/audit/user/list` | `GET` | 获取用户审批列表 |
| `getEventList` | `/Event/GetEventList` | `GET` | 获取事故列表 |
| `getEventInfo` | `/Event/GetEventInfo` | `GET` | 获取事故详情 |
| `saveEvent` | `/Event/EventSave` | `POST` | 保存事故信息 |
| `getEventDays` | `/Event/GetDays` | `GET` | 获取事故天数 |
| `getTaskList` | `/Task/GetTaskList` | `GET` | 获取任务列表 |
| `getGroupTaskInfo` | `/Task/GetGroupTaskInfo` | `GET` | 获取分组任务详情 |
| `getSimpleTaskInfo` | `/Task/GetSimpleTaskInfo` | `GET` | 获取简单任务详情 |
| `getTaskInfo` | `/Task/GetTaskInfo` | `GET` | 获取任务详情 |
| `completeTask` | `/Task/CompleteTask` | `POST` | 完成任务 |
| `saveTask` | `/Task/SaveTask` | `POST` | 保存任务信息 |
| `loadBarCharts` | `/Event/GetChartsJson` | `GET` | 加载柱状图数据 |
| `loadTableCharts` | `/Event/GetChartsByYearJson` | `GET` | 按年加载表格图数据 |
| `loadEventByMonth` | `/Event/GetChartsByMonthJson` | `GET` | 按月加载事件数据 |
| `loadEventByDay` | `/Event/GetChartsByDayJson` | `GET` | 按天加载事件数据 |
| `loadTaskCharts` | `/Task/GetChartsJson` | `GET` | 加载任务图表数据 |
| `loadIndexData` | `/Task/GetIndexData` | `GET` | 加载首页数据 |
| `getOrgList` | `/Event/GetOrgList` | `GET` | 获取组织列表 |
| `uploadFile` | `/File/Upload` | `POST` | 文件上传 |
