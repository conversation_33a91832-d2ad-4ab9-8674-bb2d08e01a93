# 技术规范文档

本文档基于对项目代码和结构的分析，反向推断出当前的技术选型与规范。

## 1. 核心框架
- **开发框架**: Uni-app
- **前端框架**: Vue.js (Vue 2)
- **UI框架**: ColorUI

## 2. 状态管理
- **方案**: Vuex
- **结构**: 采用模块化结构，通过 `require.context` 自动导入 `store/modules/` 目录下的所有模块。目前已知的模块包括 `app` 和 `user`。

## 3. API 通信
- **封装**: 基于 `uni.request` 进行了二次封装，封装库位于 `utils/MinRequest`。通过拦截器统一注入 `WC-Token`。
- **规范**: API 在 `api/api.js` 文件中统一管理，并按照业务模块（如 `User`, `Task`, `Event`）进行划分，便于维护和调用。

## 4. 编码规范
- **遵循文档**: 本项目的编码规范遵循 [`docs/code_specification.md`](docs/code_specification.md) 中的约定。
- **组件化**: 项目在 `components/` 目录下存放了大量的自定义和第三方组件，如 `mescroll-uni`、`uni-badge` 等，体现了良好的组件化实践。