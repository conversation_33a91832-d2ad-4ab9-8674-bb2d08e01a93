# WaterCloud 项目技术架构分析报告

## 1. 综述

**WaterCloud** 是一个基于 **.NET 6.0** 构建的、功能强大且高度可扩展的后台管理系统框架。项目采用了经典的多层架构设计，职责分离清晰，并整合了一系列业界主流的开源组件，为快速开发企业级应用提供了坚实的基础。

## 2. 项目架构

项目遵循了经典的多层（N-Tier）架构设计，并引入了插件化的思想以增强系统的可扩展性。

### 2.1. 核心分层

*   **`WaterCloud.Web` (表现层)**:
    *   **技术**: ASP.NET Core MVC, Razor Pages, Web API
    *   **职责**: 用户界面（UI）渲染、API接口暴露、请求的接收与响应、全局异常处理、静态资源管理。
*   **`WaterCloud.Service` (业务逻辑层)**:
    *   **职责**: 实现核心业务逻辑、处理复杂的业务规则和工作流。
*   **`WaterCloud.Domain` (领域模型层)**:
    *   **职责**: 定义系统的核心业务实体（Entities）和值对象（Value Objects）。
*   **`WaterCloud.Data` (数据访问层)**:
    *   **技术**: SqlSugar ORM
    *   **职责**: 负责与数据库进行交互，实现数据的持久化和检索。
*   **`WaterCloud.Code` (通用基础库)**:
    *   **职责**: 提供整个解决方案可复用的通用组件，如缓存、工具类、安全、过滤器等。
*   **`WaterCloud.CodeGenerator` (代码生成器)**:
    *   **职责**: 用于根据数据库表或模板快速生成各层的标准代码，提高开发效率。

### 2.2. 插件化架构

系统设计了动态插件机制，允许在不修改主程序的情况下扩展系统功能。

*   **实现方式**: 启动时扫描 `Plugins` 目录，动态加载其中的 `.dll` 文件作为 `ApplicationPart`，将其中的控制器、视图等集成到主应用中。
*   **优势**: 实现了真正的模块化开发，不同功能模块可以独立开发、部署和升级，降低了模块间的耦合度。

## 3. 技术栈详解

| 分类 | 技术/组件 | 版本/说明 |
| :--- | :--- | :--- |
| **后端框架** | .NET 6.0 | 长期支持（LTS）版本，跨平台。 |
| **数据库ORM** | SqlSugarCore | 5.1.4.59，高性能、功能丰富的ORM框架。 |
| **数据库** | SQL Server, Oracle | 主要使用SQL Server，但具备切换到Oracle的能力。 |
| **前端UI** | LayUI | 经典模块化前端UI框架。 |
| **依赖注入** | Autofac | 8.0.0，功能强大的IoC/DI容器。 |
| **任务调度** | Quartz.NET | 3.6.2，用于后台定时任务处理。 |
| **缓存** | MemoryCache, Redis | 支持内存缓存和Redis分布式缓存，可在配置中切换。 |
| **实时通信** | SignalR | 用于实现服务器与客户端之间的实时消息推送。 |
| **消息队列** | RabbitMQ | 配置中预留了支持，用于系统间的异步解耦。 |
| **JSON处理** | Newtonsoft.Json | 13.0.1，用于JSON序列化和反序列化。 |
| **Excel处理**| MiniExcel | 1.30.2，轻量级、高性能的Excel读写库。 |
| **API文档** | Swagger (Swashbuckle) | 自动生成交互式API文档。 |
| **微信集成** | Senparc.Weixin.MP | 16.23.8，用于微信公众号相关功能开发。 |

## 4. 关键特性

*   **多租户/多库支持**: 系统配置了 `"SqlMode": "MoreSql"`，表明其具备多数据库或多租户的架构能力。
*   **统一的全局异常处理**: 通过自定义中间件 `GlobalExceptionMiddleware` 捕获全局异常，增强系统健壮性。
*   **灵活的路由配置**: 为MVC、API、Areas分别定义了清晰的路由规则。
*   **可配置性**: 大部分核心服务和参数（如数据库、缓存、任务开关）均可通过 `appsettings.json` 进行灵活配置。