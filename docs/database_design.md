# 数据库设计文档

本文档基于 `ehs.sql` 文件生成，旨在提供 `WaterCloud` 项目数据库结构的权威、准确的参考。

## 1. 系统管理模块 (sys_*)

系统管理模块包含了支撑整个平台运行的基础功能，如用户、角色、权限、组织架构、菜单、字典、日志等。

### 1.1. 用户表 (sys_user)
存储系统用户信息。

| 字段名 | 数据类型 | 可空 | 主键 | 描述 |
| :--- | :--- | :--- | :--- | :--- |
| F_Id | nvarchar(50) | N | Y | 用户主键 |
| F_Account | nvarchar(50) | Y | | 登录账户 (唯一) |
| F_RealName | nvarchar(50) | Y | | 真实姓名 |
| F_NickName | nvarchar(50) | Y | | 昵称 |
| F_HeadIcon | nvarchar(50) | Y | | 头像 |
| F_Gender | tinyint | Y | | 性别 |
| F_Birthday | datetime2(7) | Y | | 生日 |
| F_MobilePhone | nvarchar(20) | Y | | 手机号码 |
| F_Email | nvarchar(50) | Y | | 电子邮箱 |
| F_WeChat | nvarchar(50) | Y | | 微信号 |
| F_ManagerId | nvarchar(50) | Y | | 主管Id |
| F_SecurityLevel | int | Y | | 安全级别 |
| F_Signature | nvarchar(max) | Y | | 个性签名 |
| F_CompanyId | nvarchar(50) | Y | | 所属公司 |
| F_OrganizeId | nvarchar(max) | Y | | 所属组织 |
| F_RoleId | nvarchar(max) | Y | | 角色 |
| F_DutyId | nvarchar(max) | Y | | 岗位 |
| F_IsAdmin | tinyint | Y | | 是否管理员 |
| F_IsBoss | tinyint | Y | | 是否老板 |
| F_IsLeaderInDepts | tinyint | Y | | 是否部门领导 |
| F_IsSenior | tinyint | Y | | 是否高管 |
| F_SortCode | int | Y | | 排序码 |
| F_DeleteMark | tinyint | Y | | 删除标志 |
| F_EnabledMark | tinyint | Y | | 有效标志 |
| F_Description | nvarchar(max) | Y | | 备注 |
| F_CreatorTime | datetime2(7) | Y | | 创建时间 |
| F_CreatorUserId | nvarchar(50) | Y | | 创建用户 |
| F_LastModifyTime | datetime2(7) | Y | | 修改时间 |
| F_LastModifyUserId | nvarchar(50) | Y | | 修改用户 |
| F_DeleteTime | datetime2(7) | Y | | 删除时间 |
| F_DeleteUserId | nvarchar(50) | Y | | 删除用户 |
| F_DingTalkUserId | nvarchar(50) | Y | | 钉钉用户Id |
| F_DingTalkUserName | nvarchar(50) | Y | | 钉钉用户名称 |
| F_DingTalkAvatar | nvarchar(100) | Y | | 钉钉头像 |
| F_WxOpenId | nvarchar(50) | Y | | 微信开放Id |
| F_WxNickName | nvarchar(50) | Y | | 微信昵称 |
| F_HeadImgUrl | nvarchar(100) | Y | | 微信头像 |

### 1.2. 用户登录表 (sys_userlogon)
存储用户登录相关信息，与用户表一对一关联。

| 字段名 | 数据类型 | 可空 | 主键 | 描述 |
| :--- | :--- | :--- | :--- | :--- |
| F_Id | nvarchar(50) | N | Y | 登录主键 |
| F_UserId | nvarchar(50) | Y | | 用户主键 (唯一) |
| F_UserPassword | nvarchar(50) | Y | | 用户密码 |
| F_UserSecretkey | nvarchar(50) | Y | | 用户秘钥 |
| F_AllowStartTime | datetime2(7) | Y | | 允许登录时间 |
| F_AllowEndTime | datetime2(7) | Y | | 允许登录结束时间 |
| F_LockStartDate | datetime2(7) | Y | | 锁定开始时间 |
| F_LockEndDate | datetime2(7) | Y | | 锁定结束时间 |
| F_FirstVisitTime | datetime2(7) | Y | | 第一次访问时间 |
| F_PreviousVisitTime | datetime2(7) | Y | | 上一次访问时间 |
| F_LastVisitTime | datetime2(7) | Y | | 最后一次访问时间 |
| F_ChangePasswordDate | datetime2(7) | Y | | 修改密码时间 |
| F_MultiUserLogin | tinyint | Y | | 是否允许多用户登录 |
| F_LogOnCount | int | Y | | 登录次数 |
| F_UserOnLine | tinyint | Y | | 是否在线 |
| F_Question | nvarchar(50) | Y | | 密保问题 |
| F_AnswerQuestion | nvarchar(max) | Y | | 密保答案 |
| F_CheckIPAddress | tinyint | Y | | 是否检查IP地址 |
| F_Language | nvarchar(50) | Y | | 语言 |
| F_Theme | nvarchar(50) | Y | | 主题 |
| F_LoginSession | nvarchar(100) | Y | | 登录Session |
| F_ErrorNum | int | Y | | 密码错误次数 |

### 1.3. 角色表 (sys_role)
定义系统中的角色。

| 字段名 | 数据类型 | 可空 | 主键 | 描述 |
| :--- | :--- | :--- | :--- | :--- |
| F_Id | nvarchar(50) | N | Y | 角色主键 |
| F_CompanyId | nvarchar(50) | Y | | 所属公司 |
| F_Category | int | Y | | 分类 |
| F_EnCode | nvarchar(50) | Y | | 角色编码 (唯一) |
| F_FullName | nvarchar(50) | Y | | 角色名称 |
| F_Type | nvarchar(50) | Y | | 类型 |
| F_AllowEdit | tinyint | Y | | 允许编辑 |
| F_AllowDelete | tinyint | Y | | 允许删除 |
| F_SortCode | int | Y | | 排序码 |
| F_DeleteMark | tinyint | Y | | 删除标志 |
| F_EnabledMark | tinyint | Y | | 有效标志 |
| F_Description | nvarchar(max) | Y | | 备注 |
| F_CreatorTime | datetime2(7) | Y | | 创建时间 |
| F_CreatorUserId | nvarchar(50) | Y | | 创建用户 |
| F_LastModifyTime | datetime2(7) | Y | | 修改时间 |
| F_LastModifyUserId | nvarchar(50) | Y | | 修改用户 |
| F_DeleteTime | datetime2(7) | Y | | 删除时间 |
| F_DeleteUserId | nvarchar(50) | Y | | 删除用户 |

### 1.4. 组织机构表 (sys_organize)
定义公司的组织架构，如部门、班组等。

| 字段名 | 数据类型 | 可空 | 主键 | 描述 |
| :--- | :--- | :--- | :--- | :--- |
| F_Id | nvarchar(50) | N | Y | 组织主键 |
| F_ParentId | nvarchar(50) | Y | | 父级 |
| F_Layers | int | Y | | 层次 |
| F_EnCode | nvarchar(50) | Y | | 编码 (唯一) |
| F_FullName | nvarchar(50) | Y | | 名称 |
| F_ShortName | nvarchar(50) | Y | | 简称 |
| F_CategoryId | nvarchar(50) | Y | | 分类 |
| F_ManagerId | nvarchar(50) | Y | | 负责人 |
| F_TelePhone | nvarchar(20) | Y | | 电话 |
| F_MobilePhone | nvarchar(20) | Y | | 手机 |
| F_WeChat | nvarchar(50) | Y | | 微信 |
| F_Fax | nvarchar(20) | Y | | 传真 |
| F_Email | nvarchar(50) | Y | | 邮箱 |
| F_AreaId | nvarchar(50) | Y | | 归属区域 |
| F_Address | nvarchar(max) | Y | | 联系地址 |
| F_AllowEdit | tinyint | Y | | 允许编辑 |
| F_AllowDelete | tinyint | Y | | 允许删除 |
| F_SortCode | int | Y | | 排序码 |
| F_DeleteMark | tinyint | Y | | 删除标志 |
| F_EnabledMark | tinyint | Y | | 有效标志 |
| F_Description | nvarchar(max) | Y | | 备注 |
| F_CreatorTime | datetime2(7) | Y | | 创建时间 |
| F_CreatorUserId | nvarchar(50) | Y | | 创建用户 |
| F_LastModifyTime | datetime2(7) | Y | | 修改时间 |
| F_LastModifyUserId | nvarchar(50) | Y | | 修改用户 |
| F_DeleteTime | datetime2(7) | Y | | 删除时间 |
| F_DeleteUserId | nvarchar(50) | Y | | 删除用户 |
| F_StaffCount | int | Y | | 成员数量 |

### 1.5. 菜单(模块)表 (sys_module)
定义系统的功能模块和菜单。

| 字段名 | 数据类型 | 可空 | 主键 | 描述 |
| :--- | :--- | :--- | :--- | :--- |
| F_Id | nvarchar(50) | N | Y | 模块主键 |
| F_ParentId | nvarchar(50) | Y | | 父级 |
| F_Layers | int | Y | | 层次 |
| F_EnCode | nvarchar(50) | Y | | 编码 (唯一) |
| F_FullName | nvarchar(50) | Y | | 名称 |
| F_Icon | nvarchar(50) | Y | | 图标 |
| F_UrlAddress | nvarchar(max) | Y | | 链接地址 |
| F_Target | nvarchar(50) | Y | | 目标 |
| F_IsMenu | tinyint | Y | | 是否菜单 |
| F_IsExpand | tinyint | Y | | 是否展开 |
| F_IsFields | tinyint | Y | | 是否公共 |
| F_IsPublic | tinyint | Y | | 是否公共 |
| F_AllowEdit | tinyint | Y | | 允许编辑 |
| F_AllowDelete | tinyint | Y | | 允许删除 |
| F_SortCode | int | Y | | 排序码 |
| F_DeleteMark | tinyint | Y | | 删除标志 |
| F_EnabledMark | tinyint | Y | | 有效标志 |
| F_Description | nvarchar(max) | Y | | 备注 |
| F_CreatorTime | datetime2(7) | Y | | 创建时间 |
| F_CreatorUserId | nvarchar(50) | Y | | 创建用户 |
| F_LastModifyTime | datetime2(7) | Y | | 修改时间 |
| F_LastModifyUserId | nvarchar(50) | Y | | 修改用户 |
| F_DeleteTime | datetime2(7) | Y | | 删除时间 |
| F_DeleteUserId | nvarchar(50) | Y | | 删除用户 |
| F_Authorize | nvarchar(100) | Y | | 权限标识 |

### 1.6. 权限关系表 (sys_roleauthorize)
存储角色与菜单、按钮、数据权限的对应关系。

| 字段名 | 数据类型 | 可空 | 主键 | 描述 |
| :--- | :--- | :--- | :--- | :--- |
| F_Id | nvarchar(50) | N | Y | 授权主键 |
| F_ItemType | int | Y | | 项目类型1-模块2-按钮3-字段4-数据 |
| F_ItemId | nvarchar(50) | Y | | 项目主键 |
| F_ObjectType | int | Y | | 对象分类1-角色2-部门-3用户 |
| F_ObjectId | nvarchar(50) | Y | | 对象主键 |
| F_SortCode | int | Y | | 排序码 |
| F_CreatorTime | datetime2(7) | Y | | 创建时间 |
| F_CreatorUserId | nvarchar(50) | Y | | 创建用户 |

### 1.7. 日志表 (sys_log)
记录系统操作日志和异常日志。

| 字段名 | 数据类型 | 可空 | 主键 | 描述 |
| :--- | :--- | :--- | :--- | :--- |
| F_Id | nvarchar(50) | N | Y | 日志主键 |
| F_Date | datetime2(7) | Y | | 日期 |
| F_Account | nvarchar(50) | Y | | 用户名 |
| F_NickName | nvarchar(50) | Y | | 姓名 |
| F_Type | nvarchar(50) | Y | | 类型 |
| F_IPAddress | nvarchar(50) | Y | | IP地址 |
| F_IPAddressName | nvarchar(50) | Y | | IP所在城市 |
| F_ModuleId | nvarchar(50) | Y | | 系统模块Id |
| F_ModuleName | nvarchar(50) | Y | | 系统模块 |
| F_Result | tinyint | Y | | 结果 |
| F_Description | nvarchar(max) | Y | | 描述 |
| F_CreatorTime | datetime2(7) | Y | | 创建时间 |
| F_CreatorUserId | nvarchar(50) | Y | | 创建用户 |
| F_KeyValue | nvarchar(max) | Y | | 关键值 |
| F_CompanyId | nvarchar(50) | Y | | 公司Id |

---

## 2. 业务管理模块 (oms_*)

业务管理模块是 EHS 系统的核心，包含事故、任务、流程、承包商等管理功能。

### 2.1. 事故台账表 (oms_event)
记录安全事故的详细信息。

| 字段名 | 数据类型 | 可空 | 主键 | 描述 |
| :--- | :--- | :--- | :--- | :--- |
| F_Id | varchar(50) | N | Y | 事故ID |
| F_Name | nvarchar(100) | Y | | 事故名称 |
| F_Num | varchar(80) | Y | | 事故编号 |
| F_Description | nvarchar(255) | Y | | 事故描述 |
| F_Date | datetime2(7) | Y | | 事故时间 |
| F_Level | nvarchar(20) | Y | | 事故等级 |
| F_Category | nvarchar(20) | Y | | 事故分类 |
| F_BMU | nvarchar(50) | Y | | 车间 |
| F_Class | nvarchar(50) | Y | | 班组 |
| F_BPU | nvarchar(50) | Y | | BPU |
| F_Surname | nvarchar(50) | Y | | 受伤人员 |
| F_ResearchUser | nvarchar(50) | Y | | 调查人员 |
| F_Actions | nvarchar(255) | Y | | 立即措施 |
| F_Sketch | nvarchar(255) | Y | | 草图或附件 |
| F_DeleteMark | tinyint | Y | | 删除标志 |
| F_EnabledMark | tinyint | Y | | 有效标志 |
| F_CreatorTime | datetime2(7) | Y | | 创建时间 |
| F_CreatorUserId | nvarchar(50) | Y | | 创建用户ID |
| F_CreatorUserName | nvarchar(50) | Y | | 创建用户名 |
| F_LastModifyTime | datetime2(7) | Y | | 修改时间 |
| F_LastModifyUserId | nvarchar(50) | Y | | 修改用户ID |
| F_DeleteTime | datetime2(7) | Y | | 删除时间 |
| F_DeleteUserId | nvarchar(50) | Y | | 删除用户ID |
| F_ReportUser | nvarchar(50) | Y | | 报告人 |
| F_ReportUserNum | nvarchar(64) | Y | | 报告人工号 |
| F_ReportFile | nvarchar(255) | Y | | 事故报告 |
| F_Reason | nvarchar(255) | Y | | 原因分析 |
| F_ActionDesc | nvarchar(255) | Y | | 行动措施 |
| F_Status | tinyint | Y | | 报告状态（0未上传1已上传） |
| F_Surno | nvarchar(50) | Y | | 受伤人员工号 |
| F_Surage | tinyint | Y | | 受伤人员年龄 |
| F_SurBuWei | nvarchar(100) | Y | | 受伤部位 |
| F_SurType | nvarchar(50) | Y | | 受伤类型 |
| F_HighRisk | tinyint | Y | | 高潜风险0否1是 |
| F_MeasureGroup | nvarchar(max) | Y | | 措施组 |
| F_Correction | tinyint | Y | | 是否整改0未整改1已整改 |
| F_JobTime | nvarchar(50) | Y | | 本岗位工龄 |
| F_SurJoinTime | nvarchar(50) | Y | | 入厂时间 |

### 2.2. 作业任务表 (oms_task)
记录各类作业任务信息。

| 字段名 | 数据类型 | 可空 | 主键 | 描述 |
| :--- | :--- | :--- | :--- | :--- |
| F_Id | nvarchar(50) | N | Y | 作业id |
| F_ApplyTime | datetime2(7) | Y | | 作业申请时间 |
| F_Num | int | Y | | 序号 |
| F_Type | int | Y | | 作业类型 |
| F_Content | nvarchar(255) | Y | | 作业内容 |
| F_Risk | int | Y | | 危险类型 |
| F_ApplyTable | varchar(500) | Y | | 施工申请表 |
| F_Leader | varchar(50) | Y | | 现场负责人 |
| F_Staff | varchar(1000) | Y | | 施工人员 |
| F_Server | varchar(255) | Y | | 公司名称 |
| F_Parent | varchar(50) | Y | | 主任务id |
| F_Position | varchar(255) | Y | | 坐标 |
| F_CompleteTime | datetime2(7) | Y | | 作业完成时间 |
| F_Status | int | Y | | 状态 |
| F_DeleteMark | tinyint | Y | | 删除标志 |
| F_CreatorTime | datetime2(7) | N | | 创建时间 |
| F_CreatorUserId | nvarchar(50) | Y | | 创建用户ID |
| F_CreatorUserName | nvarchar(50) | Y | | 创建用户名 |
| F_LastModifyTime | datetime2(7) | Y | | 修改时间 |
| F_LastModifyUserId | nvarchar(50) | Y | | 修改用户ID |
| F_ExpectTime | datetime2(7) | Y | | 预计完成日期 |

### 2.3. 流程设计表 (sys_flowscheme)
存储工作流的设计模板。

| 字段名 | 数据类型 | 可空 | 主键 | 描述 |
| :--- | :--- | :--- | :--- | :--- |
| F_Id | nvarchar(50) | N | Y | 主键Id |
| F_SchemeCode | nvarchar(50) | Y | | 流程编号 |
| F_SchemeName | nvarchar(200) | Y | | 流程名称 |
| F_SchemeType | nvarchar(50) | Y | | 流程分类 |
| F_SchemeVersion | nvarchar(50) | Y | | 流程内容版本 |
| F_SchemeCanUser | nvarchar(max) | Y | | 流程模板使用者 |
| F_SchemeContent | nvarchar(max) | Y | | 流程内容 |
| F_FrmId | nvarchar(50) | Y | | 表单ID |
| F_FrmType | int | N | | 表单类型 |
| F_AuthorizeType | int | N | | 模板权限类型：0完全公开,1指定部门/人员 |
| F_SortCode | int | Y | | 排序码 |
| F_DeleteMark | tinyint | Y | | 删除标记 |
| F_EnabledMark | tinyint | Y | | 有效 |
| F_Description | nvarchar(200) | Y | | 备注 |
| F_CreatorTime | datetime2(7) | Y | | 创建时间 |
| F_CreatorUserId | nvarchar(50) | Y | | 创建用户主键 |
| F_CreatorUserName | nvarchar(50) | Y | | 创建用户 |
| F_LastModifyTime | datetime2(7) | Y | | 修改时间 |
| F_LastModifyUserId | nvarchar(50) | Y | | 修改用户主键 |
| F_LastModifyUserName | nvarchar(50) | Y | | 修改用户 |
| F_OrganizeId | nvarchar(50) | Y | | 所属部门 |
| F_DeleteTime | datetime2(7) | Y | | 删除时间 |
| F_DeleteUserId | nvarchar(50) | Y | | 删除人 |
| F_InitiatorType | nvarchar(50) | Y | | 发起人权限类型 |
| F_InitiatorData | nvarchar(max) | Y | | 发起人权限数据 |

### 2.4. 流程实例表 (oms_flowinstance)
存储每个被发起的工作流实例。

| 字段名 | 数据类型 | 可空 | 主键 | 描述 |
| :--- | :--- | :--- | :--- | :--- |
| F_Id | nvarchar(50) | N | Y | 主键Id |
| F_InstanceSchemeId | nvarchar(50) | N | | 流程实例模板Id |
| F_Code | nvarchar(200) | Y | | 实例编号 (唯一) |
| F_CustomName | nvarchar(200) | Y | | 自定义名称 |
| F_ActivityId | nvarchar(50) | Y | | 当前节点ID |
| F_ActivityType | int | Y | | 当前节点类型（0会签节点） |
| F_ActivityName | nvarchar(200) | Y | | 当前节点名称 |
| F_PreviousId | nvarchar(50) | Y | | 前一个ID |
| F_SchemeContent | nvarchar(max) | Y | | 流程模板内容 |
| F_SchemeId | nvarchar(50) | Y | | 流程模板ID |
| F_DbName | nvarchar(50) | Y | | 数据库名称 |
| F_FrmData | nvarchar(max) | Y | | 表单数据 |
| F_FrmType | int | N | | 表单类型 |
| F_FrmContentData | nvarchar(max) | Y | | 表单字段 |
| F_FrmContentParse | nvarchar(max) | Y | | 表单参数（冗余） |
| F_FrmId | nvarchar(50) | Y | | 表单ID |
| F_SchemeType | nvarchar(50) | Y | | 流程类型 |
| F_FlowLevel | int | N | | 等级 |
| F_Description | nvarchar(200) | Y | | 实例备注 |
| F_IsFinish | int | N | | 是否完成 |
| F_MakerList | nvarchar(max) | Y | | 执行人 |
| F_OrganizeId | nvarchar(50) | Y | | 所属部门 |
| F_EnabledMark | tinyint | Y | | 有效 |
| F_CreatorTime | datetime2(7) | Y | | 创建时间 |
| F_CreatorUserId | nvarchar(50) | Y | | 创建用户主键 |
| F_CreatorUserName | nvarchar(50) | Y | | 创建用户 |
| F_FrmContent | nvarchar(max) | Y | | 表单元素json |

### 2.5. 承包商表 (oms_contractor)
管理承包商信息。

| 字段名 | 数据类型 | 可空 | 主键 | 描述 |
| :--- | :--- | :--- | :--- | :--- |
| F_Id | nvarchar(50) | N | Y | 主键 |
| F_CompanyName | nvarchar(50) | N | | 公司名称 |
| F_Address | nvarchar(100) | N | | 地址 |
| F_TypeName | nvarchar(20) | N | | 类型名称 |
| F_HeadName | nvarchar(20) | N | | 负责人姓名 |
| F_ContactsName | nvarchar(20) | N | | 联系人姓名 |
| F_ContactsTel | nvarchar(20) | Y | | 联系人电话 |
| F_ServeId | varchar(200) | N | | 服务ID |
| F_StartTime | datetime2(7) | Y | | 开始时间 |
| F_EndTime | datetime2(7) | Y | | 结束时间 |
| F_EnabledMark | tinyint | Y | | 有效标志 |
| F_SignName | nvarchar(20) | N | | 签订人 |
| F_Remark | nvarchar(100) | Y | | 备注 |
| F_Evaluate | nvarchar(200) | N | | 评价 |
| F_License | varchar(100) | Y | | 营业执照 |
| F_DeleteMark | tinyint | Y | | 删除标志 |
| F_Description | nvarchar(max) | Y | | 描述 |
| F_CreatorTime | datetime2(7) | N | | 创建时间 |
| F_CreatorUserId | nvarchar(50) | Y | | 创建用户ID |
| F_CreatorUserName | nvarchar(50) | Y | | 创建用户名 |
| F_LastModifyTime | datetime2(7) | Y | | 修改时间 |
| F_LastModifyUserId | nvarchar(50) | Y | | 修改用户ID |
| F_Num | nvarchar(50) | Y | | 编号 |

---

## 3. 内容管理模块 (cms_*)

用于管理系统中的文章、新闻等内容。

### 3.1. 文章分类表 (cms_articlecategory)

| 字段名 | 数据类型 | 可空 | 主键 | 描述 |
| :--- | :--- | :--- | :--- | :--- |
| F_Id | nvarchar(50) | N | Y | 主键Id |
| F_FullName | nvarchar(100) | Y | | 类别名称 |
| F_ParentId | nvarchar(50) | Y | | 父级Id |
| F_SortCode | int | N | | 排序 |
| F_Description | nvarchar(max) | Y | | 描述 |
| F_LinkUrl | nvarchar(255) | Y | | 链接地址 |
| F_ImgUrl | nvarchar(255) | Y | | 图片地址 |
| F_SeoTitle | nvarchar(255) | Y | | SEO标题 |
| F_SeoKeywords | nvarchar(255) | Y | | SEO关键字 |
| F_SeoDescription | nvarchar(255) | Y | | SEO描述 |
| F_IsHot | tinyint | Y | | 是否热门 |
| F_EnabledMark | tinyint | Y | | 是否启用 |
| F_DeleteMark | tinyint | Y | | 删除标志 |
| F_CreatorTime | datetime2(7) | Y | | 创建时间 |
| F_CreatorUserId | nvarchar(50) | Y | | 创建人 |
| F_LastModifyTime | datetime2(7) | Y | | 最后修改时间 |
| F_LastModifyUserId | nvarchar(50) | Y | | 最后修改人 |
| F_DeleteTime | datetime2(7) | Y | | 删除时间 |
| F_DeleteUserId | nvarchar(50) | Y | | 删除人 |

### 3.2. 文章新闻表 (cms_articlenews)

| 字段名 | 数据类型 | 可空 | 主键 | 描述 |
| :--- | :--- | :--- | :--- | :--- |
| F_Id | nvarchar(50) | N | Y | 文章主键Id |
| F_CategoryId | nvarchar(50) | N | | 类别Id |
| F_Title | nvarchar(200) | Y | | 标题 |
| F_LinkUrl | nvarchar(255) | Y | | 链接地址 |
| F_ImgUrl | nvarchar(255) | Y | | 图片地址 |
| F_SeoTitle | nvarchar(255) | Y | | SEO标题 |
| F_SeoKeywords | nvarchar(255) | Y | | SEO关键字 |
| F_SeoDescription | nvarchar(255) | Y | | SEO描述 |
| F_Tags | nvarchar(max) | Y | | 标签 |
| F_Zhaiyao | nvarchar(255) | Y | | 摘要 |
| F_Description | nvarchar(max) | Y | | 内容 |
| F_SortCode | int | Y | | 排序 |
| F_IsTop | tinyint | Y | | 是否置顶 |
| F_IsHot | tinyint | Y | | 是否热门 |
| F_IsRed | tinyint | Y | | 是否推荐 |
| F_Click | int | Y | | 点击次数 |
| F_Source | nvarchar(50) | Y | | 来源 |
| F_Author | nvarchar(50) | Y | | 作者 |
| F_EnabledMark | tinyint | Y | | 是否启用 |
| F_DeleteMark | tinyint | Y | | 逻辑删除标志 |
| F_CreatorTime | datetime2(7) | Y | | 创建时间 |
| F_CreatorUserId | nvarchar(50) | Y | | 创建人 |
| F_LastModifyTime | datetime2(7) | Y | | 最后修改时间 |
| F_LastModifyUserId | nvarchar(50) | Y | | 最后修改人 |
| F_DeleteTime | datetime2(7) | Y | | 删除时间 |
| F_DeleteUserId | nvarchar(50) | Y | | 删除人 |

---

## 4. 客户关系管理模块 (crm_*)

用于管理客户订单信息。

### 4.1. 订单表 (crm_order)

| 字段名 | 数据类型 | 可空 | 主键 | 描述 |
| :--- | :--- | :--- | :--- | :--- |
| F_Id | nvarchar(50) | N | Y | 主键 |
| F_OrderCode | nvarchar(50) | Y | | 订单编号 |
| F_OrderState | int | N | | 订单状态(0未完成，1已完成) |
| F_NeedTime | datetime2(7) | Y | | 需求时间 |
| F_ActualTime | datetime2(7) | Y | | 实际完成时间 |
| F_DeleteMark | tinyint | Y | | 删除标志 |
| F_EnabledMark | tinyint | Y | | 有效标志 |
| F_Description | nvarchar(max) | Y | | 描述 |
| F_CreatorTime | datetime2(7) | Y | | 创建时间 |
| F_CreatorUserId | nvarchar(50) | Y | | 创建用户ID |
| F_CreatorUserName | nvarchar(50) | Y | | 创建用户名 |
| F_LastModifyTime | datetime2(7) | Y | | 修改时间 |
| F_LastModifyUserId | nvarchar(50) | Y | | 修改用户ID |
| F_DeleteTime | datetime2(7) | Y | | 删除时间 |
| F_DeleteUserId | nvarchar(50) | Y | | 删除用户ID |

### 4.2. 订单明细表 (crm_orderdetail)

| 字段名 | 数据类型 | 可空 | 主键 | 描述 |
| :--- | :--- | :--- | :--- | :--- |
| F_Id | nvarchar(50) | N | Y | 主键 |
| F_OrderId | nvarchar(50) | N | | 订单ID |
| F_OrderState | int | N | | 订单状态 |
| F_ProductName | nvarchar(50) | Y | | 产品名称 |
| F_ProductDescription | nvarchar(100) | Y | | 产品规格 |
| F_ProductUnit | nvarchar(5) | Y | | 产品单位 |
| F_NeedNum | int | Y | | 需求数量 |
| F_ActualNum | int | Y | | 实际数量 |
| F_Description | nvarchar(max) | Y | | 描述 |
| F_CreatorTime | datetime2(7) | Y | | 创建时间 |
| F_CreatorUserId | nvarchar(50) | Y | | 创建用户ID |
| F_CreatorUserName | nvarchar(50) | Y | | 创建用户名 |
| F_NeedTime | datetime2(7) | Y | | 需求时间 |
| F_ActualTime | datetime2(7) | Y | | 实际时间 |