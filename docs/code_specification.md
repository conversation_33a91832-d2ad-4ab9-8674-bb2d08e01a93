# WaterCloud 项目代码规范文档

## 1. 概述

本文档旨在为 WaterCloud 项目的开发提供一套统一的编码规范和最佳实践，以提高代码质量、可读性、可维护性，并促进团队协作的效率。所有项目贡献者都应遵循此规范。

---

## 2. C# 后端代码规范

### 2.1 命名规范

- **项目与命名空间 (Namespace)**: 使用帕斯卡命名法 (PascalCase)。命名空间应与项目的文件结构保持一致。
  - 示例: `WaterCloud.Service`, `WaterCloud.Domain.SystemOrganize`

- **类、接口、枚举、委托 (Classes, Interfaces, Enums, Delegates)**: 使用帕斯卡命名法 (PascalCase)。
  - 接口名称以 `I` 作为前缀。
  - 示例: `UserService`, `IUserAction`, `DbLogType`

- **方法 (Methods)**: 使用帕斯卡命名法 (PascalCase)。方法名应为动词或动词短语。
  - 异步方法以 `Async` 作为后缀。
  - 示例: `GetUserList()`, `SubmitFormAsync()`

- **属性与公共字段 (Properties & Public Fields)**: 使用帕斯卡命名法 (PascalCase)。
  - 示例: `public string UserName { get; set; }`, `public const int MaxCount = 100;`

- **私有字段 (Private Fields)**: 使用下划线 `_` 加驼峰命名法 (camelCase)。
  - 示例: `private readonly ILogger _logger;`

- **局部变量与方法参数 (Local Variables & Parameters)**: 使用驼峰命名法 (camelCase)。
  - 示例: `var userEntity = new UserEntity();`, `void GetById(string userId)`

### 2.2 编码风格

- **大括号 `{}`**: 左大括号 `{` 应放在结构声明的末尾，而不是新的一行。
  ```csharp
  public class MyClass
  {
      // 正确
  }

  // 避免
  public class MyClass
  {
  }
  ```

- **`var` 的使用**: 在变量类型明显的情况下（如 `new()`、类型转换后），推荐使用 `var` 关键字。如果类型不明显，应明确写出类型以增强可读性。
  ```csharp
  var user = new UserEntity(); // 推荐
  var users = await _service.GetList(); // 推荐
  int count = GetCount(); // 如果 GetCount() 返回类型不明确，则明确写出
  ```

- **异步编程**:
  - 核心业务逻辑、数据库和I/O操作应优先使用异步方法 (`async/await`)。
  - 避免在代码中混合使用 `.Result` 或 `.Wait()` 与 `async`，这可能导致死锁。
  - 异步方法的返回类型应为 `Task` 或 `Task<T>`。

### 2.3 架构与分层

- **严格遵守分层**:
  - `Controller` (Web层): 仅负责接收HTTP请求、参数校验和调用 `Service` 层，禁止包含业务逻辑。
  - `Service` (服务层): 核心业务逻辑的实现层，负责编排 `Repository` 或其他 `Service`。
  - `Domain` (领域层): 定义核心的实体 (`Entity`) 和值对象。
  - `Data` (数据层): 仓储模式的实现，负责直接与数据库交互。
- **依赖注入 (DI)**:
  - 所有服务（Service）和仓储（Repository）都应通过构造函数注入。
  - 在 `Controller` 和 `Service` 中，通过公共属性注入的服务（如 `public UserService _userService { get; set; }`）是本项目通过 `Autofac` 实现的特定注入方式，应保持一致。

### 2.4 注释

- **公共API**: 所有 `public` 的类和方法都应添加 `///` XML文档注释，说明其功能、参数和返回值。
  ```csharp
  /// <summary>
  /// 获取用户列表
  /// </summary>
  /// <param name="pagination">分页参数</param>
  /// <param name="keyword">关键字</param>
  /// <returns>用户列表</returns>
  public async Task<List<UserEntity>> GetUserList(Pagination pagination, string keyword)
  {
      // ...
  }
  ```
- **复杂逻辑**: 对代码中复杂的算法、业务规则或不直观的逻辑，应添加必要的行内注释 (`//`) 来解释其目的。

---

*(后续将补充前端及其他规范...)*

---

## 3. 前端代码规范 (LayUI & jQuery)

### 3.1 HTML & Razor 规范

- **结构清晰**: 使用 `layuimini-container` 和 `layuimini-main` 作为标准页面布局容器。
- **语义化标签**:
  - 搜索区域使用 `<fieldset class="table-search-fieldset">`。
  - 表单使用 `<form class="layui-form layui-form-pane">`。
- **ID与Class命名**:
  - ID (`id`) 应在页面中唯一，用于JavaScript精确选取。使用驼峰命名法 (camelCase) 或帕斯卡命名法 (PascalCase)。示例: `currentTableId`, `searchField`。
  - Class (`class`) 用于样式定义或批量选取，使用短横线命名法 (kebab-case)。示例: `search-btn`, `layui-btn-sm`。
- **模板脚本**:
  - LayUI表格的工具栏和行内按钮应使用 `<script type="text/html">` 定义，并赋予唯一的ID。示例: `toolbarDemo`, `currentTableBar`。
- **Authorize属性**: 自定义的 `authorize` 属性用于前端按钮的权限控制，应在需要权限验证的按钮上添加。

### 3.2 JavaScript 规范

- **模块化**: 使用 `layui.use([...], function(){...})` 来加载所有依赖的模块，并将核心代码包裹在回调函数中，以确保模块加载完成后再执行。
- **变量声明**:
  - 优先使用 `var` 声明变量，以保持项目一致性。
  - 在 `layui.use` 回调函数开头，缓存常用的LayUI模块和jQuery对象。
  ```javascript
  layui.use(['jquery', 'form', 'table'], function () {
      var $ = layui.jquery,
          form = layui.form,
          table = layui.table;
      // ...
  });
  ```
- **代码封装**:
  - 项目中封装了 `commonTable` 和 `common` 等自定义模块，应优先使用这些模块提供的通用方法，如 `commonTable.rendertable`, `common.modalOpen`, `common.deleteForm`。
  - 避免在页面中编写重复的AJAX请求和弹窗逻辑。
- **事件监听**:
  - 使用 `table.on('toolbar(filter)')` 和 `table.on('tool(filter)')` 来监听表格的工具栏和行内事件。
  - 使用 `form.on('submit(filter)')` 来监听表单的提交事件。
  - `return false;` 应在事件处理函数的末尾使用，以阻止表单的默认提交行为。
- **与后端交互**:
  - 所有数据请求URL应使用绝对路径，从根目录 `/` 开始。示例: `/ShiGuManage/Event/GetGridJson`。
  - 传递给后端的参数名应与后端Action方法的参数名保持一致。

### 3.3 CSS 规范

- **内联样式**: 尽量避免使用内联 `style` 属性。若必须使用，应有充分理由。
- **CSS选择器**: 优先使用Class选择器。避免使用层级过深的选择器，以提高渲染性能。
- **注释**: 对非直观的样式或CSS hack，应添加注释说明其用途。