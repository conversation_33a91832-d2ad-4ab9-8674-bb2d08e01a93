# WaterCloud 系统功能说明文档

## 1. 系统概述

WaterCloud 是一个综合性的企业信息化管理平台，旨在通过模块化的方式，为企业提供覆盖核心业务流程的管理与支持。系统以数据为驱动，围绕人员组织、业务流程、安全管理等核心领域，构建了一套完整的功能体系。

本文档基于权威的数据库结构，对系统的各项功能进行详细说明。

---

## 2. 核心功能模块

### 2.1. 系统与权限管理 (sys_*)

此模块是整个平台的基石，负责配置和管理系统的基础数据、访问控制和核心权限。

- **组织与人员管理**:
  - **组织架构 (`sys_organize`)**: 以树形结构维护公司的组织架构，包括公司、部门、班组等。是权限分配和流程流转的基础。
  - **用户管理 (`sys_user`)**: 维护系统中的所有用户账户，记录其基本信息、所属组织、角色、岗位等。支持账户的增删改查、启用/禁用、密码重置等操作。
  - **用户登录 (`sys_userlogon`)**: 管理用户的登录信息，包括密码、秘钥、登录策略（如允许多地登录、IP限制）、登录时间、密保等，与用户表一一对应。
  - **角色管理 (`sys_role`)**: 定义系统中的不同角色（如管理员、经理、普通员工），角色是权限分配的核心载体。

- **权限与访问控制**:
  - **菜单模块 (`sys_module`)**: 配置系统的导航菜单和功能模块，支持多级树形结构，并可定义每个模块的权限标识。
  - **按钮与字段权限 (`sys_modulebutton`, `sys_modulefields`)**: 实现对页面操作（按钮）和数据字段的精细化权限控制。
  - **角色授权 (`sys_roleauthorize`)**: 将菜单、按钮、字段、数据等权限项授予特定角色，建立权限模型。
  - **数据权限 (`sys_dataprivilegerule`)**: 支持配置复杂的数据权限规则，实现数据的隔离与分享。
  - **IP过滤 (`sys_filterip`)**: 可设置IP白名单或黑名单，增强系统访问安全性。

- **系统配置与维护**:
  - **数据字典 (`sys_items`, `sys_itemsdetail`)**: 维护系统中常用的枚举值和下拉选项（如状态、类型、级别等），方便统一管理和调用。
  - **系统设置 (`sys_systemset`)**: 配置系统级的参数，如系统名称、Logo、公司信息、默认管理员账户等。
  - **定时任务 (`sys_openjob`)**: 提供强大的后台任务调度功能，可配置基于 Cron 表达式的定时任务，用于数据同步、报表生成、系统维护等场景。支持记录任务执行日志 (`sys_openjoblog`)。
  - **系统日志 (`sys_log`)**: 全面记录用户的登录、操作、异常等各类日志，便于审计和问题追溯。

### 2.2. 业务流程管理 (oms_*, sys_flow*, sys_form*)

本模块提供了一套功能完善、高度灵活的可视化工作流引擎，用于设计、配置和管理企业内部的各类审批流程。

- **流程设计与配置**:
  - **流程设计 (`sys_flowscheme`)**: 提供图形化的流程设计器，允许管理员通过拖拽节点（如开始、审批、抄送、结束）来创建和修改业务流程。支持配置节点的审批人（指定人员、角色、部门负责人等）、流转条件和表单权限。
  - **表单设计 (`sys_form`)**: 支持动态创建和设计与流程绑定的电子表单，无需编码即可生成业务所需的表单界面。
  - **流程二次配置**: 针对已设计的通用流程，提供一个独立的配置界面。允许业务管理员在不接触复杂流程设计器的情况下，为特定流程的每个审批节点动态指定、修改或清空执行人员（或部门、角色）。

- **流程执行与监控**:
  - **流程实例 (`oms_flowinstance`)**: 用户根据已发布的流程模板发起新的审批申请。系统记录每个流程实例的当前状态、节点、处理人、表单数据等。
  - **流程历史 (`oms_flowinstancehis`, `oms_flowinstanceinfo`)**: 完整记录流程的每一步流转轨迹、审批意见和操作详情，实现全程可追溯。
  - **消息通知 (`oms_message`)**: 在流程流转的关键节点（如待办、驳回、完成），系统会自动向相关人员发送消息通知，并记录已读状态 (`oms_messagehis`)。

### 2.3. EHS核心业务管理 (oms_*)

此模块是 EHS（环境、健康、安全）管理的核心，用于记录、跟踪和分析生产过程中的安全事件与作业任务。

- **事故管理**:
  - **事故台账 (`oms_event`)**: 全面记录安全事故的详细信息，包括事故名称、编号、时间、等级、分类、发生单位（车间、班组）、受伤人员及详情、调查与报告详情、原因分析、采取措施等。是事故管理的核心数据表。
  - **TRCT统计 (`oms_trct`)**: 记录并计算总可记录事故率（TRCT），用于衡量企业的安全绩效。

- **作业任务管理**:
  - **任务管理 (`oms_task`)**: 统一管理各类现场作业任务，记录任务内容、类型、风险等级、负责人、执行人员、所属承包商、地理位置坐标等信息。支持主子任务结构。
  - **平面图与任务可视化 (`oms_planview`, `oms_firstleveltask`, `oms_secondarytasks`)**: 支持上传厂区平面图，并在图上标记一级和二级任务的位置，实现作业任务的直观可视化管理。

- **承包商管理**:
  - **承包商信息 (`oms_contractor`)**: 维护合作承包商的基本信息、资质、合同、评价等。
  - **承包商能力 (`oms_contractorpower`)**: 定义和管理承包商具备的各项专业能力。
  - **承包商员工 (`oms_contractorstaff`) |**: 记录承包商派驻员工的个人信息、资质和照片，并与所属承包商关联。

### 2.4. 其他业务模块

- **内容管理 (cms_*)**:
  - **文章管理 (`cms_articlecategory`, `cms_articlenews`)**: 提供一个简单的内容发布系统，可用于发布公司新闻、通知公告等。

- **客户订单管理 (crm_*)**:
  - **订单管理 (`crm_order`, `crm_orderdetail`)**: 一个基础的订单管理模块，用于记录客户订单和订单的详细产品条目。

---

## 3. 总结

WaterCloud 通过上述模块的有机结合，构建了一个以**权限管理为基础、工作流为脉络、EHS业务为核心**的综合管理平台。其数据模型设计全面，覆盖了企业运营的多个关键方面，不仅固化了管理流程，还通过数据驱动的方式，为企业的精细化运营和决策提供了有力支持。