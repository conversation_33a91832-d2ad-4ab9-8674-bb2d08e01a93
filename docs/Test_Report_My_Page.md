# “我的”页面测试报告

## 1. 测试概述

- **测试范围**: “我的”页面的 UI 及核心功能。
- **测试时间**: 2025-07-21
- **测试负责人**: Senior Developer & QA Architect
- **总体结论**: UI 布局与设计稿一致，但核心功能存在两处代码层面的问题，建议在上线前修复。

---

## 2. 测试内容与结果

### 2.1. UI 验证

| 测试项 | 预期结果 | 实际结果 | 结论 |
| :--- | :--- | :--- | :--- |
| 界面布局与样式 | [`pages/my/index.vue`](pages/my/index.vue) 的界面与UI设计稿 `docs/ui/我的/我的.png` 完全一致。 | 经过代码分析和与用户确认，UI 布局、颜色、间距等元素均与设计稿保持一致。 | **通过** |

### 2.2. 功能验证

| 模块 | 测试用例描述 | 预期结果 | 实际结果 | 结论 |
| :--- | :--- | :--- | :--- | :--- |
| 用户信息显示 | 页面应能正确从 `Vuex` store 中获取并展示当前用户的姓名和部门。 | **功能缺陷**。页面无法获取用户信息，始终显示默认的“用户名”和“部门信息”。 | **不通过** |
| 退出登录 | 点击“退出登录”按钮后，应能成功登出并跳转到登录页面。 | **功能正常但存在代码冗余**。点击后可以正常退出并跳转，但跳转逻辑在两处被重复调用。 | **通过** |

---

## 3. 问题详述与建议

### 缺陷 1：用户信息显示功能异常

- **问题描述**:
  - [`pages/my/index.vue:56`](pages/my/index.vue:56) 组件尝试通过 `...mapState('user', ['userInfo'])` 从 Vuex store 中获取用户信息。
  - 然而，在 [`store/modules/user.js:5`](store/modules/user.js:5) 中，存储用户信息的 state 属性名为 `user`，而非 `userInfo`。
- **根本原因**: `mapState` 辅助函数因名称不匹配而无法找到对应的 state，导致组件内的 `userInfo` 计算属性始终为 `undefined`。
- **修复建议**:
  - **方案一 (推荐)**: 修改 [`pages/my/index.vue:56`](pages/my/index.vue:56)，将 `...mapState('user', ['userInfo'])` 改为 `...mapState({ userInfo: state => state.user.user })`。
  - **方案二**: 修改 [`store/modules/user.js:5`](store/modules/user.js:5)，将 `user: null` 改为 `userInfo: null`，并更新所有对 `state.user` 的引用。

### 缺陷 2：退出登录功能存在代码冗余

- **问题描述**:
  - [`pages/my/index.vue:62`](pages/my/index.vue:62) 中的 `handleLogout` 方法在调用 Vuex 的 `logout` action 后，又执行了一次 `uni.reLaunch` 跳转到登录页。
  - [`store/modules/user.js:147`](store/modules/user.js:147) 中的 `logout` action 本身已经包含了 `uni.reLaunch` 的逻辑。
- **根本原因**: 页面和 Vuex store 中存在重复的页面跳转逻辑。
- **修复建议**:
  - 移除 [`pages/my/index.vue:62-64`](pages/my/index.vue:62-64) 中的 `uni.reLaunch` 调用，仅保留 `this.logout()`。

---