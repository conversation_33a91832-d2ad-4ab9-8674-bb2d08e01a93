/*
 Navicat Premium Dump SQL

 Source Server         : 白各庄
 Source Server Type    : SQL Server
 Source Server Version : 16004175 (16.00.4175)
 Source Host           : *************:9001
 Source Catalog        : WaterCloudNetDb
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 16004175 (16.00.4175)
 File Encoding         : 65001

 Date: 15/07/2025 09:15:37
*/


-- ----------------------------
-- Table structure for cms_articlecategory
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[cms_articlecategory]') AND type IN ('U'))
	DROP TABLE [dbo].[cms_articlecategory]
GO

CREATE TABLE [dbo].[cms_articlecategory] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_FullName] nvarchar(100) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_ParentId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_SortCode] int  NOT NULL,
  [F_Description] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_LinkUrl] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_ImgUrl] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_SeoTitle] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_SeoKeywords] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_SeoDescription] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_IsHot] tinyint  NULL,
  [F_EnabledMark] tinyint  NULL,
  [F_DeleteMark] tinyint  NULL,
  [F_CreatorTime] datetime2(7)  NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_LastModifyTime] datetime2(7)  NULL,
  [F_LastModifyUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DeleteTime] datetime2(7)  NULL,
  [F_DeleteUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[cms_articlecategory] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'主键Id',
'SCHEMA', N'dbo',
'TABLE', N'cms_articlecategory',
'COLUMN', N'F_Id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'类别名称',
'SCHEMA', N'dbo',
'TABLE', N'cms_articlecategory',
'COLUMN', N'F_FullName'
GO

EXEC sp_addextendedproperty
'MS_Description', N'父级Id',
'SCHEMA', N'dbo',
'TABLE', N'cms_articlecategory',
'COLUMN', N'F_ParentId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'排序',
'SCHEMA', N'dbo',
'TABLE', N'cms_articlecategory',
'COLUMN', N'F_SortCode'
GO

EXEC sp_addextendedproperty
'MS_Description', N'描述',
'SCHEMA', N'dbo',
'TABLE', N'cms_articlecategory',
'COLUMN', N'F_Description'
GO

EXEC sp_addextendedproperty
'MS_Description', N'链接地址',
'SCHEMA', N'dbo',
'TABLE', N'cms_articlecategory',
'COLUMN', N'F_LinkUrl'
GO

EXEC sp_addextendedproperty
'MS_Description', N'图片地址',
'SCHEMA', N'dbo',
'TABLE', N'cms_articlecategory',
'COLUMN', N'F_ImgUrl'
GO

EXEC sp_addextendedproperty
'MS_Description', N'SEO标题',
'SCHEMA', N'dbo',
'TABLE', N'cms_articlecategory',
'COLUMN', N'F_SeoTitle'
GO

EXEC sp_addextendedproperty
'MS_Description', N'SEO关键字',
'SCHEMA', N'dbo',
'TABLE', N'cms_articlecategory',
'COLUMN', N'F_SeoKeywords'
GO

EXEC sp_addextendedproperty
'MS_Description', N'SEO描述',
'SCHEMA', N'dbo',
'TABLE', N'cms_articlecategory',
'COLUMN', N'F_SeoDescription'
GO

EXEC sp_addextendedproperty
'MS_Description', N'是否热门',
'SCHEMA', N'dbo',
'TABLE', N'cms_articlecategory',
'COLUMN', N'F_IsHot'
GO

EXEC sp_addextendedproperty
'MS_Description', N'是否启用',
'SCHEMA', N'dbo',
'TABLE', N'cms_articlecategory',
'COLUMN', N'F_EnabledMark'
GO

EXEC sp_addextendedproperty
'MS_Description', N'删除标志',
'SCHEMA', N'dbo',
'TABLE', N'cms_articlecategory',
'COLUMN', N'F_DeleteMark'
GO


-- ----------------------------
-- Table structure for cms_articlenews
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[cms_articlenews]') AND type IN ('U'))
	DROP TABLE [dbo].[cms_articlenews]
GO

CREATE TABLE [dbo].[cms_articlenews] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_CategoryId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_Title] nvarchar(200) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_LinkUrl] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_ImgUrl] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_SeoTitle] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_SeoKeywords] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_SeoDescription] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Tags] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Zhaiyao] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Description] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_SortCode] int  NULL,
  [F_IsTop] tinyint  NULL,
  [F_IsHot] tinyint  NULL,
  [F_IsRed] tinyint  NULL,
  [F_Click] int  NULL,
  [F_Source] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Author] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_EnabledMark] tinyint  NULL,
  [F_DeleteMark] tinyint  NULL,
  [F_CreatorTime] datetime2(7)  NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_LastModifyTime] datetime2(7)  NULL,
  [F_LastModifyUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DeleteTime] datetime2(7)  NULL,
  [F_DeleteUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[cms_articlenews] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'文章主键Id',
'SCHEMA', N'dbo',
'TABLE', N'cms_articlenews',
'COLUMN', N'F_Id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'类别Id',
'SCHEMA', N'dbo',
'TABLE', N'cms_articlenews',
'COLUMN', N'F_CategoryId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'标题',
'SCHEMA', N'dbo',
'TABLE', N'cms_articlenews',
'COLUMN', N'F_Title'
GO

EXEC sp_addextendedproperty
'MS_Description', N'链接地址',
'SCHEMA', N'dbo',
'TABLE', N'cms_articlenews',
'COLUMN', N'F_LinkUrl'
GO

EXEC sp_addextendedproperty
'MS_Description', N'图片地址',
'SCHEMA', N'dbo',
'TABLE', N'cms_articlenews',
'COLUMN', N'F_ImgUrl'
GO

EXEC sp_addextendedproperty
'MS_Description', N'SEO标题',
'SCHEMA', N'dbo',
'TABLE', N'cms_articlenews',
'COLUMN', N'F_SeoTitle'
GO

EXEC sp_addextendedproperty
'MS_Description', N'SEO关键字',
'SCHEMA', N'dbo',
'TABLE', N'cms_articlenews',
'COLUMN', N'F_SeoKeywords'
GO

EXEC sp_addextendedproperty
'MS_Description', N'SEO描述',
'SCHEMA', N'dbo',
'TABLE', N'cms_articlenews',
'COLUMN', N'F_SeoDescription'
GO

EXEC sp_addextendedproperty
'MS_Description', N'标签',
'SCHEMA', N'dbo',
'TABLE', N'cms_articlenews',
'COLUMN', N'F_Tags'
GO

EXEC sp_addextendedproperty
'MS_Description', N'摘要',
'SCHEMA', N'dbo',
'TABLE', N'cms_articlenews',
'COLUMN', N'F_Zhaiyao'
GO

EXEC sp_addextendedproperty
'MS_Description', N'内容',
'SCHEMA', N'dbo',
'TABLE', N'cms_articlenews',
'COLUMN', N'F_Description'
GO

EXEC sp_addextendedproperty
'MS_Description', N'排序',
'SCHEMA', N'dbo',
'TABLE', N'cms_articlenews',
'COLUMN', N'F_SortCode'
GO

EXEC sp_addextendedproperty
'MS_Description', N'是否置顶',
'SCHEMA', N'dbo',
'TABLE', N'cms_articlenews',
'COLUMN', N'F_IsTop'
GO

EXEC sp_addextendedproperty
'MS_Description', N'是否热门',
'SCHEMA', N'dbo',
'TABLE', N'cms_articlenews',
'COLUMN', N'F_IsHot'
GO

EXEC sp_addextendedproperty
'MS_Description', N'点击次数',
'SCHEMA', N'dbo',
'TABLE', N'cms_articlenews',
'COLUMN', N'F_Click'
GO

EXEC sp_addextendedproperty
'MS_Description', N'来源',
'SCHEMA', N'dbo',
'TABLE', N'cms_articlenews',
'COLUMN', N'F_Source'
GO

EXEC sp_addextendedproperty
'MS_Description', N'作者',
'SCHEMA', N'dbo',
'TABLE', N'cms_articlenews',
'COLUMN', N'F_Author'
GO

EXEC sp_addextendedproperty
'MS_Description', N'是否启用',
'SCHEMA', N'dbo',
'TABLE', N'cms_articlenews',
'COLUMN', N'F_EnabledMark'
GO

EXEC sp_addextendedproperty
'MS_Description', N'逻辑删除标志',
'SCHEMA', N'dbo',
'TABLE', N'cms_articlenews',
'COLUMN', N'F_DeleteMark'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'cms_articlenews',
'COLUMN', N'F_CreatorTime'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建人',
'SCHEMA', N'dbo',
'TABLE', N'cms_articlenews',
'COLUMN', N'F_CreatorUserId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'最后修改时间',
'SCHEMA', N'dbo',
'TABLE', N'cms_articlenews',
'COLUMN', N'F_LastModifyTime'
GO

EXEC sp_addextendedproperty
'MS_Description', N'最后修改人',
'SCHEMA', N'dbo',
'TABLE', N'cms_articlenews',
'COLUMN', N'F_LastModifyUserId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'删除时间',
'SCHEMA', N'dbo',
'TABLE', N'cms_articlenews',
'COLUMN', N'F_DeleteTime'
GO

EXEC sp_addextendedproperty
'MS_Description', N'删除人',
'SCHEMA', N'dbo',
'TABLE', N'cms_articlenews',
'COLUMN', N'F_DeleteUserId'
GO


-- ----------------------------
-- Table structure for crm_order
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[crm_order]') AND type IN ('U'))
	DROP TABLE [dbo].[crm_order]
GO

CREATE TABLE [dbo].[crm_order] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_OrderCode] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_OrderState] int  NOT NULL,
  [F_NeedTime] datetime2(7)  NULL,
  [F_ActualTime] datetime2(7)  NULL,
  [F_DeleteMark] tinyint  NULL,
  [F_EnabledMark] tinyint  NULL,
  [F_Description] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorTime] datetime2(7)  NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorUserName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_LastModifyTime] datetime2(7)  NULL,
  [F_LastModifyUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DeleteTime] datetime2(7)  NULL,
  [F_DeleteUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[crm_order] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'主键',
'SCHEMA', N'dbo',
'TABLE', N'crm_order',
'COLUMN', N'F_Id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'订单编号',
'SCHEMA', N'dbo',
'TABLE', N'crm_order',
'COLUMN', N'F_OrderCode'
GO

EXEC sp_addextendedproperty
'MS_Description', N'订单状态(0未完成，1已完成)',
'SCHEMA', N'dbo',
'TABLE', N'crm_order',
'COLUMN', N'F_OrderState'
GO

EXEC sp_addextendedproperty
'MS_Description', N'需求时间',
'SCHEMA', N'dbo',
'TABLE', N'crm_order',
'COLUMN', N'F_NeedTime'
GO

EXEC sp_addextendedproperty
'MS_Description', N'实际完成时间',
'SCHEMA', N'dbo',
'TABLE', N'crm_order',
'COLUMN', N'F_ActualTime'
GO


-- ----------------------------
-- Table structure for crm_orderdetail
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[crm_orderdetail]') AND type IN ('U'))
	DROP TABLE [dbo].[crm_orderdetail]
GO

CREATE TABLE [dbo].[crm_orderdetail] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_OrderId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_OrderState] int  NOT NULL,
  [F_ProductName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_ProductDescription] nvarchar(100) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_ProductUnit] nvarchar(5) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_NeedNum] int  NULL,
  [F_ActualNum] int  NULL,
  [F_Description] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorTime] datetime2(7)  NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorUserName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_NeedTime] datetime2(7)  NULL,
  [F_ActualTime] datetime2(7)  NULL
)
GO

ALTER TABLE [dbo].[crm_orderdetail] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'主键',
'SCHEMA', N'dbo',
'TABLE', N'crm_orderdetail',
'COLUMN', N'F_Id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'订单状态',
'SCHEMA', N'dbo',
'TABLE', N'crm_orderdetail',
'COLUMN', N'F_OrderState'
GO

EXEC sp_addextendedproperty
'MS_Description', N'产品名称',
'SCHEMA', N'dbo',
'TABLE', N'crm_orderdetail',
'COLUMN', N'F_ProductName'
GO

EXEC sp_addextendedproperty
'MS_Description', N'产品规格',
'SCHEMA', N'dbo',
'TABLE', N'crm_orderdetail',
'COLUMN', N'F_ProductDescription'
GO

EXEC sp_addextendedproperty
'MS_Description', N'产品单位',
'SCHEMA', N'dbo',
'TABLE', N'crm_orderdetail',
'COLUMN', N'F_ProductUnit'
GO

EXEC sp_addextendedproperty
'MS_Description', N'需求数量',
'SCHEMA', N'dbo',
'TABLE', N'crm_orderdetail',
'COLUMN', N'F_NeedNum'
GO

EXEC sp_addextendedproperty
'MS_Description', N'实际数量',
'SCHEMA', N'dbo',
'TABLE', N'crm_orderdetail',
'COLUMN', N'F_ActualNum'
GO

EXEC sp_addextendedproperty
'MS_Description', N'需求时间',
'SCHEMA', N'dbo',
'TABLE', N'crm_orderdetail',
'COLUMN', N'F_NeedTime'
GO

EXEC sp_addextendedproperty
'MS_Description', N'实际时间',
'SCHEMA', N'dbo',
'TABLE', N'crm_orderdetail',
'COLUMN', N'F_ActualTime'
GO


-- ----------------------------
-- Table structure for oms_contractor
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[oms_contractor]') AND type IN ('U'))
	DROP TABLE [dbo].[oms_contractor]
GO

CREATE TABLE [dbo].[oms_contractor] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_CompanyName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_Address] nvarchar(100) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_TypeName] nvarchar(20) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_HeadName] nvarchar(20) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_ContactsName] nvarchar(20) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_ContactsTel] nvarchar(20) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_ServeId] varchar(200) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_StartTime] datetime2(7)  NULL,
  [F_EndTime] datetime2(7)  NULL,
  [F_EnabledMark] tinyint  NULL,
  [F_SignName] nvarchar(20) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_Remark] nvarchar(100) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Evaluate] nvarchar(200) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_License] varchar(100) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DeleteMark] tinyint  NULL,
  [F_Description] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorTime] datetime2(7)  NOT NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorUserName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_LastModifyTime] datetime2(7)  NULL,
  [F_LastModifyUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Num] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[oms_contractor] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Table structure for oms_contractorpower
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[oms_contractorpower]') AND type IN ('U'))
	DROP TABLE [dbo].[oms_contractorpower]
GO

CREATE TABLE [dbo].[oms_contractorpower] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_PowerName] nvarchar(20) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_DeleteMark] tinyint  NULL,
  [F_Description] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorTime] datetime2(7)  NOT NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorUserName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_LastModifyTime] datetime2(7)  NULL,
  [F_LastModifyUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[oms_contractorpower] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'ID',
'SCHEMA', N'dbo',
'TABLE', N'oms_contractorpower',
'COLUMN', N'F_Id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'承包商能力',
'SCHEMA', N'dbo',
'TABLE', N'oms_contractorpower',
'COLUMN', N'F_PowerName'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'oms_contractorpower',
'COLUMN', N'F_CreatorTime'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建用户主键',
'SCHEMA', N'dbo',
'TABLE', N'oms_contractorpower',
'COLUMN', N'F_CreatorUserId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建用户',
'SCHEMA', N'dbo',
'TABLE', N'oms_contractorpower',
'COLUMN', N'F_CreatorUserName'
GO

EXEC sp_addextendedproperty
'MS_Description', N'承包商能力表',
'SCHEMA', N'dbo',
'TABLE', N'oms_contractorpower'
GO


-- ----------------------------
-- Table structure for oms_contractorstaff
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[oms_contractorstaff]') AND type IN ('U'))
	DROP TABLE [dbo].[oms_contractorstaff]
GO

CREATE TABLE [dbo].[oms_contractorstaff] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_ContractorId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_Name] nvarchar(20) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_Tel] varchar(20) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Remark] nvarchar(100) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DeleteMark] tinyint  NULL,
  [F_CreatorTime] datetime2(7)  NOT NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorUserName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Photo] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_LastModifyTime] datetime2(7)  NULL,
  [F_LastModifyUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_EnabledMark] tinyint DEFAULT 1 NULL
)
GO

ALTER TABLE [dbo].[oms_contractorstaff] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'禁用状态1正常0禁用',
'SCHEMA', N'dbo',
'TABLE', N'oms_contractorstaff',
'COLUMN', N'F_EnabledMark'
GO


-- ----------------------------
-- Table structure for oms_event
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[oms_event]') AND type IN ('U'))
	DROP TABLE [dbo].[oms_event]
GO

CREATE TABLE [dbo].[oms_event] (
  [F_Id] varchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS  NOT NULL,
  [F_Name] nvarchar(100) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [F_Num] varchar(80) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [F_Description] nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [F_Date] datetime2(7)  NULL,
  [F_Level] nvarchar(20) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [F_Category] nvarchar(20) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [F_BMU] nvarchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [F_Class] nvarchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [F_BPU] nvarchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [F_Surname] nvarchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [F_ResearchUser] nvarchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [F_Actions] nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [F_Sketch] nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [F_DeleteMark] tinyint  NULL,
  [F_EnabledMark] tinyint  NULL,
  [F_CreatorTime] datetime2(7)  NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorUserName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_LastModifyTime] datetime2(7)  NULL,
  [F_LastModifyUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DeleteTime] datetime2(7)  NULL,
  [F_DeleteUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_ReportUser] nvarchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [F_ReportUserNum] nvarchar(64) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [F_ReportFile] nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [F_Reason] nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [F_ActionDesc] nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [F_Status] tinyint DEFAULT 0 NULL,
  [F_Surno] nvarchar(50) COLLATE Chinese_PRC_CI_AI DEFAULT NULL NULL,
  [F_Surage] tinyint  NULL,
  [F_SurBuWei] nvarchar(100) COLLATE Chinese_PRC_CI_AI DEFAULT NULL NULL,
  [F_SurType] nvarchar(50) COLLATE Chinese_PRC_CI_AI DEFAULT NULL NULL,
  [F_HighRisk] tinyint  NULL,
  [F_MeasureGroup] nvarchar(max) COLLATE Chinese_PRC_CI_AI  NULL,
  [F_Correction] tinyint  NULL,
  [F_JobTime] nvarchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [F_SurJoinTime] nvarchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[oms_event] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'事故ID',
'SCHEMA', N'dbo',
'TABLE', N'oms_event',
'COLUMN', N'F_Id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'事故名称',
'SCHEMA', N'dbo',
'TABLE', N'oms_event',
'COLUMN', N'F_Name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'事故编号',
'SCHEMA', N'dbo',
'TABLE', N'oms_event',
'COLUMN', N'F_Num'
GO

EXEC sp_addextendedproperty
'MS_Description', N'事故描述',
'SCHEMA', N'dbo',
'TABLE', N'oms_event',
'COLUMN', N'F_Description'
GO

EXEC sp_addextendedproperty
'MS_Description', N'事故时间',
'SCHEMA', N'dbo',
'TABLE', N'oms_event',
'COLUMN', N'F_Date'
GO

EXEC sp_addextendedproperty
'MS_Description', N'事故等级',
'SCHEMA', N'dbo',
'TABLE', N'oms_event',
'COLUMN', N'F_Level'
GO

EXEC sp_addextendedproperty
'MS_Description', N'事故分类',
'SCHEMA', N'dbo',
'TABLE', N'oms_event',
'COLUMN', N'F_Category'
GO

EXEC sp_addextendedproperty
'MS_Description', N'车间',
'SCHEMA', N'dbo',
'TABLE', N'oms_event',
'COLUMN', N'F_BMU'
GO

EXEC sp_addextendedproperty
'MS_Description', N'班组',
'SCHEMA', N'dbo',
'TABLE', N'oms_event',
'COLUMN', N'F_Class'
GO

EXEC sp_addextendedproperty
'MS_Description', N'BPU',
'SCHEMA', N'dbo',
'TABLE', N'oms_event',
'COLUMN', N'F_BPU'
GO

EXEC sp_addextendedproperty
'MS_Description', N'手上人员',
'SCHEMA', N'dbo',
'TABLE', N'oms_event',
'COLUMN', N'F_Surname'
GO

EXEC sp_addextendedproperty
'MS_Description', N'调查人员',
'SCHEMA', N'dbo',
'TABLE', N'oms_event',
'COLUMN', N'F_ResearchUser'
GO

EXEC sp_addextendedproperty
'MS_Description', N'立即措施',
'SCHEMA', N'dbo',
'TABLE', N'oms_event',
'COLUMN', N'F_Actions'
GO

EXEC sp_addextendedproperty
'MS_Description', N'草图或附件',
'SCHEMA', N'dbo',
'TABLE', N'oms_event',
'COLUMN', N'F_Sketch'
GO

EXEC sp_addextendedproperty
'MS_Description', N'报告人',
'SCHEMA', N'dbo',
'TABLE', N'oms_event',
'COLUMN', N'F_ReportUser'
GO

EXEC sp_addextendedproperty
'MS_Description', N'报告人工号',
'SCHEMA', N'dbo',
'TABLE', N'oms_event',
'COLUMN', N'F_ReportUserNum'
GO

EXEC sp_addextendedproperty
'MS_Description', N'事故报告',
'SCHEMA', N'dbo',
'TABLE', N'oms_event',
'COLUMN', N'F_ReportFile'
GO

EXEC sp_addextendedproperty
'MS_Description', N'原因分析',
'SCHEMA', N'dbo',
'TABLE', N'oms_event',
'COLUMN', N'F_Reason'
GO

EXEC sp_addextendedproperty
'MS_Description', N'行动措施',
'SCHEMA', N'dbo',
'TABLE', N'oms_event',
'COLUMN', N'F_ActionDesc'
GO

EXEC sp_addextendedproperty
'MS_Description', N'报告状态（0未上传1已上传）',
'SCHEMA', N'dbo',
'TABLE', N'oms_event',
'COLUMN', N'F_Status'
GO

EXEC sp_addextendedproperty
'MS_Description', N'受伤人员工号',
'SCHEMA', N'dbo',
'TABLE', N'oms_event',
'COLUMN', N'F_Surno'
GO

EXEC sp_addextendedproperty
'MS_Description', N'受伤人员年龄',
'SCHEMA', N'dbo',
'TABLE', N'oms_event',
'COLUMN', N'F_Surage'
GO

EXEC sp_addextendedproperty
'MS_Description', N'受伤部位',
'SCHEMA', N'dbo',
'TABLE', N'oms_event',
'COLUMN', N'F_SurBuWei'
GO

EXEC sp_addextendedproperty
'MS_Description', N'受伤部位',
'SCHEMA', N'dbo',
'TABLE', N'oms_event',
'COLUMN', N'F_SurType'
GO

EXEC sp_addextendedproperty
'MS_Description', N'高潜风险0否1是',
'SCHEMA', N'dbo',
'TABLE', N'oms_event',
'COLUMN', N'F_HighRisk'
GO

EXEC sp_addextendedproperty
'MS_Description', N'措施组',
'SCHEMA', N'dbo',
'TABLE', N'oms_event',
'COLUMN', N'F_MeasureGroup'
GO

EXEC sp_addextendedproperty
'MS_Description', N'是否整改0未整改1已整改',
'SCHEMA', N'dbo',
'TABLE', N'oms_event',
'COLUMN', N'F_Correction'
GO

EXEC sp_addextendedproperty
'MS_Description', N'事故信息表',
'SCHEMA', N'dbo',
'TABLE', N'oms_event'
GO


-- ----------------------------
-- Table structure for oms_firstleveltask
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[oms_firstleveltask]') AND type IN ('U'))
	DROP TABLE [dbo].[oms_firstleveltask]
GO

CREATE TABLE [dbo].[oms_firstleveltask] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_PlanviewId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_ContractorId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_ContractorstaffId] varchar(2000) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_Name] nvarchar(20) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_Path] varchar(20) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_Remark] nvarchar(100) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_DeleteMark] tinyint  NULL,
  [F_CreatorTime] datetime2(7)  NOT NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorUserName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[oms_firstleveltask] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Table structure for oms_flowinstance
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[oms_flowinstance]') AND type IN ('U'))
	DROP TABLE [dbo].[oms_flowinstance]
GO

CREATE TABLE [dbo].[oms_flowinstance] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_InstanceSchemeId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_Code] nvarchar(200) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CustomName] nvarchar(200) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_ActivityId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_ActivityType] int  NULL,
  [F_ActivityName] nvarchar(200) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_PreviousId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_SchemeContent] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_SchemeId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DbName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_FrmData] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_FrmType] int  NOT NULL,
  [F_FrmContentData] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_FrmContentParse] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_FrmId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_SchemeType] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_FlowLevel] int  NOT NULL,
  [F_Description] nvarchar(200) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_IsFinish] int  NOT NULL,
  [F_MakerList] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_OrganizeId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_EnabledMark] tinyint  NULL,
  [F_CreatorTime] datetime2(7)  NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorUserName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_FrmContent] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[oms_flowinstance] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'主键Id',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstance',
'COLUMN', N'F_Id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'流程实例模板Id',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstance',
'COLUMN', N'F_InstanceSchemeId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'实例编号',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstance',
'COLUMN', N'F_Code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'自定义名称',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstance',
'COLUMN', N'F_CustomName'
GO

EXEC sp_addextendedproperty
'MS_Description', N'当前节点ID',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstance',
'COLUMN', N'F_ActivityId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'当前节点类型（0会签节点）',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstance',
'COLUMN', N'F_ActivityType'
GO

EXEC sp_addextendedproperty
'MS_Description', N'当前节点名称',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstance',
'COLUMN', N'F_ActivityName'
GO

EXEC sp_addextendedproperty
'MS_Description', N'前一个ID',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstance',
'COLUMN', N'F_PreviousId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'流程模板内容',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstance',
'COLUMN', N'F_SchemeContent'
GO

EXEC sp_addextendedproperty
'MS_Description', N'流程模板ID',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstance',
'COLUMN', N'F_SchemeId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'数据库名称',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstance',
'COLUMN', N'F_DbName'
GO

EXEC sp_addextendedproperty
'MS_Description', N'表单数据',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstance',
'COLUMN', N'F_FrmData'
GO

EXEC sp_addextendedproperty
'MS_Description', N'表单类型',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstance',
'COLUMN', N'F_FrmType'
GO

EXEC sp_addextendedproperty
'MS_Description', N'表单字段',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstance',
'COLUMN', N'F_FrmContentData'
GO

EXEC sp_addextendedproperty
'MS_Description', N'表单参数（冗余）',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstance',
'COLUMN', N'F_FrmContentParse'
GO

EXEC sp_addextendedproperty
'MS_Description', N'表单ID',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstance',
'COLUMN', N'F_FrmId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'流程类型',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstance',
'COLUMN', N'F_SchemeType'
GO

EXEC sp_addextendedproperty
'MS_Description', N'等级',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstance',
'COLUMN', N'F_FlowLevel'
GO

EXEC sp_addextendedproperty
'MS_Description', N'实例备注',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstance',
'COLUMN', N'F_Description'
GO

EXEC sp_addextendedproperty
'MS_Description', N'是否完成',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstance',
'COLUMN', N'F_IsFinish'
GO

EXEC sp_addextendedproperty
'MS_Description', N'执行人',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstance',
'COLUMN', N'F_MakerList'
GO

EXEC sp_addextendedproperty
'MS_Description', N'所属部门',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstance',
'COLUMN', N'F_OrganizeId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'有效',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstance',
'COLUMN', N'F_EnabledMark'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstance',
'COLUMN', N'F_CreatorTime'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建用户主键',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstance',
'COLUMN', N'F_CreatorUserId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建用户',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstance',
'COLUMN', N'F_CreatorUserName'
GO

EXEC sp_addextendedproperty
'MS_Description', N'表单元素json',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstance',
'COLUMN', N'F_FrmContent'
GO

EXEC sp_addextendedproperty
'MS_Description', N'工作流流程实例表',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstance'
GO


-- ----------------------------
-- Table structure for oms_flowinstancehis
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[oms_flowinstancehis]') AND type IN ('U'))
	DROP TABLE [dbo].[oms_flowinstancehis]
GO

CREATE TABLE [dbo].[oms_flowinstancehis] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_InstanceId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_FromNodeId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_FromNodeType] int  NULL,
  [F_FromNodeName] nvarchar(200) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_ToNodeId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_ToNodeType] int  NULL,
  [F_ToNodeName] nvarchar(200) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_TransitionSate] tinyint  NOT NULL,
  [F_IsFinish] tinyint  NOT NULL,
  [F_CreatorTime] datetime2(7)  NOT NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorUserName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[oms_flowinstancehis] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'主键Id',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstancehis',
'COLUMN', N'F_Id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'实例Id',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstancehis',
'COLUMN', N'F_InstanceId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'开始节点Id',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstancehis',
'COLUMN', N'F_FromNodeId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'开始节点类型',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstancehis',
'COLUMN', N'F_FromNodeType'
GO

EXEC sp_addextendedproperty
'MS_Description', N'开始节点名称',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstancehis',
'COLUMN', N'F_FromNodeName'
GO

EXEC sp_addextendedproperty
'MS_Description', N'结束节点Id',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstancehis',
'COLUMN', N'F_ToNodeId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'结束节点类型',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstancehis',
'COLUMN', N'F_ToNodeType'
GO

EXEC sp_addextendedproperty
'MS_Description', N'结束节点名称',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstancehis',
'COLUMN', N'F_ToNodeName'
GO

EXEC sp_addextendedproperty
'MS_Description', N'转化状态',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstancehis',
'COLUMN', N'F_TransitionSate'
GO

EXEC sp_addextendedproperty
'MS_Description', N'是否结束',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstancehis',
'COLUMN', N'F_IsFinish'
GO

EXEC sp_addextendedproperty
'MS_Description', N'转化时间',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstancehis',
'COLUMN', N'F_CreatorTime'
GO

EXEC sp_addextendedproperty
'MS_Description', N'操作人Id',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstancehis',
'COLUMN', N'F_CreatorUserId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'操作人名称',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstancehis',
'COLUMN', N'F_CreatorUserName'
GO

EXEC sp_addextendedproperty
'MS_Description', N'工作流实例流转历史记录',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstancehis'
GO


-- ----------------------------
-- Table structure for oms_flowinstanceinfo
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[oms_flowinstanceinfo]') AND type IN ('U'))
	DROP TABLE [dbo].[oms_flowinstanceinfo]
GO

CREATE TABLE [dbo].[oms_flowinstanceinfo] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_InstanceId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_Content] nvarchar(200) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorTime] datetime2(7)  NOT NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorUserName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_FrmData] nvarchar(1000) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[oms_flowinstanceinfo] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'主键Id',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstanceinfo',
'COLUMN', N'F_Id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'实例进程Id',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstanceinfo',
'COLUMN', N'F_InstanceId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'操作内容',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstanceinfo',
'COLUMN', N'F_Content'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstanceinfo',
'COLUMN', N'F_CreatorTime'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建用户主键',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstanceinfo',
'COLUMN', N'F_CreatorUserId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建用户',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstanceinfo',
'COLUMN', N'F_CreatorUserName'
GO

EXEC sp_addextendedproperty
'MS_Description', N'表单数据',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstanceinfo',
'COLUMN', N'F_FrmData'
GO

EXEC sp_addextendedproperty
'MS_Description', N'工作流实例操作记录',
'SCHEMA', N'dbo',
'TABLE', N'oms_flowinstanceinfo'
GO


-- ----------------------------
-- Table structure for oms_formtest
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[oms_formtest]') AND type IN ('U'))
	DROP TABLE [dbo].[oms_formtest]
GO

CREATE TABLE [dbo].[oms_formtest] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_UserName] nvarchar(10) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_RequestType] nvarchar(20) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_StartTime] datetime2(7)  NULL,
  [F_EndTime] datetime2(7)  NULL,
  [F_RequestComment] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Attachment] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorTime] datetime2(7)  NOT NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorUserName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_FlowInstanceId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[oms_formtest] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'ID',
'SCHEMA', N'dbo',
'TABLE', N'oms_formtest',
'COLUMN', N'F_Id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'请假人姓名',
'SCHEMA', N'dbo',
'TABLE', N'oms_formtest',
'COLUMN', N'F_UserName'
GO

EXEC sp_addextendedproperty
'MS_Description', N'请假分类，病假，事假，公休等',
'SCHEMA', N'dbo',
'TABLE', N'oms_formtest',
'COLUMN', N'F_RequestType'
GO

EXEC sp_addextendedproperty
'MS_Description', N'开始时间',
'SCHEMA', N'dbo',
'TABLE', N'oms_formtest',
'COLUMN', N'F_StartTime'
GO

EXEC sp_addextendedproperty
'MS_Description', N'结束时间',
'SCHEMA', N'dbo',
'TABLE', N'oms_formtest',
'COLUMN', N'F_EndTime'
GO

EXEC sp_addextendedproperty
'MS_Description', N'请假说明',
'SCHEMA', N'dbo',
'TABLE', N'oms_formtest',
'COLUMN', N'F_RequestComment'
GO

EXEC sp_addextendedproperty
'MS_Description', N'附件，用于提交病假证据等',
'SCHEMA', N'dbo',
'TABLE', N'oms_formtest',
'COLUMN', N'F_Attachment'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'oms_formtest',
'COLUMN', N'F_CreatorTime'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建用户主键',
'SCHEMA', N'dbo',
'TABLE', N'oms_formtest',
'COLUMN', N'F_CreatorUserId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建用户',
'SCHEMA', N'dbo',
'TABLE', N'oms_formtest',
'COLUMN', N'F_CreatorUserName'
GO

EXEC sp_addextendedproperty
'MS_Description', N'所属流程ID',
'SCHEMA', N'dbo',
'TABLE', N'oms_formtest',
'COLUMN', N'F_FlowInstanceId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'模拟一个自定页面的表单，该数据会关联到流程实例FrmData，可用于复杂页面的设计及后期的数据分析',
'SCHEMA', N'dbo',
'TABLE', N'oms_formtest'
GO


-- ----------------------------
-- Table structure for oms_message
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[oms_message]') AND type IN ('U'))
	DROP TABLE [dbo].[oms_message]
GO

CREATE TABLE [dbo].[oms_message] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_MessageType] int  NULL,
  [F_ToUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_ToUserName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_MessageInfo] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_EnabledMark] tinyint  NULL,
  [F_CreatorTime] datetime2(7)  NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorUserName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_HrefTarget] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Href] nvarchar(100) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_KeyValue] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_ClickRead] tinyint  NULL
)
GO

ALTER TABLE [dbo].[oms_message] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'主键Id',
'SCHEMA', N'dbo',
'TABLE', N'oms_message',
'COLUMN', N'F_Id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'信息类型（通知、私信、处理）',
'SCHEMA', N'dbo',
'TABLE', N'oms_message',
'COLUMN', N'F_MessageType'
GO

EXEC sp_addextendedproperty
'MS_Description', N'收件人主键',
'SCHEMA', N'dbo',
'TABLE', N'oms_message',
'COLUMN', N'F_ToUserId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'收件人',
'SCHEMA', N'dbo',
'TABLE', N'oms_message',
'COLUMN', N'F_ToUserName'
GO

EXEC sp_addextendedproperty
'MS_Description', N'内容',
'SCHEMA', N'dbo',
'TABLE', N'oms_message',
'COLUMN', N'F_MessageInfo'
GO

EXEC sp_addextendedproperty
'MS_Description', N'有效',
'SCHEMA', N'dbo',
'TABLE', N'oms_message',
'COLUMN', N'F_EnabledMark'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'oms_message',
'COLUMN', N'F_CreatorTime'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建用户主键',
'SCHEMA', N'dbo',
'TABLE', N'oms_message',
'COLUMN', N'F_CreatorUserId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建用户',
'SCHEMA', N'dbo',
'TABLE', N'oms_message',
'COLUMN', N'F_CreatorUserName'
GO

EXEC sp_addextendedproperty
'MS_Description', N'跳转类型',
'SCHEMA', N'dbo',
'TABLE', N'oms_message',
'COLUMN', N'F_HrefTarget'
GO

EXEC sp_addextendedproperty
'MS_Description', N'跳转地址',
'SCHEMA', N'dbo',
'TABLE', N'oms_message',
'COLUMN', N'F_Href'
GO

EXEC sp_addextendedproperty
'MS_Description', N'待办关联键',
'SCHEMA', N'dbo',
'TABLE', N'oms_message',
'COLUMN', N'F_KeyValue'
GO

EXEC sp_addextendedproperty
'MS_Description', N'点击已读',
'SCHEMA', N'dbo',
'TABLE', N'oms_message',
'COLUMN', N'F_ClickRead'
GO


-- ----------------------------
-- Table structure for oms_messagehis
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[oms_messagehis]') AND type IN ('U'))
	DROP TABLE [dbo].[oms_messagehis]
GO

CREATE TABLE [dbo].[oms_messagehis] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_MessageId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorTime] datetime2(7)  NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorUserName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[oms_messagehis] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'主键Id',
'SCHEMA', N'dbo',
'TABLE', N'oms_messagehis',
'COLUMN', N'F_Id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'信息Id',
'SCHEMA', N'dbo',
'TABLE', N'oms_messagehis',
'COLUMN', N'F_MessageId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'oms_messagehis',
'COLUMN', N'F_CreatorTime'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建用户主键',
'SCHEMA', N'dbo',
'TABLE', N'oms_messagehis',
'COLUMN', N'F_CreatorUserId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建用户',
'SCHEMA', N'dbo',
'TABLE', N'oms_messagehis',
'COLUMN', N'F_CreatorUserName'
GO


-- ----------------------------
-- Table structure for oms_planview
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[oms_planview]') AND type IN ('U'))
	DROP TABLE [dbo].[oms_planview]
GO

CREATE TABLE [dbo].[oms_planview] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_Name] nvarchar(20) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_Path] varchar(1000) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_Remark] nvarchar(100) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_DeleteMark] tinyint  NULL,
  [F_CreatorTime] datetime2(7)  NOT NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorUserName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CloseDuration] int  NULL,
  [F_Main] int  NULL,
  [F_LastModifyTime] datetime2(7)  NULL,
  [F_LastModifyUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[oms_planview] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'弹出层多长时间不操作就关闭',
'SCHEMA', N'dbo',
'TABLE', N'oms_planview',
'COLUMN', N'F_CloseDuration'
GO

EXEC sp_addextendedproperty
'MS_Description', N'是否是主要的，作为当前显示的平面图',
'SCHEMA', N'dbo',
'TABLE', N'oms_planview',
'COLUMN', N'F_Main'
GO


-- ----------------------------
-- Table structure for oms_secondarytasks
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[oms_secondarytasks]') AND type IN ('U'))
	DROP TABLE [dbo].[oms_secondarytasks]
GO

CREATE TABLE [dbo].[oms_secondarytasks] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_FirstlevelTaskId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_Path] varchar(20) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_Remark] nvarchar(100) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_DeleteMark] tinyint  NULL,
  [F_CreatorTime] datetime2(7)  NOT NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorUserName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[oms_secondarytasks] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Table structure for oms_task
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[oms_task]') AND type IN ('U'))
	DROP TABLE [dbo].[oms_task]
GO

CREATE TABLE [dbo].[oms_task] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_ApplyTime] datetime2(7)  NULL,
  [F_Num] int  NULL,
  [F_Type] int  NULL,
  [F_Content] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Risk] int DEFAULT NULL NULL,
  [F_ApplyTable] varchar(500) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Leader] varchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Staff] varchar(1000) COLLATE Chinese_PRC_CI_AI  NULL,
  [F_Server] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Parent] varchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Position] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CompleteTime] datetime2(7) DEFAULT NULL NULL,
  [F_Status] int  NULL,
  [F_DeleteMark] tinyint  NULL,
  [F_CreatorTime] datetime2(7)  NOT NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorUserName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_LastModifyTime] datetime2(7)  NULL,
  [F_LastModifyUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_ExpectTime] datetime2(7)  NULL
)
GO

ALTER TABLE [dbo].[oms_task] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'作业id',
'SCHEMA', N'dbo',
'TABLE', N'oms_task',
'COLUMN', N'F_Id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'作业申请时间',
'SCHEMA', N'dbo',
'TABLE', N'oms_task',
'COLUMN', N'F_ApplyTime'
GO

EXEC sp_addextendedproperty
'MS_Description', N'序号',
'SCHEMA', N'dbo',
'TABLE', N'oms_task',
'COLUMN', N'F_Num'
GO

EXEC sp_addextendedproperty
'MS_Description', N'作业类型',
'SCHEMA', N'dbo',
'TABLE', N'oms_task',
'COLUMN', N'F_Type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'作业内容',
'SCHEMA', N'dbo',
'TABLE', N'oms_task',
'COLUMN', N'F_Content'
GO

EXEC sp_addextendedproperty
'MS_Description', N'危险类型',
'SCHEMA', N'dbo',
'TABLE', N'oms_task',
'COLUMN', N'F_Risk'
GO

EXEC sp_addextendedproperty
'MS_Description', N'施工申请表',
'SCHEMA', N'dbo',
'TABLE', N'oms_task',
'COLUMN', N'F_ApplyTable'
GO

EXEC sp_addextendedproperty
'MS_Description', N'现场负责人',
'SCHEMA', N'dbo',
'TABLE', N'oms_task',
'COLUMN', N'F_Leader'
GO

EXEC sp_addextendedproperty
'MS_Description', N'施工人员',
'SCHEMA', N'dbo',
'TABLE', N'oms_task',
'COLUMN', N'F_Staff'
GO

EXEC sp_addextendedproperty
'MS_Description', N'公司名称',
'SCHEMA', N'dbo',
'TABLE', N'oms_task',
'COLUMN', N'F_Server'
GO

EXEC sp_addextendedproperty
'MS_Description', N'主任务id',
'SCHEMA', N'dbo',
'TABLE', N'oms_task',
'COLUMN', N'F_Parent'
GO

EXEC sp_addextendedproperty
'MS_Description', N'坐标',
'SCHEMA', N'dbo',
'TABLE', N'oms_task',
'COLUMN', N'F_Position'
GO

EXEC sp_addextendedproperty
'MS_Description', N'作业完成时间',
'SCHEMA', N'dbo',
'TABLE', N'oms_task',
'COLUMN', N'F_CompleteTime'
GO

EXEC sp_addextendedproperty
'MS_Description', N'状态',
'SCHEMA', N'dbo',
'TABLE', N'oms_task',
'COLUMN', N'F_Status'
GO

EXEC sp_addextendedproperty
'MS_Description', N'预计完成日期',
'SCHEMA', N'dbo',
'TABLE', N'oms_task',
'COLUMN', N'F_ExpectTime'
GO


-- ----------------------------
-- Table structure for oms_trct
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[oms_trct]') AND type IN ('U'))
	DROP TABLE [dbo].[oms_trct]
GO

CREATE TABLE [dbo].[oms_trct] (
  [id] varchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS  NOT NULL,
  [date] datetime2(7)  NULL,
  [event_count] int  NULL,
  [trct] float(53)  NULL,
  [work_hour] float(53)  NULL,
  [F_DeleteMark] tinyint  NULL,
  [F_EnabledMark] tinyint  NULL,
  [F_CreatorTime] datetime2(7)  NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorUserName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_LastModifyTime] datetime2(7)  NULL,
  [F_LastModifyUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DeleteTime] datetime2(7)  NULL,
  [F_DeleteUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[oms_trct] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'主键',
'SCHEMA', N'dbo',
'TABLE', N'oms_trct',
'COLUMN', N'id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'日期',
'SCHEMA', N'dbo',
'TABLE', N'oms_trct',
'COLUMN', N'date'
GO

EXEC sp_addextendedproperty
'MS_Description', N'事故数',
'SCHEMA', N'dbo',
'TABLE', N'oms_trct',
'COLUMN', N'event_count'
GO

EXEC sp_addextendedproperty
'MS_Description', N'TRCT',
'SCHEMA', N'dbo',
'TABLE', N'oms_trct',
'COLUMN', N'trct'
GO

EXEC sp_addextendedproperty
'MS_Description', N'工时数',
'SCHEMA', N'dbo',
'TABLE', N'oms_trct',
'COLUMN', N'work_hour'
GO


-- ----------------------------
-- Table structure for oms_uploadfile
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[oms_uploadfile]') AND type IN ('U'))
	DROP TABLE [dbo].[oms_uploadfile]
GO

CREATE TABLE [dbo].[oms_uploadfile] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_FilePath] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_FileName] nvarchar(200) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_FileType] int  NULL,
  [F_FileSize] int  NULL,
  [F_FileExtension] nvarchar(20) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_FileBy] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Description] nvarchar(200) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_OrganizeId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_EnabledMark] tinyint  NULL,
  [F_CreatorTime] datetime2(7)  NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorUserName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[oms_uploadfile] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'主键Id',
'SCHEMA', N'dbo',
'TABLE', N'oms_uploadfile',
'COLUMN', N'F_Id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'文件路径',
'SCHEMA', N'dbo',
'TABLE', N'oms_uploadfile',
'COLUMN', N'F_FilePath'
GO

EXEC sp_addextendedproperty
'MS_Description', N'文件名称',
'SCHEMA', N'dbo',
'TABLE', N'oms_uploadfile',
'COLUMN', N'F_FileName'
GO

EXEC sp_addextendedproperty
'MS_Description', N'文件类型（0 文件，1 图片）',
'SCHEMA', N'dbo',
'TABLE', N'oms_uploadfile',
'COLUMN', N'F_FileType'
GO

EXEC sp_addextendedproperty
'MS_Description', N'文件大小',
'SCHEMA', N'dbo',
'TABLE', N'oms_uploadfile',
'COLUMN', N'F_FileSize'
GO

EXEC sp_addextendedproperty
'MS_Description', N'文件扩展名',
'SCHEMA', N'dbo',
'TABLE', N'oms_uploadfile',
'COLUMN', N'F_FileExtension'
GO

EXEC sp_addextendedproperty
'MS_Description', N'文件所属',
'SCHEMA', N'dbo',
'TABLE', N'oms_uploadfile',
'COLUMN', N'F_FileBy'
GO

EXEC sp_addextendedproperty
'MS_Description', N'备注',
'SCHEMA', N'dbo',
'TABLE', N'oms_uploadfile',
'COLUMN', N'F_Description'
GO

EXEC sp_addextendedproperty
'MS_Description', N'所属部门',
'SCHEMA', N'dbo',
'TABLE', N'oms_uploadfile',
'COLUMN', N'F_OrganizeId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'有效',
'SCHEMA', N'dbo',
'TABLE', N'oms_uploadfile',
'COLUMN', N'F_EnabledMark'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'oms_uploadfile',
'COLUMN', N'F_CreatorTime'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建用户主键',
'SCHEMA', N'dbo',
'TABLE', N'oms_uploadfile',
'COLUMN', N'F_CreatorUserId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建用户',
'SCHEMA', N'dbo',
'TABLE', N'oms_uploadfile',
'COLUMN', N'F_CreatorUserName'
GO


-- ----------------------------
-- Table structure for sys_area
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_area]') AND type IN ('U'))
	DROP TABLE [dbo].[sys_area]
GO

CREATE TABLE [dbo].[sys_area] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_ParentId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Layers] int  NULL,
  [F_EnCode] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_FullName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_SimpleSpelling] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_SortCode] bigint  NULL,
  [F_DeleteMark] tinyint  NULL,
  [F_EnabledMark] tinyint  NULL,
  [F_Description] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorTime] datetime2(7)  NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_LastModifyTime] datetime2(7)  NULL,
  [F_LastModifyUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DeleteTime] datetime2(7)  NULL,
  [F_DeleteUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[sys_area] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'主键',
'SCHEMA', N'dbo',
'TABLE', N'sys_area',
'COLUMN', N'F_Id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'父级',
'SCHEMA', N'dbo',
'TABLE', N'sys_area',
'COLUMN', N'F_ParentId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'层次',
'SCHEMA', N'dbo',
'TABLE', N'sys_area',
'COLUMN', N'F_Layers'
GO

EXEC sp_addextendedproperty
'MS_Description', N'编码',
'SCHEMA', N'dbo',
'TABLE', N'sys_area',
'COLUMN', N'F_EnCode'
GO

EXEC sp_addextendedproperty
'MS_Description', N'名称',
'SCHEMA', N'dbo',
'TABLE', N'sys_area',
'COLUMN', N'F_FullName'
GO

EXEC sp_addextendedproperty
'MS_Description', N'简拼',
'SCHEMA', N'dbo',
'TABLE', N'sys_area',
'COLUMN', N'F_SimpleSpelling'
GO

EXEC sp_addextendedproperty
'MS_Description', N'排序码',
'SCHEMA', N'dbo',
'TABLE', N'sys_area',
'COLUMN', N'F_SortCode'
GO

EXEC sp_addextendedproperty
'MS_Description', N'删除标志',
'SCHEMA', N'dbo',
'TABLE', N'sys_area',
'COLUMN', N'F_DeleteMark'
GO

EXEC sp_addextendedproperty
'MS_Description', N'有效标志',
'SCHEMA', N'dbo',
'TABLE', N'sys_area',
'COLUMN', N'F_EnabledMark'
GO

EXEC sp_addextendedproperty
'MS_Description', N'描述',
'SCHEMA', N'dbo',
'TABLE', N'sys_area',
'COLUMN', N'F_Description'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建日期',
'SCHEMA', N'dbo',
'TABLE', N'sys_area',
'COLUMN', N'F_CreatorTime'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建用户主键',
'SCHEMA', N'dbo',
'TABLE', N'sys_area',
'COLUMN', N'F_CreatorUserId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'最后修改时间',
'SCHEMA', N'dbo',
'TABLE', N'sys_area',
'COLUMN', N'F_LastModifyTime'
GO

EXEC sp_addextendedproperty
'MS_Description', N'最后修改用户',
'SCHEMA', N'dbo',
'TABLE', N'sys_area',
'COLUMN', N'F_LastModifyUserId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'删除时间',
'SCHEMA', N'dbo',
'TABLE', N'sys_area',
'COLUMN', N'F_DeleteTime'
GO

EXEC sp_addextendedproperty
'MS_Description', N'删除用户',
'SCHEMA', N'dbo',
'TABLE', N'sys_area',
'COLUMN', N'F_DeleteUserId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'行政区域表',
'SCHEMA', N'dbo',
'TABLE', N'sys_area'
GO


-- ----------------------------
-- Table structure for sys_codegeneratelog
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_codegeneratelog]') AND type IN ('U'))
	DROP TABLE [dbo].[sys_codegeneratelog]
GO

CREATE TABLE [dbo].[sys_codegeneratelog] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_Code] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_RuleId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_RuleName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_PrintJson] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_PrintCount] int  NULL,
  [F_DeleteMark] tinyint  NULL,
  [F_EnabledMark] tinyint  NULL,
  [F_Description] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorTime] datetime2(7)  NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorUserName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_LastModifyTime] datetime2(7)  NULL,
  [F_LastModifyUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DeleteTime] datetime2(7)  NULL,
  [F_DeleteUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[sys_codegeneratelog] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'主键',
'SCHEMA', N'dbo',
'TABLE', N'sys_codegeneratelog',
'COLUMN', N'F_Id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'条码',
'SCHEMA', N'dbo',
'TABLE', N'sys_codegeneratelog',
'COLUMN', N'F_Code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'规则id',
'SCHEMA', N'dbo',
'TABLE', N'sys_codegeneratelog',
'COLUMN', N'F_RuleId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'规则名称',
'SCHEMA', N'dbo',
'TABLE', N'sys_codegeneratelog',
'COLUMN', N'F_RuleName'
GO

EXEC sp_addextendedproperty
'MS_Description', N'打印Json',
'SCHEMA', N'dbo',
'TABLE', N'sys_codegeneratelog',
'COLUMN', N'F_PrintJson'
GO

EXEC sp_addextendedproperty
'MS_Description', N'打印次数',
'SCHEMA', N'dbo',
'TABLE', N'sys_codegeneratelog',
'COLUMN', N'F_PrintCount'
GO

EXEC sp_addextendedproperty
'MS_Description', N'条码生成记录',
'SCHEMA', N'dbo',
'TABLE', N'sys_codegeneratelog'
GO


-- ----------------------------
-- Table structure for sys_coderule
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_coderule]') AND type IN ('U'))
	DROP TABLE [dbo].[sys_coderule]
GO

CREATE TABLE [dbo].[sys_coderule] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_RuleName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_RuleJson] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_Reset] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_TemplateId] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DeleteMark] tinyint  NULL,
  [F_EnabledMark] tinyint  NULL,
  [F_Description] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorTime] datetime2(7)  NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorUserName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_LastModifyTime] datetime2(7)  NULL,
  [F_LastModifyUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DeleteTime] datetime2(7)  NULL,
  [F_DeleteUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[sys_coderule] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'主键',
'SCHEMA', N'dbo',
'TABLE', N'sys_coderule',
'COLUMN', N'F_Id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'规则名称',
'SCHEMA', N'dbo',
'TABLE', N'sys_coderule',
'COLUMN', N'F_RuleName'
GO

EXEC sp_addextendedproperty
'MS_Description', N'规则内容',
'SCHEMA', N'dbo',
'TABLE', N'sys_coderule',
'COLUMN', N'F_RuleJson'
GO

EXEC sp_addextendedproperty
'MS_Description', N'重设机制',
'SCHEMA', N'dbo',
'TABLE', N'sys_coderule',
'COLUMN', N'F_Reset'
GO

EXEC sp_addextendedproperty
'MS_Description', N'打印模板Id',
'SCHEMA', N'dbo',
'TABLE', N'sys_coderule',
'COLUMN', N'F_TemplateId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'条码规则',
'SCHEMA', N'dbo',
'TABLE', N'sys_coderule'
GO


-- ----------------------------
-- Table structure for sys_coderulelog
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_coderulelog]') AND type IN ('U'))
	DROP TABLE [dbo].[sys_coderulelog]
GO

CREATE TABLE [dbo].[sys_coderulelog] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_RuleId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_Key] nvarchar(100) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_Value] nvarchar(100) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Score] int  NULL
)
GO

ALTER TABLE [dbo].[sys_coderulelog] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'主键',
'SCHEMA', N'dbo',
'TABLE', N'sys_coderulelog',
'COLUMN', N'F_Id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'规则Id',
'SCHEMA', N'dbo',
'TABLE', N'sys_coderulelog',
'COLUMN', N'F_RuleId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'key',
'SCHEMA', N'dbo',
'TABLE', N'sys_coderulelog',
'COLUMN', N'F_Key'
GO

EXEC sp_addextendedproperty
'MS_Description', N'value',
'SCHEMA', N'dbo',
'TABLE', N'sys_coderulelog',
'COLUMN', N'F_Value'
GO

EXEC sp_addextendedproperty
'MS_Description', N'计数',
'SCHEMA', N'dbo',
'TABLE', N'sys_coderulelog',
'COLUMN', N'F_Score'
GO


-- ----------------------------
-- Table structure for sys_dataprivilegerule
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_dataprivilegerule]') AND type IN ('U'))
	DROP TABLE [dbo].[sys_dataprivilegerule]
GO

CREATE TABLE [dbo].[sys_dataprivilegerule] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_ModuleId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_ModuleCode] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_PrivilegeRules] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_SortCode] int  NULL,
  [F_DeleteMark] tinyint  NULL,
  [F_EnabledMark] tinyint  NULL,
  [F_Description] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorTime] datetime2(7)  NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_LastModifyTime] datetime2(7)  NULL,
  [F_LastModifyUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DeleteTime] datetime2(7)  NULL,
  [F_DeleteUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[sys_dataprivilegerule] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Table structure for sys_filterip
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_filterip]') AND type IN ('U'))
	DROP TABLE [dbo].[sys_filterip]
GO

CREATE TABLE [dbo].[sys_filterip] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_Type] tinyint  NULL,
  [F_StartIP] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_EndIP] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_SortCode] int  NULL,
  [F_DeleteMark] tinyint  NULL,
  [F_EnabledMark] tinyint  NULL,
  [F_Description] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorTime] datetime2(7)  NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_LastModifyTime] datetime2(7)  NULL,
  [F_LastModifyUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DeleteTime] datetime2(7)  NULL,
  [F_DeleteUserId] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_EndTime] datetime2(7)  NULL
)
GO

ALTER TABLE [dbo].[sys_filterip] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Table structure for sys_flowscheme
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_flowscheme]') AND type IN ('U'))
	DROP TABLE [dbo].[sys_flowscheme]
GO

CREATE TABLE [dbo].[sys_flowscheme] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_SchemeCode] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_SchemeName] nvarchar(200) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_SchemeType] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_SchemeVersion] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_SchemeCanUser] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_SchemeContent] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_FrmId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_FrmType] int  NOT NULL,
  [F_AuthorizeType] int  NOT NULL,
  [F_SortCode] int  NULL,
  [F_DeleteMark] tinyint  NULL,
  [F_EnabledMark] tinyint  NULL,
  [F_Description] nvarchar(200) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorTime] datetime2(7)  NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorUserName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_LastModifyTime] datetime2(7)  NULL,
  [F_LastModifyUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_LastModifyUserName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_OrganizeId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DeleteTime] datetime2(7)  NULL,
  [F_DeleteUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_InitiatorType] nvarchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [F_InitiatorData] nvarchar(max) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[sys_flowscheme] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'主键Id',
'SCHEMA', N'dbo',
'TABLE', N'sys_flowscheme',
'COLUMN', N'F_Id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'流程编号',
'SCHEMA', N'dbo',
'TABLE', N'sys_flowscheme',
'COLUMN', N'F_SchemeCode'
GO

EXEC sp_addextendedproperty
'MS_Description', N'流程名称',
'SCHEMA', N'dbo',
'TABLE', N'sys_flowscheme',
'COLUMN', N'F_SchemeName'
GO

EXEC sp_addextendedproperty
'MS_Description', N'流程分类',
'SCHEMA', N'dbo',
'TABLE', N'sys_flowscheme',
'COLUMN', N'F_SchemeType'
GO

EXEC sp_addextendedproperty
'MS_Description', N'流程内容版本',
'SCHEMA', N'dbo',
'TABLE', N'sys_flowscheme',
'COLUMN', N'F_SchemeVersion'
GO

EXEC sp_addextendedproperty
'MS_Description', N'流程模板使用者',
'SCHEMA', N'dbo',
'TABLE', N'sys_flowscheme',
'COLUMN', N'F_SchemeCanUser'
GO

EXEC sp_addextendedproperty
'MS_Description', N'流程内容',
'SCHEMA', N'dbo',
'TABLE', N'sys_flowscheme',
'COLUMN', N'F_SchemeContent'
GO

EXEC sp_addextendedproperty
'MS_Description', N'表单ID',
'SCHEMA', N'dbo',
'TABLE', N'sys_flowscheme',
'COLUMN', N'F_FrmId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'表单类型',
'SCHEMA', N'dbo',
'TABLE', N'sys_flowscheme',
'COLUMN', N'F_FrmType'
GO

EXEC sp_addextendedproperty
'MS_Description', N'模板权限类型：0完全公开,1指定部门/人员',
'SCHEMA', N'dbo',
'TABLE', N'sys_flowscheme',
'COLUMN', N'F_AuthorizeType'
GO

EXEC sp_addextendedproperty
'MS_Description', N'排序码',
'SCHEMA', N'dbo',
'TABLE', N'sys_flowscheme',
'COLUMN', N'F_SortCode'
GO

EXEC sp_addextendedproperty
'MS_Description', N'删除标记',
'SCHEMA', N'dbo',
'TABLE', N'sys_flowscheme',
'COLUMN', N'F_DeleteMark'
GO

EXEC sp_addextendedproperty
'MS_Description', N'有效',
'SCHEMA', N'dbo',
'TABLE', N'sys_flowscheme',
'COLUMN', N'F_EnabledMark'
GO

EXEC sp_addextendedproperty
'MS_Description', N'备注',
'SCHEMA', N'dbo',
'TABLE', N'sys_flowscheme',
'COLUMN', N'F_Description'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'sys_flowscheme',
'COLUMN', N'F_CreatorTime'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建用户主键',
'SCHEMA', N'dbo',
'TABLE', N'sys_flowscheme',
'COLUMN', N'F_CreatorUserId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建用户',
'SCHEMA', N'dbo',
'TABLE', N'sys_flowscheme',
'COLUMN', N'F_CreatorUserName'
GO

EXEC sp_addextendedproperty
'MS_Description', N'修改时间',
'SCHEMA', N'dbo',
'TABLE', N'sys_flowscheme',
'COLUMN', N'F_LastModifyTime'
GO

EXEC sp_addextendedproperty
'MS_Description', N'修改用户主键',
'SCHEMA', N'dbo',
'TABLE', N'sys_flowscheme',
'COLUMN', N'F_LastModifyUserId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'修改用户',
'SCHEMA', N'dbo',
'TABLE', N'sys_flowscheme',
'COLUMN', N'F_LastModifyUserName'
GO

EXEC sp_addextendedproperty
'MS_Description', N'所属部门',
'SCHEMA', N'dbo',
'TABLE', N'sys_flowscheme',
'COLUMN', N'F_OrganizeId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'删除时间',
'SCHEMA', N'dbo',
'TABLE', N'sys_flowscheme',
'COLUMN', N'F_DeleteTime'
GO

EXEC sp_addextendedproperty
'MS_Description', N'删除人',
'SCHEMA', N'dbo',
'TABLE', N'sys_flowscheme',
'COLUMN', N'F_DeleteUserId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'发起人权限类型',
'SCHEMA', N'dbo',
'TABLE', N'sys_flowscheme',
'COLUMN', N'F_InitiatorType'
GO

EXEC sp_addextendedproperty
'MS_Description', N'发起人权限数据',
'SCHEMA', N'dbo',
'TABLE', N'sys_flowscheme',
'COLUMN', N'F_InitiatorData'
GO

EXEC sp_addextendedproperty
'MS_Description', N'工作流模板信息表',
'SCHEMA', N'dbo',
'TABLE', N'sys_flowscheme'
GO


-- ----------------------------
-- Table structure for sys_form
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_form]') AND type IN ('U'))
	DROP TABLE [dbo].[sys_form]
GO

CREATE TABLE [dbo].[sys_form] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_Name] nvarchar(200) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_FrmType] int  NULL,
  [F_WebId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Fields] int  NULL,
  [F_ContentData] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_ContentParse] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Content] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_SortCode] int  NULL,
  [F_EnabledMark] tinyint  NULL,
  [F_DeleteMark] tinyint  NULL,
  [F_CreatorTime] datetime2(7)  NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_LastModifyTime] datetime2(7)  NULL,
  [F_LastModifyUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DeleteTime] datetime2(7)  NULL,
  [F_DeleteUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Description] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_OrganizeId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DbName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[sys_form] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'表单模板Id',
'SCHEMA', N'dbo',
'TABLE', N'sys_form',
'COLUMN', N'F_Id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'表单名称',
'SCHEMA', N'dbo',
'TABLE', N'sys_form',
'COLUMN', N'F_Name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'表单类型，0：默认动态表单；1：Web自定义表单',
'SCHEMA', N'dbo',
'TABLE', N'sys_form',
'COLUMN', N'F_FrmType'
GO

EXEC sp_addextendedproperty
'MS_Description', N'系统页面标识，当表单类型为用Web自定义的表单时，需要标识加载哪个页面',
'SCHEMA', N'dbo',
'TABLE', N'sys_form',
'COLUMN', N'F_WebId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'字段个数',
'SCHEMA', N'dbo',
'TABLE', N'sys_form',
'COLUMN', N'F_Fields'
GO

EXEC sp_addextendedproperty
'MS_Description', N'表单中的控件属性描述',
'SCHEMA', N'dbo',
'TABLE', N'sys_form',
'COLUMN', N'F_ContentData'
GO

EXEC sp_addextendedproperty
'MS_Description', N'表单控件位置模板',
'SCHEMA', N'dbo',
'TABLE', N'sys_form',
'COLUMN', N'F_ContentParse'
GO

EXEC sp_addextendedproperty
'MS_Description', N'表单原html模板未经处理的',
'SCHEMA', N'dbo',
'TABLE', N'sys_form',
'COLUMN', N'F_Content'
GO

EXEC sp_addextendedproperty
'MS_Description', N'排序码',
'SCHEMA', N'dbo',
'TABLE', N'sys_form',
'COLUMN', N'F_SortCode'
GO

EXEC sp_addextendedproperty
'MS_Description', N'是否启用',
'SCHEMA', N'dbo',
'TABLE', N'sys_form',
'COLUMN', N'F_EnabledMark'
GO

EXEC sp_addextendedproperty
'MS_Description', N'逻辑删除标志',
'SCHEMA', N'dbo',
'TABLE', N'sys_form',
'COLUMN', N'F_DeleteMark'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'sys_form',
'COLUMN', N'F_CreatorTime'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建人',
'SCHEMA', N'dbo',
'TABLE', N'sys_form',
'COLUMN', N'F_CreatorUserId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'最后修改时间',
'SCHEMA', N'dbo',
'TABLE', N'sys_form',
'COLUMN', N'F_LastModifyTime'
GO

EXEC sp_addextendedproperty
'MS_Description', N'最后修改人',
'SCHEMA', N'dbo',
'TABLE', N'sys_form',
'COLUMN', N'F_LastModifyUserId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'删除时间',
'SCHEMA', N'dbo',
'TABLE', N'sys_form',
'COLUMN', N'F_DeleteTime'
GO

EXEC sp_addextendedproperty
'MS_Description', N'删除人',
'SCHEMA', N'dbo',
'TABLE', N'sys_form',
'COLUMN', N'F_DeleteUserId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'内容',
'SCHEMA', N'dbo',
'TABLE', N'sys_form',
'COLUMN', N'F_Description'
GO

EXEC sp_addextendedproperty
'MS_Description', N'所属组织',
'SCHEMA', N'dbo',
'TABLE', N'sys_form',
'COLUMN', N'F_OrganizeId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'数据库名称',
'SCHEMA', N'dbo',
'TABLE', N'sys_form',
'COLUMN', N'F_DbName'
GO

EXEC sp_addextendedproperty
'MS_Description', N'表单模板表',
'SCHEMA', N'dbo',
'TABLE', N'sys_form'
GO


-- ----------------------------
-- Table structure for sys_items
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_items]') AND type IN ('U'))
	DROP TABLE [dbo].[sys_items]
GO

CREATE TABLE [dbo].[sys_items] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_ParentId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_EnCode] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_FullName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_IsTree] tinyint  NULL,
  [F_Layers] int  NULL,
  [F_SortCode] int  NULL,
  [F_DeleteMark] tinyint  NULL,
  [F_EnabledMark] tinyint  NULL,
  [F_Description] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorTime] datetime2(7)  NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_LastModifyTime] datetime2(7)  NULL,
  [F_LastModifyUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DeleteTime] datetime2(7)  NULL,
  [F_DeleteUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[sys_items] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Table structure for sys_itemsdetail
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_itemsdetail]') AND type IN ('U'))
	DROP TABLE [dbo].[sys_itemsdetail]
GO

CREATE TABLE [dbo].[sys_itemsdetail] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_ItemId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_ParentId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_ItemCode] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_ItemName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_SimpleSpelling] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_IsDefault] tinyint  NULL,
  [F_Layers] int  NULL,
  [F_SortCode] int  NULL,
  [F_DeleteMark] tinyint  NULL,
  [F_EnabledMark] tinyint  NULL,
  [F_Description] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorTime] datetime2(7)  NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_LastModifyTime] datetime2(7)  NULL,
  [F_LastModifyUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DeleteTime] datetime2(7)  NULL,
  [F_DeleteUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[sys_itemsdetail] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Table structure for sys_log
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_log]') AND type IN ('U'))
	DROP TABLE [dbo].[sys_log]
GO

CREATE TABLE [dbo].[sys_log] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_Date] datetime2(7)  NULL,
  [F_Account] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_NickName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Type] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_IPAddress] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_IPAddressName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_ModuleId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_ModuleName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Result] tinyint  NULL,
  [F_Description] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorTime] datetime2(7)  NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_KeyValue] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CompanyId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[sys_log] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Table structure for sys_module
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_module]') AND type IN ('U'))
	DROP TABLE [dbo].[sys_module]
GO

CREATE TABLE [dbo].[sys_module] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_ParentId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Layers] int  NULL,
  [F_EnCode] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_FullName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Icon] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_UrlAddress] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Target] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_IsMenu] tinyint  NULL,
  [F_IsExpand] tinyint  NULL,
  [F_IsFields] tinyint  NULL,
  [F_IsPublic] tinyint  NULL,
  [F_AllowEdit] tinyint  NULL,
  [F_AllowDelete] tinyint  NULL,
  [F_SortCode] int  NULL,
  [F_DeleteMark] tinyint  NULL,
  [F_EnabledMark] tinyint  NULL,
  [F_Description] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorTime] datetime2(7)  NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_LastModifyTime] datetime2(7)  NULL,
  [F_LastModifyUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DeleteTime] datetime2(7)  NULL,
  [F_DeleteUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Authorize] nvarchar(100) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[sys_module] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Table structure for sys_modulebutton
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_modulebutton]') AND type IN ('U'))
	DROP TABLE [dbo].[sys_modulebutton]
GO

CREATE TABLE [dbo].[sys_modulebutton] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_ModuleId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_ParentId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Layers] int  NULL,
  [F_EnCode] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_FullName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Icon] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Location] int  NULL,
  [F_JsEvent] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_UrlAddress] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Split] tinyint  NULL,
  [F_IsPublic] tinyint  NULL,
  [F_AllowEdit] tinyint  NULL,
  [F_AllowDelete] tinyint  NULL,
  [F_SortCode] int  NULL,
  [F_DeleteMark] tinyint  NULL,
  [F_EnabledMark] tinyint  NULL,
  [F_Description] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorTime] datetime2(7)  NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_LastModifyTime] datetime2(7)  NULL,
  [F_LastModifyUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DeleteTime] datetime2(7)  NULL,
  [F_DeleteUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Authorize] nvarchar(100) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[sys_modulebutton] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Table structure for sys_modulefields
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_modulefields]') AND type IN ('U'))
	DROP TABLE [dbo].[sys_modulefields]
GO

CREATE TABLE [dbo].[sys_modulefields] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_ModuleId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_EnCode] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_FullName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DeleteMark] tinyint  NULL,
  [F_EnabledMark] tinyint  NULL,
  [F_Description] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorTime] datetime2(7)  NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_LastModifyTime] datetime2(7)  NULL,
  [F_LastModifyUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DeleteTime] datetime2(7)  NULL,
  [F_DeleteUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_IsPublic] tinyint  NULL
)
GO

ALTER TABLE [dbo].[sys_modulefields] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Table structure for sys_notice
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_notice]') AND type IN ('U'))
	DROP TABLE [dbo].[sys_notice]
GO

CREATE TABLE [dbo].[sys_notice] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_Title] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Content] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DeleteMark] tinyint  NULL,
  [F_EnabledMark] tinyint  NULL,
  [F_Description] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorTime] datetime2(7)  NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorUserName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_LastModifyTime] datetime2(7)  NULL,
  [F_LastModifyUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DeleteTime] datetime2(7)  NULL,
  [F_DeleteUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[sys_notice] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Table structure for sys_openjob
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_openjob]') AND type IN ('U'))
	DROP TABLE [dbo].[sys_openjob]
GO

CREATE TABLE [dbo].[sys_openjob] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_FileName] nvarchar(100) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_JobName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_JobGroup] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_StarRunTime] datetime2(7)  NULL,
  [F_EndRunTime] datetime2(7)  NULL,
  [F_CronExpress] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DeleteMark] tinyint  NULL,
  [F_EnabledMark] tinyint  NULL,
  [F_Description] nvarchar(500) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorTime] datetime2(7)  NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_LastModifyTime] datetime2(7)  NULL,
  [F_LastModifyUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DeleteTime] datetime2(7)  NULL,
  [F_DeleteUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_LastRunTime] datetime2(7)  NULL,
  [F_LastRunMark] tinyint  NULL,
  [F_LastRunErrTime] datetime2(7)  NULL,
  [F_LastRunErrMsg] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_JobType] int  NOT NULL,
  [F_IsLog] char(2) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_RequestHeaders] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_RequestString] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_RequestUrl] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DbNumber] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_JobSqlParm] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_JobSql] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_JobDBProvider] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[sys_openjob] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'主键Id',
'SCHEMA', N'dbo',
'TABLE', N'sys_openjob',
'COLUMN', N'F_Id'
GO


-- ----------------------------
-- Table structure for sys_openjoblog
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_openjoblog]') AND type IN ('U'))
	DROP TABLE [dbo].[sys_openjoblog]
GO

CREATE TABLE [dbo].[sys_openjoblog] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_JobId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_Description] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorTime] datetime2(7)  NULL,
  [F_EnabledMark] tinyint  NOT NULL,
  [F_JobName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[sys_openjoblog] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'任务Id',
'SCHEMA', N'dbo',
'TABLE', N'sys_openjoblog',
'COLUMN', N'F_JobId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'任务信息',
'SCHEMA', N'dbo',
'TABLE', N'sys_openjoblog',
'COLUMN', N'F_Description'
GO

EXEC sp_addextendedproperty
'MS_Description', N'执行时间',
'SCHEMA', N'dbo',
'TABLE', N'sys_openjoblog',
'COLUMN', N'F_CreatorTime'
GO

EXEC sp_addextendedproperty
'MS_Description', N'执行状态',
'SCHEMA', N'dbo',
'TABLE', N'sys_openjoblog',
'COLUMN', N'F_EnabledMark'
GO

EXEC sp_addextendedproperty
'MS_Description', N'任务名称',
'SCHEMA', N'dbo',
'TABLE', N'sys_openjoblog',
'COLUMN', N'F_JobName'
GO


-- ----------------------------
-- Table structure for sys_organize
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_organize]') AND type IN ('U'))
	DROP TABLE [dbo].[sys_organize]
GO

CREATE TABLE [dbo].[sys_organize] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_ParentId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Layers] int  NULL,
  [F_EnCode] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_FullName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_ShortName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CategoryId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_ManagerId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_TelePhone] nvarchar(20) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_MobilePhone] nvarchar(20) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_WeChat] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Fax] nvarchar(20) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Email] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_AreaId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Address] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_AllowEdit] tinyint  NULL,
  [F_AllowDelete] tinyint  NULL,
  [F_SortCode] int  NULL,
  [F_DeleteMark] tinyint  NULL,
  [F_EnabledMark] tinyint  NULL,
  [F_Description] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorTime] datetime2(7)  NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_LastModifyTime] datetime2(7)  NULL,
  [F_LastModifyUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DeleteTime] datetime2(7)  NULL,
  [F_DeleteUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_StaffCount] int DEFAULT 0 NULL
)
GO

ALTER TABLE [dbo].[sys_organize] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'成员数量',
'SCHEMA', N'dbo',
'TABLE', N'sys_organize',
'COLUMN', N'F_StaffCount'
GO


-- ----------------------------
-- Table structure for sys_quickmodule
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_quickmodule]') AND type IN ('U'))
	DROP TABLE [dbo].[sys_quickmodule]
GO

CREATE TABLE [dbo].[sys_quickmodule] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_ModuleId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DeleteMark] tinyint  NULL,
  [F_EnabledMark] tinyint  NULL,
  [F_Description] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorTime] datetime2(7)  NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_LastModifyTime] datetime2(7)  NULL,
  [F_LastModifyUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DeleteTime] datetime2(7)  NULL,
  [F_DeleteUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[sys_quickmodule] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_role]') AND type IN ('U'))
	DROP TABLE [dbo].[sys_role]
GO

CREATE TABLE [dbo].[sys_role] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_CompanyId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Category] int  NULL,
  [F_EnCode] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_FullName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Type] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_AllowEdit] tinyint  NULL,
  [F_AllowDelete] tinyint  NULL,
  [F_SortCode] int  NULL,
  [F_DeleteMark] tinyint  NULL,
  [F_EnabledMark] tinyint  NULL,
  [F_Description] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorTime] datetime2(7)  NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_LastModifyTime] datetime2(7)  NULL,
  [F_LastModifyUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DeleteTime] datetime2(7)  NULL,
  [F_DeleteUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[sys_role] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Table structure for sys_roleauthorize
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_roleauthorize]') AND type IN ('U'))
	DROP TABLE [dbo].[sys_roleauthorize]
GO

CREATE TABLE [dbo].[sys_roleauthorize] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_ItemType] int  NULL,
  [F_ItemId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_ObjectType] int  NULL,
  [F_ObjectId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_SortCode] int  NULL,
  [F_CreatorTime] datetime2(7)  NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[sys_roleauthorize] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Table structure for sys_serverstate
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_serverstate]') AND type IN ('U'))
	DROP TABLE [dbo].[sys_serverstate]
GO

CREATE TABLE [dbo].[sys_serverstate] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_WebSite] nvarchar(200) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_ARM] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CPU] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_IIS] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Date] date  NULL,
  [F_Cout] int  NULL
)
GO

ALTER TABLE [dbo].[sys_serverstate] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Table structure for sys_systemset
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_systemset]') AND type IN ('U'))
	DROP TABLE [dbo].[sys_systemset]
GO

CREATE TABLE [dbo].[sys_systemset] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_Logo] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_LogoCode] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_ProjectName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CompanyName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_AdminAccount] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_AdminPassword] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DeleteMark] tinyint  NULL,
  [F_EnabledMark] tinyint  NULL,
  [F_Description] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorTime] datetime2(7)  NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_LastModifyTime] datetime2(7)  NULL,
  [F_LastModifyUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DeleteTime] datetime2(7)  NULL,
  [F_DeleteUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_MobilePhone] nvarchar(20) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_PrincipalMan] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_EndTime] datetime2(7)  NULL,
  [F_DbString] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DBProvider] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_HostUrl] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DbNumber] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL
)
GO

ALTER TABLE [dbo].[sys_systemset] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Table structure for sys_template
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_template]') AND type IN ('U'))
	DROP TABLE [dbo].[sys_template]
GO

CREATE TABLE [dbo].[sys_template] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_TemplateName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_TemplateFile] nvarchar(100) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_TemplateDBProvider] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_TemplateSql] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_TemplateSqlParm] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_PrintType] int  NULL,
  [F_Batch] tinyint  NULL,
  [F_DeleteMark] tinyint  NULL,
  [F_EnabledMark] tinyint  NULL,
  [F_Description] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorTime] datetime2(7)  NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorUserName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_LastModifyTime] datetime2(7)  NULL,
  [F_LastModifyUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DeleteTime] datetime2(7)  NULL,
  [F_DeleteUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[sys_template] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'主键',
'SCHEMA', N'dbo',
'TABLE', N'sys_template',
'COLUMN', N'F_Id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'模板名称',
'SCHEMA', N'dbo',
'TABLE', N'sys_template',
'COLUMN', N'F_TemplateName'
GO

EXEC sp_addextendedproperty
'MS_Description', N'模板文件',
'SCHEMA', N'dbo',
'TABLE', N'sys_template',
'COLUMN', N'F_TemplateFile'
GO

EXEC sp_addextendedproperty
'MS_Description', N'模板执行库',
'SCHEMA', N'dbo',
'TABLE', N'sys_template',
'COLUMN', N'F_TemplateDBProvider'
GO

EXEC sp_addextendedproperty
'MS_Description', N'模板执行sql',
'SCHEMA', N'dbo',
'TABLE', N'sys_template',
'COLUMN', N'F_TemplateSql'
GO

EXEC sp_addextendedproperty
'MS_Description', N'模板执行参数',
'SCHEMA', N'dbo',
'TABLE', N'sys_template',
'COLUMN', N'F_TemplateSqlParm'
GO

EXEC sp_addextendedproperty
'MS_Description', N'打印方式',
'SCHEMA', N'dbo',
'TABLE', N'sys_template',
'COLUMN', N'F_PrintType'
GO

EXEC sp_addextendedproperty
'MS_Description', N'是否批量',
'SCHEMA', N'dbo',
'TABLE', N'sys_template',
'COLUMN', N'F_Batch'
GO

EXEC sp_addextendedproperty
'MS_Description', N'模板',
'SCHEMA', N'dbo',
'TABLE', N'sys_template'
GO


-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_user]') AND type IN ('U'))
	DROP TABLE [dbo].[sys_user]
GO

CREATE TABLE [dbo].[sys_user] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_Account] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_RealName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_NickName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_HeadIcon] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Gender] tinyint  NULL,
  [F_Birthday] datetime2(7)  NULL,
  [F_MobilePhone] nvarchar(20) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Email] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_WeChat] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_ManagerId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_SecurityLevel] int  NULL,
  [F_Signature] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CompanyId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_OrganizeId] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_RoleId] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DutyId] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_IsAdmin] tinyint  NULL,
  [F_IsBoss] tinyint  NULL,
  [F_IsLeaderInDepts] tinyint  NULL,
  [F_IsSenior] tinyint  NULL,
  [F_SortCode] int  NULL,
  [F_DeleteMark] tinyint  NULL,
  [F_EnabledMark] tinyint  NULL,
  [F_Description] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CreatorTime] datetime2(7)  NULL,
  [F_CreatorUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_LastModifyTime] datetime2(7)  NULL,
  [F_LastModifyUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DeleteTime] datetime2(7)  NULL,
  [F_DeleteUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DingTalkUserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DingTalkUserName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_DingTalkAvatar] nvarchar(100) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_WxOpenId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_WxNickName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_HeadImgUrl] nvarchar(100) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[sys_user] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Table structure for sys_userlogon
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_userlogon]') AND type IN ('U'))
	DROP TABLE [dbo].[sys_userlogon]
GO

CREATE TABLE [dbo].[sys_userlogon] (
  [F_Id] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [F_UserId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_UserPassword] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_UserSecretkey] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_AllowStartTime] datetime2(7)  NULL,
  [F_AllowEndTime] datetime2(7)  NULL,
  [F_LockStartDate] datetime2(7)  NULL,
  [F_LockEndDate] datetime2(7)  NULL,
  [F_FirstVisitTime] datetime2(7)  NULL,
  [F_PreviousVisitTime] datetime2(7)  NULL,
  [F_LastVisitTime] datetime2(7)  NULL,
  [F_ChangePasswordDate] datetime2(7)  NULL,
  [F_MultiUserLogin] tinyint  NULL,
  [F_LogOnCount] int  NULL,
  [F_UserOnLine] tinyint  NULL,
  [F_Question] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_AnswerQuestion] nvarchar(max) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_CheckIPAddress] tinyint  NULL,
  [F_Language] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_Theme] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_LoginSession] nvarchar(100) COLLATE Chinese_PRC_CI_AS  NULL,
  [F_ErrorNum] int  NULL
)
GO

ALTER TABLE [dbo].[sys_userlogon] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Indexes structure for table cms_articlecategory
-- ----------------------------
CREATE UNIQUE NONCLUSTERED INDEX [cms_articlecategory_key1]
ON [dbo].[cms_articlecategory] (
  [F_FullName] ASC
)
GO


-- ----------------------------
-- Primary Key structure for table cms_articlecategory
-- ----------------------------
ALTER TABLE [dbo].[cms_articlecategory] ADD CONSTRAINT [PK__cms_arti__2C6EC7236FC12859] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Indexes structure for table cms_articlenews
-- ----------------------------
CREATE UNIQUE NONCLUSTERED INDEX [cms_articlenews_key1]
ON [dbo].[cms_articlenews] (
  [F_CategoryId] ASC,
  [F_Title] ASC
)
GO


-- ----------------------------
-- Primary Key structure for table cms_articlenews
-- ----------------------------
ALTER TABLE [dbo].[cms_articlenews] ADD CONSTRAINT [PK__cms_arti__2C6EC723A669E568] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Indexes structure for table crm_order
-- ----------------------------
CREATE UNIQUE NONCLUSTERED INDEX [crm_order_key1]
ON [dbo].[crm_order] (
  [F_OrderCode] ASC
)
GO

EXEC sp_addextendedproperty
'MS_Description', N'编号唯一',
'SCHEMA', N'dbo',
'TABLE', N'crm_order',
'INDEX', N'crm_order_key1'
GO


-- ----------------------------
-- Primary Key structure for table crm_order
-- ----------------------------
ALTER TABLE [dbo].[crm_order] ADD CONSTRAINT [PK__crm_orde__2C6EC723652E10FE] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Indexes structure for table crm_orderdetail
-- ----------------------------
CREATE UNIQUE NONCLUSTERED INDEX [crm_orderdetail_key1]
ON [dbo].[crm_orderdetail] (
  [F_OrderId] ASC,
  [F_ProductName] ASC
)
GO

EXEC sp_addextendedproperty
'MS_Description', N'唯一键',
'SCHEMA', N'dbo',
'TABLE', N'crm_orderdetail',
'INDEX', N'crm_orderdetail_key1'
GO


-- ----------------------------
-- Primary Key structure for table crm_orderdetail
-- ----------------------------
ALTER TABLE [dbo].[crm_orderdetail] ADD CONSTRAINT [PK__crm_orde__2C6EC7239DE559CA] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Primary Key structure for table oms_contractor
-- ----------------------------
ALTER TABLE [dbo].[oms_contractor] ADD CONSTRAINT [PK__oms_cont__2C6EC7238B687B98] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Primary Key structure for table oms_contractorpower
-- ----------------------------
ALTER TABLE [dbo].[oms_contractorpower] ADD CONSTRAINT [PK__oms_cont__2C6EC7238B687B97] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Primary Key structure for table oms_contractorstaff
-- ----------------------------
ALTER TABLE [dbo].[oms_contractorstaff] ADD CONSTRAINT [PK__oms_cont__2C6EC7238B687B99] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Primary Key structure for table oms_event
-- ----------------------------
ALTER TABLE [dbo].[oms_event] ADD CONSTRAINT [PK__oms_even__2C6EC723528CC91F] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Primary Key structure for table oms_firstleveltask
-- ----------------------------
ALTER TABLE [dbo].[oms_firstleveltask] ADD CONSTRAINT [PK__oms_task__2C6EC7238B687B99] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Indexes structure for table oms_flowinstance
-- ----------------------------
CREATE UNIQUE NONCLUSTERED INDEX [oms_flowinstance_key1]
ON [dbo].[oms_flowinstance] (
  [F_Code] ASC
)
GO


-- ----------------------------
-- Primary Key structure for table oms_flowinstance
-- ----------------------------
ALTER TABLE [dbo].[oms_flowinstance] ADD CONSTRAINT [PK__oms_flow__2C6EC7234EC149C4] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Primary Key structure for table oms_flowinstancehis
-- ----------------------------
ALTER TABLE [dbo].[oms_flowinstancehis] ADD CONSTRAINT [PK__oms_flow__2C6EC723FEBD1C76] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Primary Key structure for table oms_flowinstanceinfo
-- ----------------------------
ALTER TABLE [dbo].[oms_flowinstanceinfo] ADD CONSTRAINT [PK__oms_flow__2C6EC7231253CD53] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Primary Key structure for table oms_formtest
-- ----------------------------
ALTER TABLE [dbo].[oms_formtest] ADD CONSTRAINT [PK__oms_form__2C6EC7238B687B97] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Primary Key structure for table oms_message
-- ----------------------------
ALTER TABLE [dbo].[oms_message] ADD CONSTRAINT [PK__oms_mess__2C6EC7238BC47D88] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Primary Key structure for table oms_messagehis
-- ----------------------------
ALTER TABLE [dbo].[oms_messagehis] ADD CONSTRAINT [PK__oms_mess__2C6EC723EAF9C232] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Primary Key structure for table oms_planview
-- ----------------------------
ALTER TABLE [dbo].[oms_planview] ADD CONSTRAINT [PK__oms_plan__2C6EC7238B687B99] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Primary Key structure for table oms_secondarytasks
-- ----------------------------
ALTER TABLE [dbo].[oms_secondarytasks] ADD CONSTRAINT [PK__oms_task__2C6EC7238B687B98] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Primary Key structure for table oms_task
-- ----------------------------
ALTER TABLE [dbo].[oms_task] ADD CONSTRAINT [PK__oms_task__2C6EC723DD810A80] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Primary Key structure for table oms_trct
-- ----------------------------
ALTER TABLE [dbo].[oms_trct] ADD CONSTRAINT [PK__oms_trct__3213E83F5462BDC6] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Indexes structure for table oms_uploadfile
-- ----------------------------
CREATE UNIQUE NONCLUSTERED INDEX [oms_uploadfile_key1]
ON [dbo].[oms_uploadfile] (
  [F_FileName] ASC
)
GO

EXEC sp_addextendedproperty
'MS_Description', N'唯一键',
'SCHEMA', N'dbo',
'TABLE', N'oms_uploadfile',
'INDEX', N'oms_uploadfile_key1'
GO


-- ----------------------------
-- Primary Key structure for table oms_uploadfile
-- ----------------------------
ALTER TABLE [dbo].[oms_uploadfile] ADD CONSTRAINT [PK__oms_uplo__2C6EC723C7D25D2B] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Indexes structure for table sys_area
-- ----------------------------
CREATE UNIQUE NONCLUSTERED INDEX [sys_area_key1]
ON [dbo].[sys_area] (
  [F_EnCode] ASC
)
GO


-- ----------------------------
-- Primary Key structure for table sys_area
-- ----------------------------
ALTER TABLE [dbo].[sys_area] ADD CONSTRAINT [PK__sys_area__2C6EC7234A0359F1] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Primary Key structure for table sys_codegeneratelog
-- ----------------------------
ALTER TABLE [dbo].[sys_codegeneratelog] ADD CONSTRAINT [PK__sys_code__2C6EC72327F98075] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Primary Key structure for table sys_coderule
-- ----------------------------
ALTER TABLE [dbo].[sys_coderule] ADD CONSTRAINT [PK__sys_code__2C6EC7235EA6B31D] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Primary Key structure for table sys_coderulelog
-- ----------------------------
ALTER TABLE [dbo].[sys_coderulelog] ADD CONSTRAINT [PK__sys_code__2C6EC7235EE7C97A] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Indexes structure for table sys_dataprivilegerule
-- ----------------------------
CREATE UNIQUE NONCLUSTERED INDEX [sys_dataprivilegerule_key1]
ON [dbo].[sys_dataprivilegerule] (
  [F_ModuleId] ASC
)
GO

EXEC sp_addextendedproperty
'MS_Description', N'唯一键',
'SCHEMA', N'dbo',
'TABLE', N'sys_dataprivilegerule',
'INDEX', N'sys_dataprivilegerule_key1'
GO


-- ----------------------------
-- Primary Key structure for table sys_dataprivilegerule
-- ----------------------------
ALTER TABLE [dbo].[sys_dataprivilegerule] ADD CONSTRAINT [PK__sys_data__2C6EC72377389C0F] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Primary Key structure for table sys_filterip
-- ----------------------------
ALTER TABLE [dbo].[sys_filterip] ADD CONSTRAINT [PK__sys_filt__2C6EC7236F19E7C9] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Indexes structure for table sys_flowscheme
-- ----------------------------
CREATE UNIQUE NONCLUSTERED INDEX [sys_flowscheme_key1]
ON [dbo].[sys_flowscheme] (
  [F_SchemeCode] ASC
)
GO


-- ----------------------------
-- Primary Key structure for table sys_flowscheme
-- ----------------------------
ALTER TABLE [dbo].[sys_flowscheme] ADD CONSTRAINT [PK__sys_flow__2C6EC72308DCA9DB] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Indexes structure for table sys_form
-- ----------------------------
CREATE UNIQUE NONCLUSTERED INDEX [sys_form_key1]
ON [dbo].[sys_form] (
  [F_Name] ASC
)
GO

EXEC sp_addextendedproperty
'MS_Description', N'唯一',
'SCHEMA', N'dbo',
'TABLE', N'sys_form',
'INDEX', N'sys_form_key1'
GO


-- ----------------------------
-- Primary Key structure for table sys_form
-- ----------------------------
ALTER TABLE [dbo].[sys_form] ADD CONSTRAINT [PK__sys_form__2C6EC723B7085DF6] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Indexes structure for table sys_items
-- ----------------------------
CREATE UNIQUE NONCLUSTERED INDEX [sys_items_key1]
ON [dbo].[sys_items] (
  [F_EnCode] ASC
)
GO

EXEC sp_addextendedproperty
'MS_Description', N'唯一键',
'SCHEMA', N'dbo',
'TABLE', N'sys_items',
'INDEX', N'sys_items_key1'
GO


-- ----------------------------
-- Primary Key structure for table sys_items
-- ----------------------------
ALTER TABLE [dbo].[sys_items] ADD CONSTRAINT [PK__sys_item__2C6EC723A092701F] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Indexes structure for table sys_itemsdetail
-- ----------------------------
CREATE UNIQUE NONCLUSTERED INDEX [sys_itemsdetail_key1]
ON [dbo].[sys_itemsdetail] (
  [F_ItemId] ASC,
  [F_ItemCode] ASC
)
GO

EXEC sp_addextendedproperty
'MS_Description', N'唯一键',
'SCHEMA', N'dbo',
'TABLE', N'sys_itemsdetail',
'INDEX', N'sys_itemsdetail_key1'
GO


-- ----------------------------
-- Primary Key structure for table sys_itemsdetail
-- ----------------------------
ALTER TABLE [dbo].[sys_itemsdetail] ADD CONSTRAINT [PK__sys_item__2C6EC7233965730B] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Primary Key structure for table sys_log
-- ----------------------------
ALTER TABLE [dbo].[sys_log] ADD CONSTRAINT [PK__sys_log__2C6EC7231BFD14AC] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Indexes structure for table sys_module
-- ----------------------------
CREATE UNIQUE NONCLUSTERED INDEX [sys_module_key1]
ON [dbo].[sys_module] (
  [F_EnCode] ASC
)
GO

EXEC sp_addextendedproperty
'MS_Description', N'唯一键',
'SCHEMA', N'dbo',
'TABLE', N'sys_module',
'INDEX', N'sys_module_key1'
GO


-- ----------------------------
-- Primary Key structure for table sys_module
-- ----------------------------
ALTER TABLE [dbo].[sys_module] ADD CONSTRAINT [PK__sys_modu__2C6EC723378CC01A] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Indexes structure for table sys_modulebutton
-- ----------------------------
CREATE UNIQUE NONCLUSTERED INDEX [sys_modulebutton_key1]
ON [dbo].[sys_modulebutton] (
  [F_ModuleId] ASC,
  [F_ParentId] ASC,
  [F_EnCode] ASC
)
GO

EXEC sp_addextendedproperty
'MS_Description', N'唯一键',
'SCHEMA', N'dbo',
'TABLE', N'sys_modulebutton',
'INDEX', N'sys_modulebutton_key1'
GO


-- ----------------------------
-- Primary Key structure for table sys_modulebutton
-- ----------------------------
ALTER TABLE [dbo].[sys_modulebutton] ADD CONSTRAINT [PK__sys_modu__2C6EC723D6D79974] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Indexes structure for table sys_modulefields
-- ----------------------------
CREATE UNIQUE NONCLUSTERED INDEX [sys_modulefields_key1]
ON [dbo].[sys_modulefields] (
  [F_ModuleId] ASC,
  [F_EnCode] ASC
)
GO

EXEC sp_addextendedproperty
'MS_Description', N'唯一键',
'SCHEMA', N'dbo',
'TABLE', N'sys_modulefields',
'INDEX', N'sys_modulefields_key1'
GO


-- ----------------------------
-- Primary Key structure for table sys_modulefields
-- ----------------------------
ALTER TABLE [dbo].[sys_modulefields] ADD CONSTRAINT [PK__sys_modu__2C6EC72383656FED] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Indexes structure for table sys_notice
-- ----------------------------
CREATE UNIQUE NONCLUSTERED INDEX [sys_notice_key1]
ON [dbo].[sys_notice] (
  [F_Title] ASC
)
GO

EXEC sp_addextendedproperty
'MS_Description', N'唯一键',
'SCHEMA', N'dbo',
'TABLE', N'sys_notice',
'INDEX', N'sys_notice_key1'
GO


-- ----------------------------
-- Primary Key structure for table sys_notice
-- ----------------------------
ALTER TABLE [dbo].[sys_notice] ADD CONSTRAINT [PK__sys_noti__2C6EC723E0859AC2] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Primary Key structure for table sys_openjob
-- ----------------------------
ALTER TABLE [dbo].[sys_openjob] ADD CONSTRAINT [PK__sys_open__2C6EC7233E52440B] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Primary Key structure for table sys_openjoblog
-- ----------------------------
ALTER TABLE [dbo].[sys_openjoblog] ADD CONSTRAINT [PK__sys_open__2C6EC723E76897B9] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Indexes structure for table sys_organize
-- ----------------------------
CREATE UNIQUE NONCLUSTERED INDEX [sys_organize_key1]
ON [dbo].[sys_organize] (
  [F_EnCode] ASC
)
GO

EXEC sp_addextendedproperty
'MS_Description', N'唯一键',
'SCHEMA', N'dbo',
'TABLE', N'sys_organize',
'INDEX', N'sys_organize_key1'
GO


-- ----------------------------
-- Primary Key structure for table sys_organize
-- ----------------------------
ALTER TABLE [dbo].[sys_organize] ADD CONSTRAINT [PK__sys_orga__2C6EC7238FCE5A71] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Indexes structure for table sys_quickmodule
-- ----------------------------
CREATE UNIQUE NONCLUSTERED INDEX [sys_quickmodule_key1]
ON [dbo].[sys_quickmodule] (
  [F_ModuleId] ASC,
  [F_CreatorUserId] ASC
)
GO

EXEC sp_addextendedproperty
'MS_Description', N'唯一键',
'SCHEMA', N'dbo',
'TABLE', N'sys_quickmodule',
'INDEX', N'sys_quickmodule_key1'
GO


-- ----------------------------
-- Primary Key structure for table sys_quickmodule
-- ----------------------------
ALTER TABLE [dbo].[sys_quickmodule] ADD CONSTRAINT [PK__sys_quic__2C6EC72399178528] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Indexes structure for table sys_role
-- ----------------------------
CREATE UNIQUE NONCLUSTERED INDEX [sys_role_key1]
ON [dbo].[sys_role] (
  [F_EnCode] ASC
)
GO

EXEC sp_addextendedproperty
'MS_Description', N'唯一键',
'SCHEMA', N'dbo',
'TABLE', N'sys_role',
'INDEX', N'sys_role_key1'
GO


-- ----------------------------
-- Primary Key structure for table sys_role
-- ----------------------------
ALTER TABLE [dbo].[sys_role] ADD CONSTRAINT [PK__sys_role__2C6EC723A73B1012] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Primary Key structure for table sys_roleauthorize
-- ----------------------------
ALTER TABLE [dbo].[sys_roleauthorize] ADD CONSTRAINT [PK__sys_role__2C6EC7235BB03629] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Indexes structure for table sys_serverstate
-- ----------------------------
CREATE UNIQUE NONCLUSTERED INDEX [IX_Sys_ServerState]
ON [dbo].[sys_serverstate] (
  [F_WebSite] ASC,
  [F_Date] ASC
)
GO

EXEC sp_addextendedproperty
'MS_Description', N'唯一键',
'SCHEMA', N'dbo',
'TABLE', N'sys_serverstate',
'INDEX', N'IX_Sys_ServerState'
GO


-- ----------------------------
-- Primary Key structure for table sys_serverstate
-- ----------------------------
ALTER TABLE [dbo].[sys_serverstate] ADD CONSTRAINT [PK__sys_serv__2C6EC7238622D477] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Indexes structure for table sys_systemset
-- ----------------------------
CREATE UNIQUE NONCLUSTERED INDEX [sys_systemset_key1]
ON [dbo].[sys_systemset] (
  [F_DbNumber] ASC
)
GO


-- ----------------------------
-- Primary Key structure for table sys_systemset
-- ----------------------------
ALTER TABLE [dbo].[sys_systemset] ADD CONSTRAINT [PK__sys_syst__2C6EC72365900F98] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Primary Key structure for table sys_template
-- ----------------------------
ALTER TABLE [dbo].[sys_template] ADD CONSTRAINT [PK__sys_temp__2C6EC723A210694A] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Indexes structure for table sys_user
-- ----------------------------
CREATE UNIQUE NONCLUSTERED INDEX [sys_user_key1]
ON [dbo].[sys_user] (
  [F_Account] ASC
)
GO

EXEC sp_addextendedproperty
'MS_Description', N'唯一键',
'SCHEMA', N'dbo',
'TABLE', N'sys_user',
'INDEX', N'sys_user_key1'
GO


-- ----------------------------
-- Primary Key structure for table sys_user
-- ----------------------------
ALTER TABLE [dbo].[sys_user] ADD CONSTRAINT [PK__sys_user__2C6EC723D855CFB2] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Indexes structure for table sys_userlogon
-- ----------------------------
CREATE UNIQUE NONCLUSTERED INDEX [sys_userlogon_key1]
ON [dbo].[sys_userlogon] (
  [F_UserId] ASC
)
GO


-- ----------------------------
-- Primary Key structure for table sys_userlogon
-- ----------------------------
ALTER TABLE [dbo].[sys_userlogon] ADD CONSTRAINT [PK__sys_user__2C6EC72387C53FFC] PRIMARY KEY CLUSTERED ([F_Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

