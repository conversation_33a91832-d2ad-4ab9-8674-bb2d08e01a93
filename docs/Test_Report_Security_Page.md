# “账号与安全”页面测试报告

## 1. 测试概述

本次测试旨在验证“账号与安全”页面的导航、UI和核心功能是否符合预期。

**测试范围:**

*   **导航验证**: 从“我的”页面到“账号与安全”页面的跳转。
*   **UI 验证**: “账号与安全”页面的界面布局和元素。
*   **功能验证**: 用户邮箱信息的数据绑定和显示。

## 2. 测试结果

| 测试项 | 预期结果 | 实际结果 | 状态 |
| --- | --- | --- | --- |
| **导航验证** | 从“我的”页面点击“账号与安全”后，应跳转到“账号与安全”页面。 | 导航功能正常，页面按预期跳转。 | ✅ 通过 |
| **UI 验证** | 页面 UI 应与设计图一致。 | 由于缺少设计图，仅基于代码进行了基础 UI 审查。 | ⚠️ 注意 |
| **功能验证** | 页面应能正确获取并显示用户的邮箱信息。 | **功能缺陷**: `email` getter 未在 Vuex store 中定义，导致邮箱信息始终显示为“暂未绑定”。 | ❌ 失败 |

## 3. 发现的缺陷

### 3.1. 功能缺陷：缺少 `email` Getter

**问题描述:**

`pages/my/security.vue` 组件尝试通过 `mapGetters(['email'])` 从 Vuex store 获取邮箱信息，但 `store/modules/user.js` 中并未定义 `email` getter。这导致 `email` 的值始终为 `undefined`，页面无法正确显示用户的邮箱地址。

**复现步骤:**

1.  导航到“账号与安全”页面。
2.  观察“绑定邮箱”列表项。
3.  无论用户是否已登录或绑定邮箱，该项始终显示“暂未绑定”。

**建议修复:**

在 `store/modules/user.js` 的 `getters` 对象中添加 `email` getter，以从 `user` 状态中正确返回邮箱信息。

**代码示例:**

```javascript
// store/modules/user.js

getters: {
  user: state => {
    if (state.user) {
      return state.user
    }
    return Vue.prototype.$cache.get('_userInfo')
  },
  // 新增 email getter
  email: (state, getters) => {
    return getters.user ? getters.user.email : null
  }
}
```

## 4. 总结与建议

*   **导航功能** 已按预期实现。
*   **UI 验证** 因缺少设计图而未完全执行。建议在后续测试中提供设计图以进行全面的 UI 核对。
*   **功能验证** 发现一个关键缺陷，需要开发人员修复。

建议开发团队优先修复缺失的 `email` getter，以确保核心功能正常运行。