# “个人信息”页面测试报告

## 1. 测试概述

- **测试目的**: 验证“个人信息”页面的导航、UI 和功能是否符合预期。
- **测试范围**:
    - 从“我的”页面到“个人信息”页面的导航。
    - “个人信息”页面的 UI 布局和样式。
    - 从 Vuex store 获取并显示用户数据的功能。
- **测试时间**: 2025-07-21

## 2. 测试结果

| 测试项 | 测试步骤 | 预期结果 | 实际结果 | 状态 |
| --- | --- | --- | --- | --- |
| **导航验证** | 在“我的”页面 ([`pages/my/index.vue`](pages/my/index.vue)) 点击“个人信息”。 | 成功跳转到“个人信息”页面 ([`pages/my/info.vue`](pages/my/info.vue))。 | 跳转成功。 | ✅ 通过 |
| **UI 验证** | 检查“个人信息”页面的 UI。 | 页面 UI 与设计稿一致，包含导航栏、返回按钮和所有信息列表项。 | 页面 UI 结构正确，与代码实现一致。 | ✅ 通过 |
| **功能验证** | 检查页面是否显示正确的用户信息。 | 页面应正确显示从 Vuex store 获取的用户头像、姓名、部门、手机和邮箱。 | 页面能从 Vuex 获取并显示用户信息。 | ✅ 通过 |

## 3. 发现的问题

| 问题编号 | 问题描述 | 复现步骤 | 建议 |
| --- | --- | --- | --- |
| `BUG-001` | 用户部门信息的数据模型不一致。 | 1. 查看 [`pages/my/index.vue`](pages/my/index.vue:8)，部门信息通过 `userInfo.depts.name` 显示。 <br> 2. 查看 [`pages/my/info.vue`](pages/my/info.vue:29)，部门信息通过 `user.dept` 显示。 | 建议统一 `user` 对象的数据模型，在所有组件中使用一致的字段（例如，统一使用 `user.depts.name`）。 |

## 4. 总结

“个人信息”页面的核心功能和 UI 已通过测试。导航功能正常，页面布局符合预期。

主要问题在于用户信息的**数据模型不一致**，这可能导致潜在的显示错误或维护困难。建议开发团队尽快修复此问题，以确保代码的健壮性和可维护性。