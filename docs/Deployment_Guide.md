# 部署与发布指南 (微信小程序)

本文档描述了如何将此 Uni-app 项目编译并发布为微信小程序。

## 1. 环境要求
- **HBuilderX**: 最新版本的 HBuilderX IDE。
- **微信开发者工具**: 已安装并登录了开发者账号。
- **微信小程序 AppID**: 已在微信公众平台注册并获取。

## 2. 编译与发布流程

### 步骤 1: 配置 AppID
1.  打开项目根目录下的 `manifest.json` 文件。
2.  在 "微信小程序配置" 选项卡中，填入你的微信小程序 AppID。

### 步骤 2: 编译到小程序平台
1.  在 HBuilderX 的顶部菜单栏中，选择 "发行"。
2.  在下拉菜单中，选择 "小程序-微信(仅适用于uni-app)"。
3.  在弹出的窗口中，填写本次发布的相关信息（如版本号），然后点击 "发行" 按钮。
4.  HBuilderX 将会自动编译项目，并在完成后启动微信开发者工具，加载编译后的代码。

### 步骤 3: 在微信开发者工具中预览和上传
1.  在微信开发者工具中，确认项目已成功加载。
2.  点击 "预览" 生成预览二维码，用手机微信扫码可以在真机上进行测试。
3.  确认所有功能正常后，点击 "上传"。
4.  填写版本信息，然后点击 "上传" 按钮，将代码包上传至微信公众平台。

## 3. 在微信公众平台发布
1.  登录 [微信公众平台](https://mp.weixin.qq.com/)。
2.  进入 "版本管理" 页面。
3.  你会看到刚刚上传的版本，可以将其设置为体验版，供内部人员测试。
4.  测试无误后，提交审核。
5.  审核通过后，即可点击 "发布"，将新版本小程序发布给所有用户。

## 4. API 服务器部署
小程序的后端 API 服务需要独立部署。API 服务的部署指南请参考后端项目的相关文档。本指南仅涵盖前端小程序部分的发布流程。