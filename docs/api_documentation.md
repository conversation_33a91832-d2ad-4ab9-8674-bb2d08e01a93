# API 接口文档

本文档详细描述了 WaterCloud 项目的后端 API 接口，并结合权威的数据库模型对接口功能进行说明。

## 1. 通用说明

- **基地址**: 所有API的路由都以 `/api` 开头。
- **认证**: 大部分接口需要通过在请求头中传递 `Authorization` 令牌进行认证。
- **数据格式**: 请求和响应主体均为 `application/json` 格式。
- **路由格式**: `api/[controller]/[action]`

---

## 2. 用户与权限模块 (`UserController`)

管理用户、角色、组织及相关权限。

### 2.1 登录与会话

- **接口路径**: `POST /api/User/WechatLogin`
  - **功能描述**: 微信小程序一键登录。
- **接口路径**: `POST /api/User/Login`
  - **功能描述**: 标准用户名密码登录。
- **接口路径**: `POST /api/User/LoginOff`
  - **功能描述**: 用户退出登录。
- **接口路径**: `POST /api/User/CheckLoginState`
  - **功能描述**: 验证当前用户的Token是否有效。

### 2.2 用户与组织查询

- **接口路径**: `GET /api/User/GetUserList`
  - **功能描述**: 分页获取 `sys_user` 表中的用户列表。
- **接口路径**: `GET /api/User/GetRoleList`
  - **功能描述**: 获取 `sys_role` 表中所有可用的角色。
- **接口路径**: `GET /api/User/GetTreeWithUserJson`
  - **功能描述**: 获取 `sys_organize` 和 `sys_user` 构建的组织用户树，用于人员选择器。

### 2.3 权限设置

- **接口路径**: `POST /api/User/SettingRole`
  - **功能描述**: 为指定用户更新其在 `sys_user` 表中的 `F_RoleId` 字段。

---

## 3. EHS核心业务模块 (`TaskController`, `EventController`)

管理作业任务、承包商、安全事故等核心EHS业务。

### 3.1 承包商管理

- **接口路径**: `GET /api/Task/GetContractorList`
  - **功能描述**: 分页获取 `oms_contractor` 表中的承包商列表。
- **接口路径**: `GET /api/Task/GetContractorInfo`
  - **功能描述**: 获取单个承包商在 `oms_contractor` 表的详细信息。
- **接口路径**: `POST /api/Task/ContractorSave`
  - **功能描述**: 新增或修改 `oms_contractor` 表中的承包商信息。
- **接口路径**: `GET /api/Task/GetStaffList`
  - **功能描述**: 获取指定承包商下的所有员工（查询 `oms_contractorstaff` 表）。
- **接口路径**: `GET /api/Task/GetStaffInfo`
  - **功能描述**: 获取 `oms_contractorstaff` 表中单个员工的详细信息。
- **接口路径**: `POST /api/Task/StaffSave`
  - **功能描述**: 新增或修改 `oms_contractorstaff` 表中的员工信息。
- **接口路径**: `POST /api/Task/DelStaffInfo`
  - **功能描述**: 删除 `oms_contractorstaff` 表中的一名员工。

### 3.2 作业任务管理

- **接口路径**: `GET /api/Task/GetTaskList`
  - **功能描述**: 根据条件分页查询 `oms_task` 表中的作业任务。
- **接口路径**: `GET /api/Task/GetTaskInfo`
  - **功能描述**: 获取 `oms_task` 表中单个作业任务的详细信息。
- **接口路径**: `GET /api/Task/GetSimpleTaskInfo`
  - **功能描述**: 获取任务详情，包含关联的 `oms_contractorstaff` 和 `oms_contractor` 信息。
- **接口路径**: `GET /api/Task/GetGroupTaskInfo`
  - **功能描述**: 获取一个主任务及其所有风险子任务的摘要信息，数据来源于 `oms_task` 表的自关联。
- **接口路径**: `POST /api/Task/SaveTask`
  - **功能描述**: 保存对 `oms_task` 表中一个作业任务的修改。
- **接口路径**: `POST /api/Task/CompleteTask`
  - **功能描述**: 更新 `oms_task` 表中指定任务的状态为已完成。

### 3.3 事故管理

- **接口路径**: `GET /api/Event/GetOrgList`
  - **功能描述**: 获取 `sys_organize` 表中用于筛选的组织（部门）列表。
- **接口路径**: `GET /api/Event/GetEventList`
  - **功能描述**: 分页和筛选获取 `oms_event` 表中的事故记录列表。
- **接口路径**: `GET /api/Event/GetEventInfo`
  - **功能描述**: 获取 `oms_event` 表中单个事故的详细信息。
- **接口路径**: `POST /api/Event/EventSave`
  - **功能描述**: 新增或修改 `oms_event` 表中的事故记录。

### 3.4 统计与图表

- **接口路径**: `GET /api/Task/GetChartsJson`
  - **功能描述**: 聚合 `oms_task` 数据，用于生成任务相关的统计图表。
- **接口路径**: `GET /api/Task/GetIndexData`
  - **功能描述**: 获取首页/大屏展示所需的聚合数据，数据来源于 `oms_planview`, `oms_task` 等表。
- **接口路径**: `GET /api/Event/GetChartsJson`
  - **功能描述**: 按年份范围和事故等级统计 `oms_event` 数据，用于生成柱状图。
- **接口路径**: `GET /api/Event/GetChartsByYearJson`
  - **功能描述**: 获取指定年份内，`oms_event` 和 `oms_trct` 的月度统计数据。
- **接口路径**: `GET /api/Event/GetChartsByMonthJson`
  - **功能描述**: 获取指定月份每日的最高事故等级（源于 `oms_event`），用于安全日历。
- **接口路径**: `GET /api/Event/GetChartsByDayJson`
  - **功能描述**: 获取指定某一天的所有 `oms_event` 事故记录。
- **接口路径**: `GET /api/Event/GetDays`
  - **功能描述**: 基于 `oms_event` 统计并返回无损工天数。

---

## 4. 工作流模块 (`FlowConfigController`)

管理工作流的设计与执行。

- **接口路径**: `GET /FlowManage/FlowConfig/GetConfig`
  - **功能描述**: 根据流程编码，获取 `sys_flowscheme` 表中存储的流程配置JSON。
- **接口路径**: `POST /FlowManage/FlowConfig/SaveConfig`
  - **功能描述**: 保存对 `sys_flowscheme` 表中指定流程的二次配置更改。

---

## 5. 文件管理模块 (`FileController`)

- **接口路径**: `POST /api/File/Upload`
  - **功能描述**: 上传文件，并将文件元数据存入 `oms_uploadfile` 表。
  - **查询参数**:
    - `fileby`: `string` - 文件用途描述 (e.g., "公司logo", "附件")
    - `filetype`: `int` - 文件类型 (1:图片, 2:Excel, 3:模板, 4:通用文档)
  - **请求体**: `multipart/form-data`，包含一个或多个文件。

---

## 6. 其他模块

### 6.1 内容管理 (`ArticleController` - 推测)
*此部分为根据数据库表结构推测，实际接口需代码确认。*

- **推测接口**: `GET /api/Article/GetCategoryList`
  - **功能描述**: 获取 `cms_articlecategory` 表中的文章分类。
- **推测接口**: `GET /api/Article/GetArticleList`
  - **功能描述**: 分页获取 `cms_articlenews` 表中的文章列表。
- **推测接口**: `POST /api/Article/SaveArticle`
  - **功能描述**: 新增或修改 `cms_articlenews` 表中的文章。

### 6.2 订单管理 (`OrderController` - 推测)
*此部分为根据数据库表结构推测，实际接口需代码确认。*

- **推测接口**: `GET /api/Order/GetOrderList`
  - **功能描述**: 分页获取 `crm_order` 表中的订单。
- **推测接口**: `GET /api/Order/GetOrderDetail`
  - **功能描述**: 获取指定订单的所有明细（查询 `crm_orderdetail` 表）。
- **推测接口**: `POST /api/Order/SaveOrder`
  - **功能描述**: 新增或修改 `crm_order` 及关联的 `crm_orderdetail`。