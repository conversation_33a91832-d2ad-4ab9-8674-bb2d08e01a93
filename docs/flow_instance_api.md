### **新接口定义：获取流转中心列表 (支持复杂筛选)**

**1. 功能概述**

此接口专为“流转中心”页面设计，旨在替代原有的 `getApprovalList`。它支持更复杂的筛选逻辑，特别是允许前端传递一个结构化的对象，以便同时筛选多个流程下的多个状态。

**2. 接口详情**

*   **接口地址**: `/api/Flow/GetFlowInstanceList`
*   **请求方式**: `POST`
    *   **说明**: 采用 `POST` 是因为筛选条件（特别是 `statusFilters`）是一个复杂的JSON对象，放在 `POST` 的请求体中比放在 `GET` 的URL参数中更清晰、更健壮，且没有长度限制。
*   **Content-Type**: `application/json`

**3. 请求参数 (Request Body)**

| 参数名 | 类型 | 是否必填 | 描述 |
| :--- | :--- | :--- | :--- |
| `page` | `int` | 是 | 当前页码，从 `1` 开始。 |
| `rows` | `int` | 是 | 每页显示的记录数。 |
| `keyword` | `string` | 否 | 搜索关键字，匹配流程标题、编码等。 |
| `departmentId` | `string` | 否 | 部门ID，用于按部门筛选。 |
| `beginTime` | `string` | 否 | 开始时间，格式 `YYYY-MM-DD`。 |
| `endTime` | `string` | 否 | 结束时间，格式 `YYYY-MM-DD`。 |
| `statusFilters` | `object` | 否 | **核心筛选参数**：一个对象，键为流程的完整名称，值为一个包含该流程下所有已选状态的字符串数组。 |

**`statusFilters` 参数结构示例：**

如果用户选择了“变更审批”下的“待评估”和“待审批”，以及“化学品审批”下的“待批准”，那么 `statusFilters` 对象应如下所示：

```json
{
  "变更审批": ["待评估", "待审批"],
  "新化学品审批": ["待批准"]
}
```

**完整请求体示例：**

```json
{
  "page": 1,
  "rows": 10,
  "keyword": "紧急",
  "departmentId": "DEPT_001",
  "beginTime": "2024-05-10",
  "endTime": "2024-05-10",
  "statusFilters": {
    "变更审批": ["待评估", "待审批"],
    "新化学品审批": ["待批准"]
  }
}
```

**4. 返回参数 (Response Body)**

**成功响应示例：**

```json
{
  "state": 0,
  "message": "获取成功",
  "data": {
    "total": 99,
    "list": [
      {
        "F_Id": "flow-instance-id-123",
        "F_Code": "202502190001",
        "F_SchemeName": "变更审批",
        "F_FlowStatus": "待审批",
        "F_CreatorUserName": "张三",
        "F_CreatorTime": "2025-09-09 10:00",
        "F_SummaryFields": [
          { "label": "作业区域", "value": "冲压点击、机加工" },
          { "label": "具体位置", "value": "加工厂区域" },
          { "label": "变更内容", "value": "变更内容详情..." }
        ]
      }
      // ... 更多列表项
    ]
  }
}
```

**`data.list` 中每个对象的字段说明：**

| 字段名 | 类型 | 描述 |
| :--- | :--- | :--- |
| `F_Id` | `string` | 流程实例的唯一ID。 |
| `F_Code` | `string` | 审批编号。 |
| `F_SchemeName` | `string` | 流程类型名称，如“变更审批”。 |
| `F_FlowStatus` | `string` | 流程当前的状态，如“待审批”。 |
| `F_CreatorUserName` | `string` | 发起人姓名。 |
| `F_CreatorTime` | `string` | 发起时间。 |
| `F_SummaryFields` | `array` | **卡片核心内容**：一个对象数组，用于动态渲染卡片中部的摘要信息。每个对象包含 `label` (标签) 和 `value` (值)。 |