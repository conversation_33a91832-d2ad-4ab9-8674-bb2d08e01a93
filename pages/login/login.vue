<template>
	<view class="login login-bg w-h-100" :class="darkMode?'custom-dark':'custom-light'">
		<view class="content">
			<!-- 头部logo -->
			<view class="header">
				<image :src="logoImage"></image>
				<text class="title">EHS-掌上观纹</text>
			</view>
			<!-- 主体表单 -->
			<view class="main">
				<wInput v-model="phoneData" type="text" maxlength="20" placeholder="请输入账号"></wInput>
				<wInput v-model="passData" type="password" maxlength="20" placeholder="请输入登录密码" isShowPass></wInput>
			</view>
			<wButton text="账号密码登录" :rotate="isRotate" @click.native="startLogin()"></wButton>
			
			<!-- 微信快速登录 -->
			<!-- #ifdef MP-WEIXIN -->
			<view class="wechat-login">
				<view class="divider">
					<text>或</text>
				</view>
				<button 
					:class="['buttonBorder',!wechatLoading?'dlbutton':'dlbutton_loading']" 
					style="background: linear-gradient(to right, #00984a, #00984a, #00984a); color: #FFFFFF;display: flex;justify-content: center;width: 601rpx;height: 100rpx;line-height: 100rpx;border-radius: 50rpx;"
					open-type="getPhoneNumber" 
					@getphonenumber="handleWechatLogin"
				>
					<view :class="wechatLoading?'rotate_loop':''" style="font-size: 15px;line-height: 100rpx; display: flex; align-items: center; justify-content: center;">
						<text v-if="wechatLoading" class="cuIcon cuIcon-loading1"></text>
						<view v-if="!wechatLoading" style="display: flex; align-items: center;">
							<image src="/static/img/wechat.svg" style="width: 40rpx; height: 40rpx; margin-right: 12rpx;"></image>
							<text style="font-size: 15px;">微信手机号快速登录</text>
						</view>
					</view>
				</button>
			</view>
			<!-- #endif -->
		</view>
	</view>
</template>

<script>
	import { mapGetters } from 'vuex'
	import wInput from '@/components/watch-login/watch-input.vue'
	import wButton from '@/components/watch-login/watch-button.vue'
	import md5 from '@/common/lib/md5.min.js'
	import globalConfig from '@/config'

	export default {
		computed: {
			...mapGetters(['themeBgColor', 'darkMode']),
		},
		data() {
			return {
				baseUrl: '',
				// logo图片
				logoImage: '/static/img/logo.png',
				phoneData: '', //用户/电话
				passData: '', //密码
				isRotate: false, //是否加载旋转
				wechatLoading: false, //微信登录加载状态
			};
		},
		components: {
			wInput,
			wButton,
		},
		onLoad(options) {
			this.baseUrl = globalConfig.baseUrl.replace('/api', '');
			if (options.errorMsg) {
				uni.showToast({
					title: options.errorMsg,
					icon: 'none'
				})
			}
		},
		onReady() {
			this.setNavBarColor()
		},
		onShow() {
			this.setNavBarColor()
		},
		onShareAppMessage() {
		    return {
		      title: 'EHS-掌上观纹', // 自定义分享标题
		      path: '/pages/start/index', // 分享页面路径（默认当前页）
		      imageUrl: this.baseUrl+'/images/nideke.jpg', // 分享图片（可选）
		      success(res) {
		        console.log('分享成功', res);
		      },
		      fail(err) {
		        console.error('分享失败', err);
		      }
		    };
		},
		methods: {
			login(params) {
				if (params) {
					this.isRotate = true
					this.$store.dispatch('login', params).then(res => {
						this.isRotate = false
						console.log('res:==>',res)
						uni.reLaunch({
							url: '/pages/home/<USER>',
						})
					}).catch(() => {
						this.isRotate = false
					})
				}
			},
			startLogin() {
				//登录
				if (this.isRotate) {
					//判断是否加载中，避免重复点击请求
					return false;
				}
				if (this.phoneData.length == "") {
					uni.showToast({
						icon: 'none',
						position: 'bottom',
						title: '用户名不能为空'
					});
					return;
				}
				if (this.passData.length == "") {
					uni.showToast({
						icon: 'none',
						position: 'bottom',
						title: '密码不能为空'
					});
					return;
				}

				// 网络请求
				const params = {
					userName: this.phoneData,
					password: this.passData
				}
				this.login(params)
			},
			setNavBarColor() {
				// navBar-bg-color
				uni.setNavigationBarColor({
				    frontColor: '#000000',
				    backgroundColor: this.themeBgColor,
				    animation: {
				        duration: 400,
				        timingFunc: 'easeIn'
				    }
				})
			},
			// 微信登录处理
			async handleWechatLogin(e) {
				if (this.wechatLoading) return;
				
				console.log('获取手机号回调:', e);
				
				if (e.detail.errMsg !== 'getPhoneNumber:ok') {
					uni.showToast({
						title: '获取手机号失败',
						icon: 'none'
					});
					return;
				}
				
				this.wechatLoading = true;
				
				try {
					// 获取微信登录code
					const loginRes = await this.getWechatCode();
					
					// 构造请求参数 - 不再获取用户信息，因为getUserProfile已被废弃
					const params = {
						phoneCode: e.detail.code,
						code: loginRes.code,
						avatarUrl: '', // 使用默认值
						nickName: '微信用户' // 使用默认值
					};
					
					// 调用微信登录
					await this.wechatLogin(params);
					
				} catch (error) {
					console.error('微信登录失败:', error);
					uni.showToast({
						title: error.message || '微信登录失败',
						icon: 'none'
					});
				} finally {
					this.wechatLoading = false;
				}
			},
			// 获取微信code
			getWechatCode() {
				return new Promise((resolve, reject) => {
					uni.login({
						provider: 'weixin',
						success: (res) => {
							if (res.code) {
								resolve(res);
							} else {
								reject(new Error('获取微信授权码失败'));
							}
						},
						fail: (err) => {
							reject(new Error('微信授权失败'));
						}
					});
				});
			},

			// 调用微信登录接口
			wechatLogin(params) {
				return new Promise((resolve, reject) => {
					this.$store.dispatch('wechatLogin', params).then(res => {
						console.log('微信登录成功:', res);
						uni.reLaunch({
							url: '/pages/home/<USER>',
						});
						resolve(res);
					}).catch(error => {
						reject(error);
					});
				});
			},
		}
	}
</script>

<style>
	@import url("../../components/watch-login/css/icon.css");
	@import url("./css/main.css");

	.login-bg {
		/* width: 100%;
		height: 100%;
		background-image: url('/static/img/login-bg.png');
		background-size: cover;
		background-position: 50%;
		position: absolute; */
	}

	.login-bg .content .header image {
		width: 160rpx;
		height: 160rpx;
	}

	/* .login-bg .content .header {
		width: 241rpx;
		height: 241rpx;
		background-size: 241rpx;
		background-image: url('/static/img/logo.png');
	} */

	.wechat-login {
		/* margin-top: 60rpx; */
	}

	.divider {
		text-align: center;
		margin: 40rpx 0;
		position: relative;
	}

	.divider text {
		background: var(--bg-color);
		padding: 0 20rpx;
		color: #999;
		font-size: 28rpx;
	}

	.divider::before {
		content: '';
		position: absolute;
		top: 50%;
		left: 0;
		right: 0;
		height: 1rpx;
		background: #e5e5e5;
		z-index: -1;
	}
</style>
