<template>
	<view class="page-container">
		<!-- 搜索与筛选区域 -->
		<view class="search-filter-container">
			<!-- 关键字搜索框 -->
			<view class="search-bar">
				<uni-icons type="search" size="20" color="#999"></uni-icons>
				<input v-model="searchParams.keyword" class="search-input" placeholder="请输入搜索内容" @confirm="handleSearch" />
			</view>

			<!-- 日期范围选择 -->
			<view class="date-range-picker">
				<picker mode="date" @change="onDateChange($event, 'beginTime')" :value="searchParams.beginTime">
					<view class="date-picker-input">
						<uni-icons type="calendar" size="16" color="#999"></uni-icons>
						<text>{{ searchParams.beginTime || '开始日期' }}</text>
					</view>
				</picker>
				<picker mode="date" @change="onDateChange($event, 'endTime')" :value="searchParams.endTime">
					<view class="date-picker-input">
						<uni-icons type="calendar" size="16" color="#999"></uni-icons>
						<text>{{ searchParams.endTime || '结束日期' }}</text>
					</view>
				</picker>
			</view>

			<!-- 分类筛选按钮 (仅在非被拒回状态下显示) -->
			<view class="category-filters" v-if="searchParams.status != 3">
				<button class="filter-btn" :class="{ 'active': activeCategory === '变更审批' }" @click="filterByCategory('变更审批')">
					变更审批
					<uni-badge :text="counts['变更审批'] || 0" type="error" size="small"></uni-badge>
				</button>
				<button class="filter-btn" :class="{ 'active': activeCategory === '新化学品审批' }" @click="filterByCategory('新化学品审批')">
					化学品审批
					<uni-badge :text="counts['新化学品审批'] || 0" type="error" size="small"></uni-badge>
				</button>
				<button class="filter-btn" :class="{ 'active': activeCategory === '外部持入化学品审批' }" @click="filterByCategory('外部持入化学品审批')">
					外部持入化学品
					<uni-badge :text="counts['外部持入化学品审批'] || 0" type="error" size="small"></uni-badge>
				</button>
				<button class="filter-btn" :class="{ 'active': activeCategory === '双休及节假日工作审批' }" @click="filterByCategory('双休及节假日工作审批')">
					双休日及节假日
					<uni-badge :text="counts['双休及节假日工作审批'] || 0" type="error" size="small"></uni-badge>
				</button>
			</view>
		</view>

		<!-- 列表区域 -->
		<view>
			<view v-for="item in dataList" :key="item.F_Id" class="list-item-card" @click="viewDetails(item.F_Id)">
				<!-- 卡片头部 -->
				<view class="card-header">
					<view class="header-left">
						<image class="status-dot" src="/static/img/dian.svg"></image>
						<text class="code">审批编号: {{ item.F_Code }}</text>
					</view>
					<view class="header-right">
						<text class="status" :class="[getStatusClass(item.F_FlowStatus)]">{{ item.F_FlowStatus }}</text>
					</view>
				</view>

				<!-- 卡片中部 -->
				<view class="card-body">
					<view class="body-title">
						<text class="title-text">{{ item.F_SchemeName }}</text>
					</view>
					<view class="body-content">
						<view class="content-item">
							<text class="label">发起人:</text>
							<text class="value">{{ item.F_CreatorUserName }}</text>
						</view>
						<view class="content-item">
							<text class="label">发起时间:</text>
							<text class="value">{{ formatDateTime(item.F_CreatorTime) }}</text>
						</view>
					</view>
				</view>

				<!-- 卡片底部 -->
				<view class="card-footer">
					<view class="footer-left">
						<image class="avatar" src="/static/img/profile.svg"></image>
						<text class="username">{{ item.F_CreatorUserName }}</text>
					</view>
					<view class="footer-right">
						<text class="time">{{ formatDateTime(item.F_CreatorTime) }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import uniIcons from '@/components/uni-icons/uni-icons.vue';
import uniBadge from '@/components/uni-badge/uni-badge.vue';

export default {
	components: {
		uniIcons,
		uniBadge,
	},
	data() {
		return {
			dataList: [],
			searchParams: {
				keyword: '',
				beginTime: '',
				endTime: '',
				status: 0, // 页面状态, 默认为待处理
				category: '', // 流程分类
			},
			activeCategory: '',
			counts: {
				'变更审批': 0,
				'新化学品审批': 0,
				'外部持入化学品审批': 0,
				'双休及节假日工作审批': 0,
			},
			page: 1,
			pageSize: 10,
			total: 0,
			loading: false,
			hasMore: true,
		};
	},
	onReady() {
		uni.setNavigationBarTitle({
			title: this.$t('个人信息')
		})
		this.setNavBarColor()
	},
	onShow() {
		this.setNavBarColor()
		this.$minApi.checkLoginState().then(res=>{
			if(res.state == 'success'){
				
			} else{
				uni.reLaunch({
					url: '/pages/login/login'
				});
			}
		})
	},

	onLoad(options) {
		this.searchParams.status = options.status || 0;
		if (options.title) {
			uni.setNavigationBarTitle({
				title: options.title,
			});
		}
		this.getCategoryCounts();
		this.handleSearch();
	},
	onPullDownRefresh() {
		this.page = 1;
		this.hasMore = true;
		this.getList();
	},
	onReachBottom() {
		if (this.hasMore) {
			this.page++;
			this.getList();
		}
	},
	methods: {
		setNavBarColor() {
			// navBar-bg-color
			uni.setNavigationBarColor({
				frontColor: '#000000',
				// backgroundColor: '#00984a',
				backgroundColor: '#FFFFFF',
				animation: {
					duration: 400,
					timingFunc: 'easeIn'
				}
			})
		},
		// 获取分类计数
		getCategoryCounts() {
			if (this.searchParams.status == 3) {
				return;
			}
			this.$minApi.getApprovalCategoryCounts({ status: this.searchParams.status }).then(res => {
				if (res.data) {
					this.counts = Object.assign({}, this.counts, res.data);
				}
			}).catch(err => {
				console.error("Failed to get category counts:", err);
			});
		},
		// 获取列表数据
		getList() {
			if (this.loading || !this.hasMore) return;
			this.loading = true;
			const params = {
				page: this.page,
				rows: this.pageSize,
				...this.searchParams,
			};
			this.$minApi.getApprovalList(params).then(res => {
				const newItems = res.data;
				this.dataList = this.page === 1 ? newItems : this.dataList.concat(newItems);
				this.hasMore = newItems.length === this.pageSize;
				this.loading = false;
				uni.stopPullDownRefresh();
			}).catch(() => {
				this.loading = false;
				uni.stopPullDownRefresh();
			});
		},
		// 搜索
		handleSearch() {
			this.page = 1;
			this.dataList = [];
			this.hasMore = true;
			this.getList();
		},
		// 日期选择
		onDateChange(e, field) {
			this.searchParams[field] = e.detail.value;
			this.handleSearch();
		},
		// 分类筛选
		filterByCategory(category) {
			if (this.activeCategory === category) {
				this.activeCategory = '';
				this.searchParams.category = '';
			} else {
				this.activeCategory = category;
				this.searchParams.category = category;
			}
			this.handleSearch();
		},
		// 查看详情
		viewDetails(id) {
			// 根据实际情况跳转
			uni.navigateTo({
				url: '/pages/approval/detail?id=' + id
			});
		},
		// 格式化状态样式
		getStatusClass(status) {
			switch (status) {
				case '待处理':
				case '待审批':
					return 'status-approving';
				case '已驳回':
				case '已拒绝':
				case '已取消':
					return 'status-rejected';
				case '已通过':
				case '已完成':
					return 'status-verified';
				default:
					return 'status-pending';
			}
		},
		// 格式化时间
		formatDateTime(dateTime) {
			if (!dateTime) return '';
			return dateTime.split(' ');
		},
	},
};
</script>

<style scoped>
.page-container {
	background-color: #f4f4f4;
}
.search-filter-container {
	padding: 20rpx;
	background-color: #ffffff;
	margin-bottom: 20rpx;
}
.search-bar {
	display: flex;
	align-items: center;
	padding: 16rpx 24rpx;
	background-color: #f8f8f8;
	border-radius: 40rpx;
	margin-bottom: 20rpx;
}
.search-input {
	flex: 1;
	margin-left: 16rpx;
	font-size: 28rpx;
	border: none;
	background-color: transparent;
}
.date-range-picker {
	display: flex;
	justify-content: space-between;
	gap: 20rpx;
	margin-bottom: 20rpx;
}
.date-picker-input {
	display: flex;
	align-items: center;
	width: 42vw;
	padding: 16rpx 24rpx;
	background-color: #f8f8f8;
	border-radius: 20rpx;
	font-size: 28rpx;
	color: #666;
	box-sizing: border-box;
}
.date-picker-input text {
	margin-left: 16rpx;
}
.category-filters {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
}
.filter-btn {
	padding: 10rpx 30rpx;
	font-size: 30rpx;
	background-color: #f8f8f8;
	border: 2rpx solid #f0f0f0;
	border-radius: 20rpx;
	color: #666;
	display: flex;
	justify-content: space-between;
	align-items: left;
	gap: 10rpx;
	width: 46vw;
}
.filter-btn.active {
	background-color: #00984a;
	color: #ffffff;
	border-color: #00984a;
}
.filter-btn::after {
	border: none;
}
.list-item-card {
	background-color: white;
	border-radius: 16rpx;
	margin: 0 20rpx 20rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
}
.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-bottom: 20rpx;
	border-bottom: 2rpx solid #f5f5f5;
}
.header-left {
	display: flex;
	align-items: center;
}
.status-dot {
	width: 16rpx;
	height: 16rpx;
	margin-right: 16rpx;
}
.code {
	font-size: 28rpx;
	color: #333;
}
.status {
	font-size: 28rpx;
	font-weight: bold;
}
.status-pending { color: #2979ff; }
.status-approving { color: #ff9800; }
.status-rejected { color: #f44336; }
.status-verified { color: #4caf50; }
.card-body {
	padding: 30rpx 0;
}
.body-title {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}
.title-text {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}
.content-item {
	display: flex;
	font-size: 28rpx;
	color: #666;
	line-height: 1.8;
}
.label {
	width: 160rpx;
	color: #999;
}
.value {
	flex: 1;
}
.card-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-top: 20rpx;
	border-top: 2rpx solid #f5f5f5;
}
.footer-left {
	display: flex;
	align-items: center;
}
.avatar {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	margin-right: 16rpx;
}
.username {
	font-size: 28rpx;
	color: #333;
}
.time {
	font-size: 26rpx;
	color: #999;
}
</style>