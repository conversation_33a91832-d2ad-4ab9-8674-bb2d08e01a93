<template>
	<view class="page-container">
		<ledger-list
			ref="ledgerList"
			list-api-name="getExternalChemicalLedgerJson"
			stats-api-name="getExternalChemicalLedgerStatistics"
			:status-tabs="statusTabs"
			:search-params="searchParams"
		>
			<template v-slot:search>
				<view class="search-container">
				    <!-- 关键字搜索框 -->
				    <view class="search-bar">
				        <uni-icons type="search" size="20" color="#999"></uni-icons>
				        <input v-model="searchParams.keyword" class="search-input" placeholder="请输入搜索内容" @confirm="handleSearch" />
				    </view>

				    <!-- 日期范围选择 -->
				    <view class="date-range-picker">
				        <picker mode="date" @change="onDateChange($event, 'beginTime')" :value="searchParams.beginTime">
				            <view class="date-picker-input">
				                <uni-icons type="calendar" size="16" color="#999"></uni-icons>
				                <text>{{ searchParams.beginTime || '开始日期' }}</text>
				            </view>
				        </picker>
				        <picker mode="date" @change="onDateChange($event, 'endTime')" :value="searchParams.endTime">
				            <view class="date-picker-input">
				                <uni-icons type="calendar" size="16" color="#999"></uni-icons>
				                <text>{{ searchParams.endTime || '结束日期' }}</text>
				            </view>
				        </picker>
				    </view>

				    <!-- 状态筛选按钮 -->
				    <view class="filter-buttons">
				        <button class="filter-btn" :class="{ 'active': activeFilter === '已拒绝' }" @click="filterByStatus('已拒绝')">仅查看已拒绝</button>
				        <button class="filter-btn" :class="{ 'active': activeFilter === '已取消' }" @click="filterByStatus('已取消')">仅查看已取消</button>
				    </view>
				</view>
			</template>

			<template v-slot:item="{ item }">
				<view v-if="item" @click="viewDetails(item.F_Id)">
					<!-- 卡片头部 -->
					<view class="card-header">
						<view class="header-left">
							<image class="status-dot" src="/static/img/dian.svg"></image>
							<text class="code">审批编号: {{ item.F_Code }}</text>
						</view>
						<view class="header-right">
							<text class="status" :class="[getStatusClass(item.F_FlowStatus)]">{{ item.F_FlowStatus }}</text>
						</view>
					</view>

					<!-- 卡片中部 -->
					<view class="card-body">
						<view class="body-title">
							<text class="title-text">外部持入化学品审批</text>
							<uni-icons type="trash" size="20" color="#e43d33" @click.stop="handleDelete(item.F_Id)"></uni-icons>
						</view>
						<view class="body-content">
							<view class="content-item">
								<text class="label">化学品名称:</text>
								<text class="value">{{ item.F_ChemicalName }}</text>
							</view>
							<view class="content-item">
								<text class="label">使用地点:</text>
								<text class="value">{{ item.F_Location }}</text>
							</view>
							<view class="content-item">
								<text class="label">贮存位置:</text>
								<text class="value">{{ item.F_Storage }}</text>
							</view>
							<view class="content-item">
								<text class="label">化学品用途:</text>
								<text class="value">{{ item.F_Purpose }}</text>
							</view>
						</view>
					</view>

					<!-- 卡片底部 -->
					<view class="card-footer">
						<view class="footer-left">
							<image class="avatar" src="/static/img/profile.svg"></image>
							<text class="username">{{ item.F_CreatorUserName }}</text>
						</view>
						<view class="footer-right">
							<text class="time">{{ formatDateTime(item.F_CreatorTime) }}</text>
						</view>
					</view>
				</view>
			</template>
		</ledger-list>
	</view>
</template>

<script>
import LedgerList from '@/components/ledger-list/ledger-list.vue';
import uniIcons from '@/components/uni-icons/uni-icons.vue';

export default {
	components: {
		LedgerList,
		uniIcons,
	},
	data() {
		return {
			activeFilter: '', // 用于追踪当前激活的筛选按钮
			searchParams: {
				keyword: '',
				submitter: '',
				beginTime: '',
				endTime: '',
			},
			statusTabs: ['全部', '内部审批', 'EHS审批', '已通过', '已拒绝', '已取消'],
		};
	},
	methods: {
		onDateChange(e, field) {
			this.searchParams[field] = e.detail.value;
			// 可选：如果希望选择后立即搜索，可以取消下一行的注释
			// this.handleSearch();
		},
		handleSearch() {
			this.$refs.ledgerList.refresh();
		},
		filterByStatus(status) {
		    // 核心逻辑：实现单选和取消选择
		    // 1. 如果点击的按钮已经是激活状态，则取消激活
		    if (this.activeFilter === status) {
		        this.activeFilter = '';
		        this.searchParams.status = ''; // 同时清除搜索参数
		    } else {
		    // 2. 如果点击的按钮不是激活状态，则激活它
		    //    这会自动让另一个按钮的 :class 绑定失效，实现互斥
		        this.activeFilter = status;
		        this.searchParams.status = status;
		    }
		    
		    // 统一调用搜索方法刷新列表
		    this.handleSearch();
		},
		viewDetails(id) {
			uni.navigateTo({
				url: `/pages/approval/detail?type=preview&id=${id}`
			});
		},
		handleDelete(id) {
			uni.showModal({
				title: '确认删除',
				content: '确定要删除这条记录吗？',
				success: res => {
					if (res.confirm) {
						this.$minApi.deleteFlow(id).then(() => {
							uni.showToast({ title: '删除成功', icon: 'success' });
							this.$refs.ledgerList.refresh();
						});
					}
				},
			});
		},
		getStatusClass(status) {
			switch (status) {
				case '待评估':
					return 'status-pending';
				case '待审批':
				case '内部审批':
				case 'EHS审批':
					return 'status-approving';
				case '已拒绝':
				case '已取消':
					return 'status-rejected';
				case '已通过':
				case '已验证':
					return 'status-verified';
				default:
					return '';
			}
		},
		formatDateTime(dateTime) {
			if (!dateTime) return '';
			const parts = dateTime.split(' ');
			return parts.length > 0 ? parts : '';
		},
	},
};
</script>

<style scoped>
.search-container {
    padding: 20rpx;
    background-color: #ffffff;
}

.search-bar {
    display: flex;
    align-items: center;
    padding: 16rpx 24rpx;
    background-color: #f8f8f8;
    border-radius: 40rpx;
    margin-bottom: 20rpx;
}

.search-input {
    flex: 1;
    margin-left: 16rpx;
    font-size: 28rpx;
    border: none;
    background-color: transparent;
}

.date-range-picker {
    display: flex;
    justify-content: space-between;
    gap: 20rpx; /* 增加间距 */
    margin-bottom: 20rpx;
}

.date-picker-input {
    display: flex;
	align-items: center;
	width: 42vw;
	padding: 16rpx 24rpx;
	background-color: #f8f8f8;
	border-radius: 20rpx;
	font-size: 28rpx;
	color: #666;
	box-sizing: border-box;
}

.date-picker-input text {
    margin-left: 16rpx;
}

.filter-buttons {
    display: flex;
    justify-content: space-between;
}

.filter-btn {
    width: 48%;
    padding: 16rpx 0;
    font-size: 28rpx;
    background-color: #f8f8f8;
    border: 2rpx solid #f0f0f0;
    border-radius: 40rpx;
    line-height: 1.5;
    color: #666;
}

.filter-btn.active {
    background-color: #00984a; /* Tab的背景颜色 */
    color: #ffffff;
    border-color: #00984a;
}

/* 重置按钮默认样式 */
.filter-btn::after {
    border: none;
}

/* 卡片统一样式 */
.card {
	background-color: #ffffff;
	border-radius: 16rpx;
	margin: 20rpx;
	padding: 30rpx;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
	transition: all 0.3s ease;
}

.card:active {
	transform: translateY(4rpx);
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-bottom: 20rpx;
	border-bottom: 2rpx solid #f5f5f5;
}
.header-left {
	display: flex;
	align-items: center;
}
.status-dot {
	width: 16rpx;
	height: 16rpx;
	border-radius: 50%;
	/* background-color: #4caf50; */
	margin-right: 16rpx;
}
.code {
	font-size: 28rpx;
	color: #333;
}
.status {
	font-size: 28rpx;
	font-weight: bold;
}

/* 状态颜色 */
.status-pending { color: #2979ff; } /* 待处理/待评估 */
.status-approving { color: #ff9800; } /* 审批中 */
.status-rejected { color: #f44336; } /* 已拒绝/已取消 */
.status-verified { color: #4caf50; } /* 已通过/已验证 */


.card-body {
	padding: 30rpx 0;
}
.body-title {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}
.title-text {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}
.content-item {
	display: flex;
	font-size: 28rpx;
	color: #666;
	line-height: 1.8;
}
.label {
	width: 160rpx;
	color: #999;
	flex-shrink: 0;
}
.value {
	flex: 1;
	word-break: break-all;
}

.card-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-top: 20rpx;
	border-top: 2rpx solid #f5f5f5;
}
.footer-left {
	display: flex;
	align-items: center;
}
.avatar {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	margin-right: 16rpx;
}
.username {
	font-size: 28rpx;
	color: #333;
}
.time {
	font-size: 26rpx;
	color: #999;
}
</style>