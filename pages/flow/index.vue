<template>
	<view class="container">
		<!-- 审批类型 -->
		<view class="card type-card">
			<view class="card-title">
				<text>审批类型</text>
			</view>
			<view class="nav-grid">
				<navigator url="/pages/flow/change-ledger" open-type="navigate" class="nav-item">
					<view class="icon-wrapper blue-bg">
						<!-- 在这里替换为您的图标 -->
						<image src="/static/img/home/<USER>" class="icon" mode="aspectFit"></image>
					</view>
					<text class="nav-text">变更审批</text>
				</navigator>
				<navigator url="/pages/navigate/index?type=flow" open-type="navigate" class="nav-item">
					<view class="icon-wrapper green-bg">
						<!-- 在这里替换为您的图标 -->
						<image src="/static/img/home/<USER>" class="icon" mode="aspectFit"></image>
					</view>
					<text class="nav-text">化学品审批</text>
				</navigator>
				<navigator url="/pages/flow/holiday-work-ledger" open-type="navigate" class="nav-item">
					<view class="icon-wrapper orange-bg">
						<!-- 在这里替换为您的图标 -->
						<image src="/static/img/home/<USER>" class="icon" mode="aspectFit"></image>
					</view>
					<text class="nav-text">双休日审批</text>
				</navigator>
			</view>
		</view>

		<!-- 审批统计 -->
		<view class="card stats-card">
			<view class="stats-header">
				<view class="card-title">审批统计</view>
				<view class="date-picker-group">
					<text class="month-label">月份</text>
					<picker class="month" mode="date" fields="month" @change="onBeginTimeChange" :value="filters.beginTime">
						<view class="uni-input">{{ filters.beginTime || '开始时间' }}</view>
					</picker>
					<picker class="month" mode="date" fields="month" @change="onEndTimeChange" :value="filters.endTime">
						<view class="uni-input">{{ filters.endTime || '结束时间' }}</view>
					</picker>
				</view>
			</view>

			<!-- Loading 状态 -->
			<!-- <view v-if="loading" class="loading-container">
				<view class="loading-content">
					<text class="loading-text">正在加载统计数据...</text>
				</view>
			</view> -->
			<view class="filter-buttons">
				<view class="filter-btn" :class="{active: filters.approvalType.includes('变更审批')}" @click="selectApprovalType('变更审批')">
					<text class="btn-text">变更审批</text>
					<view class="check-icon" v-if="filters.approvalType.includes('变更审批')">✓</view>
				</view>
				<view class="filter-btn" :class="{active: filters.approvalType.includes('化学品审批')}" @click="selectApprovalType('化学品审批')">
					<text class="btn-text">化学品审批</text>
					<view class="check-icon" v-if="filters.approvalType.includes('化学品审批')">✓</view>
				</view>
				<view class="filter-btn" :class="{active: filters.approvalType.includes('外部带入化学品')}" @click="selectApprovalType('外部带入化学品')">
					<text class="btn-text">外部带入化学品</text>
					<view class="check-icon" v-if="filters.approvalType.includes('外部带入化学品')">✓</view>
				</view>
				<view class="filter-btn" :class="{active: filters.approvalType.includes('双休日及节假日')}" @click="selectApprovalType('双休日及节假日')">
					<text class="btn-text">双休日及节假日</text>
					<view class="check-icon" v-if="filters.approvalType.includes('双休日及节假日')">✓</view>
				</view>
			</view>
		</view>

		<!-- 可视化图表 -->
		<view class="card chart-card">
			<view class="legend">
				<view class="legend-item">
					<view class="dot blue"></view>
					<text>流转中</text>
				</view>
				<view class="legend-item">
					<view class="dot green"></view>
					<text>完成</text>
				</view>
				<view class="legend-item">
					<view class="dot red"></view>
					<text>拒绝</text>
				</view>
				<view class="legend-item">
					<view class="dot orange"></view>
					<text>取消</text>
				</view>
			</view>
			<view class="pie-charts" ref="charts" v-show="showCharts">
				<canvas canvas-id="flowPieChart" id="flowPieChart" class="charts"/>
			</view>
		</view>


	</view>
</template>

<script>
	import { mapState, mapActions, mapGetters } from 'vuex';
	import globalConfig from '@/config'
	import uCharts from '@/common/lib/u-charts.min.js'

	var _self;
	var pieCharts = {};

	export default {
		components: {
		},
		data() {
			return {
				statistics: {
					inProgress: 0,
					completed: 0,
					rejected: 0,
					cancelled: 0
				},
				totalCount: 0,
				filters: {
					beginTime: this.getCurrentMonth(),
					endTime: this.getCurrentMonth(),
					approvalType: []
				},
				loading: false,
				// 图表相关
				cWidth: 750,
				cHeight: 500,
				pixelRatio: 1,
				showCharts: true
			}
		},
		onReady() {
			uni.setNavigationBarTitle({
			    title: this.$t('流程审批')
			})
			this.setNavBarColor()
		},
		mounted() {
			_self = this;
			//#ifdef MP-ALIPAY
			uni.getSystemInfo({
				success: function(res) {
					if (res.pixelRatio > 1) {
						//正常这里给2就行，如果pixelRatio=3性能会降低一点
						//_self.pixelRatio =res.pixelRatio;
						_self.pixelRatio = 2;
					}
				}
			});
			//#endif
			this.cWidth = uni.upx2px(750);
			this.cHeight = uni.upx2px(500);
		},
		onShow() {
			this.setNavBarColor()
			this.$minApi.checkLoginState().then(res=>{
				if(res.state == 'success'){
					// 登录成功后获取统计数据
					this.getFlowStatistics()
				} else{
					uni.reLaunch({
						url: '/pages/login/login'
					});
				}
			})
		},
		methods: {
			setNavBarColor() {
				// navBar-bg-color
				uni.setNavigationBarColor({
				    frontColor: '#000000',
				    // backgroundColor: '#00984a',
					backgroundColor: '#FFFFFF',
				    animation: {
				        duration: 400,
				        timingFunc: 'easeIn'
				    }
				})
			},
			// 获取当前月份
			getCurrentMonth() {
				const now = new Date()
				const year = now.getFullYear()
				const month = (now.getMonth() + 1).toString().padStart(2, '0')
				return `${year}-${month}`
			},
			// 获取审批统计数据
			getFlowStatistics() {
				this.loading = true
				uni.showLoading({
					title: '加载中...'
				})
				// 准备API调用参数，将approvalType数组转换为逗号分隔字符串
				const apiFilters = {
					...this.filters,
					approvalType: this.getApprovalTypeString()
				}
				this.$minApi.getFlowStatistics(apiFilters).then(res => {
					this.loading = false
					uni.hideLoading()
					if (res.state === 0) {
						this.statistics = res.data
						this.calculateTotal()
						// 绘制饼图
						this.drawPieChart()
					} else {
						uni.showToast({
							title: res.message || '获取统计数据失败',
							icon: 'none'
						})
					}
				}).catch(err => {
					this.loading = false
					uni.hideLoading()
					console.error('获取统计数据失败:', err)
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					})
				})
			},
			// 绘制饼图
			drawPieChart() {
				const pieData = this.convertStatisticsToPieData(this.statistics)
				if (pieData.length > 0) {
					const chartData = {
						series: [{
							data: pieData
						}]
					}
					setTimeout(() => {
						this.drawPieCharts('flowPieChart', chartData)
					}, 500)
				}
			},
			// 计算总数量
			calculateTotal() {
				this.totalCount = this.statistics.inProgress + this.statistics.completed + this.statistics.rejected + this.statistics.cancelled
			},
			// 转换统计数据为饼图格式
			convertStatisticsToPieData(statistics) {
				const pieData = [];
				const statusMap = {
					inProgress: { name: '流转中', color: '#1890FF' },
					completed: { name: '已完成', color: '#52C41A' },
					rejected: { name: '已拒绝', color: '#F5222D' },
					cancelled: { name: '已取消', color: '#FAAD14' }
				};

				Object.keys(statistics).forEach(key => {
					const value = statistics[key];
					if (value > 0 && statusMap[key]) {
						pieData.push({
							name: statusMap[key].name,
							value: value,
							color: statusMap[key].color
						});
					}
				});

				return pieData;
			},
			// 绘制饼图
			drawPieCharts(id, data) {
				// 处理数据，为每个数据项添加更友好的标签
				if (data && data.series && data.series[0] && data.series[0].data) {
					data.series[0].data = data.series[0].data.map(item => {
						// 确保每个项目都有一个标准的标签格式：名称+数量
						item.labelText = `${item.name}:${item.value}个`;
						return item;
					});
				}

				const ctx = uni.createCanvasContext(id, this);
				pieCharts[id] = new uCharts({
					$this: _self,
					type: "ring",
					context: ctx,
					width: _self.cWidth * _self.pixelRatio,
					height: _self.cHeight * _self.pixelRatio,
					series: data.series,
					animation: true,
					rotate: false,
					rotateLock: false,
					background: "#FFFFFF",
					color: ["#1890FF", "#52C41A", "#F5222D", "#FAAD14"],
					// 调整内边距，左右多留空间
					padding: [15, 60, 15, 60],
					dataLabel: true,
					enableScroll: false,
					legend: {
						show: false,
						position: "right",
						lineHeight: 25
					},
					extra: {
						ring: {
							// 减小圆环宽度
							customRadius: 50,
							ringWidth: 30,
							activeOpacity: 0.5,
							activeRadius: 5,
							offsetAngle: 0,
							// 增加标签宽度，防止文字挤压
							labelWidth: 5,
							border: false,
							borderWidth: 3,
							borderColor: "#FFFFFF"
						}
					}
				});
			},
			// 开始时间变更
			onBeginTimeChange(e) {
				this.filters.beginTime = e.detail.value
				this.getFlowStatistics()
			},
			// 结束时间变更
			onEndTimeChange(e) {
				this.filters.endTime = e.detail.value
				this.getFlowStatistics()
			},
			// 选择审批类型（支持多选）
			selectApprovalType(type) {
				const index = this.filters.approvalType.indexOf(type)
				if (index > -1) {
					// 如果已选中，则移除
					this.filters.approvalType.splice(index, 1)
				} else {
					// 如果未选中，则添加
					this.filters.approvalType.push(type)
				}
				this.getFlowStatistics()
			},
			// 将审批类型数组转换为逗号分隔的字符串
			getApprovalTypeString() {
				return this.filters.approvalType.length > 0 ? this.filters.approvalType.join(',') : ''
			}
		}
	}
</script>

<style lang="scss" scoped>
.container {
	// background-color: #f5f5f5;
	background-color: #FFFFFF;
	min-height: 100vh;
	padding: 30rpx;
	box-sizing: border-box;
}

.card {
	background-color: #ffffff;
	border-radius: 16rpx;
	// margin-bottom: 20rpx;
	padding: 30rpx 0rpx;
}

.type-card {
	background-image: linear-gradient(to right, #e8f5e9, #ffffff);
	position: relative;
	padding-top: 60rpx;
	overflow: hidden;
	
	&::before {
		content: "";
		position: absolute;
		top: 0;
		width: 200rpx;
		height: 80rpx;
		background-color: #4caf50;
		opacity: 0.8;
		border-radius: 0 0 80rpx 0;
		transform: skewX(-20deg);
		left: -20rpx;
	}
}

.card-title {
	font-size: 34rpx;
	// font-weight: bold;
	color: #23262A;
	margin-bottom: 20rpx;
	position: relative;
	z-index: 1;
}

.type-card .card-title {
	position: absolute;
	top: 10rpx;
	left: 30rpx;
	color: white;
}


.nav-grid {
	display: flex;
	justify-content: space-around;
	align-items: center;
	padding-top: 20rpx;
}

.nav-item {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.icon-wrapper {
	width: 120rpx;
	height: 120rpx;
	border-radius: 16rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	// margin-bottom: 16rpx;
	// border: 1rpx dashed #ccc;

	.icon {
		width: 100rpx;
		height: 100rpx;
	}
}

// .blue-bg {
// 	background-color: #e3f2fd;
// }
// .green-bg {
// 	background-color: #e8f5e9;
// }
// .orange-bg {
// 	background-color: #fbe9e7;
// }


.nav-text {
	font-size: 26rpx;
	color: #4C5156;
}

.stats-card .card-title {
	margin-bottom: 0;
}

.stats-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.date-picker-group {
	display: flex;
	align-items: center;
	font-size: 24rpx;
	color: #8D949C;
	justify-content: space-between;
	gap: 10rpx;
}

.month-label {
	
	color: #4C5156;
}
.month {
	width: 156rpx;
	line-height: 70px;
	height: 70rpx;
	display: flex;
	background: #F6F7FC;
	border-radius: 14rpx;
	.uni-input {
		background: #F6F7FC !important;
	}
}
.date-button {
    background-color: #F6F7FC;
    padding: 8rpx 20rpx;
    border-radius: 14rpx;
    margin-left: 10rpx;
}


.filter-buttons {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
	justify-content: space-between;
}

.filter-btn {
    background-color: #F6F7FC;
    color: #4C5156;
    padding: 22rpx 20rpx;
    border-radius: 14rpx;
    font-size: 28rpx;
    text-align: left;
    width: 42vw;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
    &.active {
        background-color: #00984a;
        color: #ffffff;
        transform: scale(1.02);
        box-shadow: 0 4rpx 12rpx rgba(0, 152, 74, 0.3);
    }
}

.btn-text {
    flex: 1;
}

.check-icon {
    font-size: 24rpx;
    font-weight: bold;
    margin-left: 10rpx;
    animation: checkIn 0.3s ease;
}

@keyframes checkIn {
    from {
        opacity: 0;
        transform: scale(0.5);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.legend {
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 40rpx;
	margin-bottom: 40rpx;
	font-size: 24rpx;
	color: #666;
}

.legend-item {
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.dot {
	width: 16rpx;
	height: 16rpx;
	border-radius: 50%;
	
	&.blue { background-color: #1890ff; }
	&.green { background-color: #52c41a; }
	&.red { background-color: #f5222d; }
	&.orange { background-color: #faad14; }
}

.pie-charts {
	width: 750rpx;
	height: 500rpx;
	margin: 0 auto;
	display: flex;
	justify-content: center;
	align-items: center;
}

.charts {
	width: 750rpx;
	height: 500rpx;
}

.total-number {
	font-size: 48rpx;
	font-weight: bold;
	color: #333;
}

.total-label {
	font-size: 24rpx;
	color: #999;
}

.stats-detail-card {
	margin-top: 30rpx;
}

.stats-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
	margin-top: 30rpx;
}

.stat-item {
	flex: 1;
	min-width: 150rpx;
	background-color: #f8f9fa;
	border-radius: 12rpx;
	padding: 30rpx 20rpx;
	text-align: center;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.stat-number {
	font-size: 48rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}

.stat-label {
  font-size: 26rpx;
  color: #666;
}

.loading-container {
  position: relative;
  min-height: 200rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
}

.loading-content {
  text-align: center;
}

.loading-text {
  display: block;
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666;
}

</style>