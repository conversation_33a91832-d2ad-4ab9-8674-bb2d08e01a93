<template>
	<view class="page-container">
		<view class="head-container">
			<uni-icons type="arrowleft" size="30" color="#23262A" class="back" @click="goBack()"></uni-icons>
			<image src="/static/img/mine/password.png" style="width:60vw;height: 345rpx;position:absolute;right: 0;top:0;" mode="aspectFill"></image>
			<view class="title">重置密码</view>
		</view>
		<view class="steps-container">
			<StepProgress :steps="stepList" :current-step="currentStepIndex" />
		</view>
		
		<view class="form-panel" v-if="!isSuccess">
			<view v-if="currentStepIndex === 0">
				<view class="form-item">
					<input type="password" v-model="oldPassword" placeholder="请输入原密码" class="form-input" />
				</view>
				<button class="submit-btn" @click="verifyPassword">下一步</button>
			</view>
			<view v-if="currentStepIndex === 1">
				<view class="form-item">
					<input type="password" v-model="newPassword" placeholder="请输入6-18位新密码" class="form-input" />
				</view>
				<view class="form-item">
					<input type="password" v-model="confirmPassword" placeholder="请再次输入新密码" class="form-input" />
				</view>
				<button class="submit-btn" @click="resetPassword">确认</button>
			</view>
		</view>

		<view class="success-panel" v-if="isSuccess">
			<image src="/static/img/mine/success.svg" class="success-icon"></image>
			<view class="success-text">密码重置成功</view>
			<button class="submit-btn" @click="goBackToLogin">返回</button>
		</view>
	</view>
</template>

<script>
	import { mapGetters } from 'vuex'
	import StepProgress from '@/components/step-progress/index.vue'; 
	export default {
		components: {
			StepProgress
		},
		data() {
			return {
				oldPassword: '',
				newPassword: '',
				confirmPassword: '',
				isSuccess: false,
				currentStepIndex: 0,
				stepList: [
					{ title: '验证密码' },
					{ title: '设置新密码' },
					{ title: '重置成功' }
				],
			}
		},
		computed: {
			...mapGetters({
				user: 'user'
			})
		},
		methods: {
			goBack() {
				uni.navigateBack({
					delta: 1
				});
			},
			verifyPassword() {
				if (!this.oldPassword) {
					return uni.showToast({ title: '请输入原密码', icon: 'none' });
				}
				uni.showLoading({ title: '验证中...' });
				this.$minApi.verifyOldPassword({ password: this.oldPassword }).then(() => {
					uni.hideLoading();
					this.currentStepIndex = 1;
				}).catch(err => {
					uni.hideLoading();
					uni.showToast({
						title: err.msg || '原密码错误',
						icon: 'none'
					});
				});
			},
			resetPassword() {
				if (!this.newPassword) {
					return uni.showToast({ title: '请输入新密码', icon: 'none' });
				}
				if (this.newPassword.length < 6 || this.newPassword.length > 18) {
					return uni.showToast({ title: '密码长度需为6-18位', icon: 'none' });
				}
				if (!this.confirmPassword) {
					return uni.showToast({ title: '请再次输入新密码', icon: 'none' });
				}
				if (this.newPassword !== this.confirmPassword) {
					return uni.showToast({ title: '两次输入的密码不一致', icon: 'none' });
				}
				
				uni.showLoading({ title: '正在重置...' });
				this.$minApi.resetPassword({
					oldPassword: this.oldPassword,
					newPassword: this.newPassword
				}).then(() => {
					uni.hideLoading();
					this.currentStepIndex = 2;
					this.isSuccess = true;
				}).catch(err => {
					uni.hideLoading();
					uni.showToast({
						title: err.msg || '重置失败',
						icon: 'none'
					});
				});
			},
			goBackToLogin() {
				uni.reLaunch({ url: '/pages/login/login' });
			}
		}
	}
</script>

<style lang="scss" scoped>
	.success-panel {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding-top: 100rpx;
		.success-icon {
			width: 120rpx;
			height: 120rpx;
		}
		.success-text {
			font-size: 36rpx;
			color: #333;
			margin-top: 40rpx;
			margin-bottom: 80rpx;
		}
	}
	.page-container {
		background-color: #FFFFFF;
		min-height: 100vh;
		
		.head-container{
			height: 25vh;
			position: relative;
			.back{
				position: absolute;
				margin-top: 100rpx;
				margin-left: 20rpx;
				z-index: 2;
			}
			.title{
				height: 46rpx;
				font-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30;
				font-weight: normal;
				font-size: 46rpx;
				color: #23262A;
				line-height: 46rpx;
				margin-top: 250rpx;
				margin-left: 32rpx;
				position: absolute;
			}
		}
		
		.steps-container {
			padding: 30rpx 60rpx;
			margin-bottom: 40rpx;
		}
	}
	
	

	.form-panel {
		background-color: #fff;
		border-radius: 16rpx;
		padding: 0 30rpx;
	}

	.form-item {
		padding: 20rpx 0;

		&:last-child {
			border-bottom: none;
		}
	}

	.form-input {
		font-size: 28rpx;
		color: #23262A;
		height: 100rpx;
		background: #F6F7FC;
		border-radius: 20rpx 20rpx 20rpx 20rpx;
		padding: 36rpx 20rpx;
	}

	.submit-btn {
		height: 88rpx;
		margin-top: 20rpx;
		width: 78vw;
		background-color: #37994C;
		color: #fff;
		border-radius: 50rpx;
		font-size: 32rpx;
	}
</style>