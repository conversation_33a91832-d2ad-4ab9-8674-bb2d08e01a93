<template>
	<view class="container">
		<view class="head-container">
			<image src="/static/img/mine/top.png" style="width:100vw;"></image>
			
			<view class="user-info-card" @click="navigateTo('/pages/my/info')">
				<view class="title">我的</view>
				<view class="userinfo">
					<image class="avatar" :src="user.F_HeadIcon? baseUrl+user.F_HeadIcon : '/static/img/profile.svg'" mode="aspectFit"></image>
					<view class="labels">
						<view class="name">{{ user.F_RealName }}</view>
						<view class="org">{{ user.F_OrganizeName }}</view>
					</view>
				</view>
			</view>
			
			<view class="quick-nabar">
				<view class="nabar" v-if="$checkMenuAuth('cbsgl')">
					<navigator url="/pages/flow/task-list?title=待处理&status=0" open-type="navigate">
						<image src="/static/img/mine/daichuli.svg" mode="aspectFit"></image>
						<view class="icon_name">待处理</view>
					</navigator>
				</view>
				<view class="nabar" v-if="$checkMenuAuth('cbsgl')">
					<navigator url="/pages/flow/task-list?title=我已处理&status=1" open-type="navigate">
						<image src="/static/img/mine/yichuli.svg" mode="aspectFit"></image>
						<view class="icon_name">我已处理</view>
					</navigator>
				</view>
				<view class="nabar" v-if="$checkMenuAuth('cbsgl')">
					<navigator url="/pages/flow/task-list?title=我发起的&status=2" open-type="navigate">
						<image src="/static/img/mine/wofaqi.svg" mode="aspectFit"></image>
						<view class="icon_name">我发起的</view>
					</navigator>
				</view>
				<view class="nabar" v-if="$checkMenuAuth('cbsgl')">
					<navigator url="/pages/flow/task-list?title=被拒回&status=3" open-type="navigate">
						<image src="/static/img/mine/beijuhui.svg" mode="aspectFit"></image>
						<view class="icon_name">被拒回</view>
					</navigator>
				</view>
			</view>
		</view>
		

		<view class="menu-list">
			<uni-list :border="false">
				<uni-list-item thumb="/static/img/mine/gerenxinxi.svg" title="个人信息" showArrow clickable @click="navigateTo('/pages/my/info')"></uni-list-item>
				<uni-list-item thumb="/static/img/mine/safe.svg" title="账号与安全" showArrow clickable @click="navigateTo('/pages/my/security')"></uni-list-item>
				<uni-list-item thumb="/static/img/mine/logout.svg" title="退出登录" showArrow clickable @click="handleLogout()"></uni-list-item>
			</uni-list>
		</view>

		<view class="logout-btn-container">
			<!-- <button class="logout-btn" @click="handleLogout">退出登录</button> -->
			<image src="/static/img/mine/nideke.svg" style="width: 272rpx;height: 48rpx;"></image>
		</view>
	</view>
</template>

<script>
	import api from '@/api/api.js';
	import globalConfig from '@/config'
	import { mapState, mapActions, mapGetters } from 'vuex';

	export default {
		data() {
			return {
				// userInfo: {} // 使用Vuex中的userInfo
				baseUrl: "",
			}
		},
		computed: {
			...mapGetters(['themeBgColor', 'darkMode', 'user']),
		},
		onShow() {
			this.checkLogin();
		},
		onLoad(){
			this.baseUrl = globalConfig.baseUrl.replace("/api","");
		},
		methods: {
			...mapActions('user', ['logout', 'checkLoginState']),
			checkLogin() {
				this.$minApi.checkLoginState().then(res => {
					// 假设checkLoginState成功后会将用户信息更新到Vuex
				}).catch(err => {
					// 未登录或登录失效，可以跳转到登录页
					uni.showToast({
						title: '请先登录',
						icon: 'none',
						complete: () => {
							uni.reLaunch({
								url: '/pages/login/login'
							})
						}
					})
				})
			},
			navigateTo(url) {
				uni.navigateTo({
					url: url
				});
			},
			handleLogout() {
				uni.showModal({
					title: '提示',
					content: '确定要退出登录吗？',
					success: (res) => {
						if (res.confirm) {
							uni.showLoading({
								title: '退出中...'
							})
							this.$minApi.logout().then(res => {
								if (res.state === 'success') {
									// 调用 vuex 的登出方法清除用户状态
									this.$store.dispatch('logout')
									uni.showToast({
										title: '已退出登录',
										icon: 'success'
									})
								} else {
									uni.showToast({
										title: res.message || '退出失败',
										icon: 'none'
									})
								}
							}).catch(err => {
								console.error('退出登录失败:', err)
								uni.showToast({
									title: '退出失败，请重试',
									icon: 'none'
								})
							}).finally(() => {
								uni.hideLoading()
							})
						}
					}
				})
			}
		}
	}
</script>

<style lang="scss">
	.container {
		background-color: #F2F6FC;
		min-height: 100vh;
	}
	.head-container{
		position: relative;
		display: flex;
		flex-direction: column;
		image{
			width: 100vw;
			height: 40vh;
		}
		.user-info-card {
			width: 100vw;
			margin-top: 86rpx;
			position: absolute;
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: 20rpx 20rpx 1rpx 20rpx;
			gap: 20rpx;
			justify-content: start;
			.title{
				display: flex;
				align-items: center;
				text-align: center;
				font-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30;
				font-weight: normal;
				font-size: 34rpx;
				color: #FFFFFF;
				line-height: 34rpx;
			}
			.userinfo{
				display: flex;
				gap: 20rpx;
				width: 90vw;
				margin-top: 40rpx;
				.avatar{
					width: 140rpx;
					height: 140rpx;
					background: #22C992;
					border: 1rpx solid #FFFFFF;
					border-radius: 50%;
					object-fit: cover;
				}
				.labels{
					display: flex;
					flex-direction: column;
					align-items: left;
					
					.name{
						color: white;
						font-size: 40rpx;
						font-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30;
						font-weight: normal;
					}
					.org{
						// width: 192rpx;
						height: 50rpx;
						background: #FFFFFF;
						border-radius: 100rpx 100rpx 100rpx 100rpx;
						font-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30;
						font-weight: normal;
						font-size: 20rpx;
						color: #37994C;
						text-align: center;
						align-items: center;
						line-height: 50rpx;
						padding: 2rpx 40rpx;
					}
				}
			}
		}
		.quick-nabar{
			position: absolute;
			bottom: 0rpx;
			display: flex;
			flex-direction: row;
			gap: 10rpx;
			background-color: white;
			width: 90vw;
			z-index: 2;
			margin: 0 auto;
			border-radius: 20rpx;
			padding: 20rpx;
			left: 0;
			right: 0;
			margin: auto;
			.nabar{
				display: flex;
				flex-direction: column;
				align-items: center;
				width: 22vw;
				text-align: center;
				image{
					width: 100rpx;
					height: 100rpx;
				}
				.icon_name{
					text-align: center;
					font-size: 26rpx;
				}
			}
		}
		
	}
	
	
	.menu-list {
		margin-top: 10px;
		border-radius: 20rpx;
		padding: 50rpx 20rpx;
		background-color: #ffffff;
		margin: 34rpx;
	}
	::v-deep .uni-list:before {
	    background-color: #FFFFFF !important;
	}
	
	/* 针对 uni-list-item 组件的顶部分割线（如果存在） */
	::v-deep .uni-list-item__container:after  {
	    background-color: #FFFFFF !important;
	}
	::v-deep .uni-list:after {
	    background-color: #FFFFFF !important;
	}
	.uni-list:after {
	    background-color: #FFFFFF !important;
	}
	uni-list {
	  border-top: none;
	  border-bottom: none;
	}
	
	/* Remove the borders of each list item */
	uni-list-item {
	  border: none;
	}

	.logout-btn-container {
	    position: absolute; /* 相对于父容器定位 */
	    bottom: 30rpx; /* 距离父容器底部 30rpx */
	    left: 50%; /* 水平居中 */
	    transform: translateX(-50%); /* 微调以实现完美居中 */
	}
</style>