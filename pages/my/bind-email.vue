<template>
	<view class="page-container">
		<view class="head-container">
			<uni-icons type="arrowleft" size="30" color="#23262A" class="back" @click="goBack()"></uni-icons>
			<image src="/static/img/mine/email.png" style="width:60vw;height: 345rpx;position:absolute;right: 0;top:0;" mode="aspectFill"></image>
			<view class="title">绑定邮箱</view>
		</view>
		<view class="steps-container">
			<StepProgress :steps="stepList" :current-step="currentStepIndex" />
		</view>
		
		<view class="form-panel" v-if="!isSuccess">
			<view class="form-item" v-if="currentStepIndex === 0">
				<input type="password" v-model="password" placeholder="请输入当前登录密码" class="form-input" />
			</view>
			<view class="form-item" v-if="currentStepIndex === 1">
				<input type="text" v-model="email" placeholder="请输入邮箱地址" class="form-input" />
			</view>
			<button class="submit-btn" @click="handleNext" v-if="currentStepIndex === 0">下一步</button>
			<button class="submit-btn" @click="submit" v-if="currentStepIndex === 1">确认绑定</button>
		</view>

		<view class="success-panel" v-if="isSuccess">
			<image src="/static/img/mine/success.svg" class="success-icon"></image>
			<view class="success-text">设置邮箱成功</view>
			<button class="submit-btn" @click="goBack">返回</button>
		</view>
	</view>
</template>

<script>
	import { mapGetters } from 'vuex'
	import StepProgress from '@/components/step-progress/index.vue'; 
	export default {
		components: {
			StepProgress
		},
		data() {
			return {
				email: '',
				password: '',
				currentStepIndex: 0,
				isSuccess: false,
				stepList: [
					{ title: '验证密码' },
					{ title: '设置邮箱' },
					{ title: '设置成功' }
				],
			}
		},
		computed: {
			...mapGetters({
				user: 'user'
			})
		},
		methods: {
			goBack() {
			    uni.navigateBack({
			        delta: 1
			    });
			},
			handleNext() {
				if (!this.password) {
					return uni.showToast({ title: '请输入密码', icon: 'none' });
				}
				uni.showLoading({ title: '验证中...' });
				this.$minApi.verifyOldPassword({ password: this.password }).then(() => {
					uni.hideLoading();
					this.currentStepIndex = 1;
				}).catch(err => {
					uni.hideLoading();
					uni.showToast({
						title: err.msg || '密码验证失败',
						icon: 'none'
					});
				});
			},
			submit() {
				if (!this.email) {
					return uni.showToast({ title: '请输入邮箱地址', icon: 'none' });
				}
				const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
				if (!emailPattern.test(this.email)) {
					return uni.showToast({ title: '邮箱格式不正确', icon: 'none' });
				}
				this.bindEmail();
			},
			bindEmail() {
				uni.showLoading({ title: '正在绑定...' });
				this.$minApi.bindEmail({ email: this.email }).then(() => {
					uni.hideLoading();
					this.updateUserEmail();
					uni.showToast({
						title: '邮箱绑定成功',
						icon: 'success'
					});
					this.currentStepIndex = 2;
					this.isSuccess = true;
				}).catch(err => {
					uni.hideLoading();
					uni.showToast({
						title: err.msg || '操作失败',
						icon: 'none'
					});
				});
			},
			updateUserEmail() {
				let newUserInfo = { ...this.user, F_Email: this.email };
				this.$store.commit('login', newUserInfo);
			}
		}
	}
</script>

<style lang="scss" scoped>
	.success-panel {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding-top: 100rpx;
		.success-icon {
			width: 120rpx;
			height: 120rpx;
		}
		.success-text {
			font-size: 36rpx;
			color: #333;
			margin-top: 40rpx;
			margin-bottom: 80rpx;
		}
	}
	.page-container {
		background-color: #FFFFFF;
		min-height: 100vh;
		
		.head-container{
			height: 25vh;
			position: relative;
			.back{
				position: absolute;
				margin-top: 100rpx;
				margin-left: 20rpx;
				z-index: 2;
			}
			.title{
				height: 46rpx;
				font-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30;
				font-weight: normal;
				font-size: 46rpx;
				color: #23262A;
				line-height: 46rpx;
				margin-top: 250rpx;
				margin-left: 32rpx;
				position: absolute;
			}
		}
		
		.steps-container {
			padding: 30rpx 60rpx;
			margin-bottom: 40rpx; // 与表单拉开距离
		}
	}
	
	

	.form-panel {
		background-color: #fff;
		border-radius: 16rpx;
		padding: 0 30rpx;
	}

	.form-item {
		padding: 20rpx 0;

		&:last-child {
			border-bottom: none;
		}
	}

	.form-input {
		font-size: 28rpx;
		color: #23262A;
		height: 100rpx;
		background: #F6F7FC;
		border-radius: 20rpx 20rpx 20rpx 20rpx;
		padding: 36rpx 20rpx;
	}

	.submit-btn {
		height: 88rpx;
		margin-top: 20rpx;
		width: 78vw;
		background-color: #37994C;
		color: #fff;
		border-radius: 50rpx;
		font-size: 32rpx;
	}
</style>