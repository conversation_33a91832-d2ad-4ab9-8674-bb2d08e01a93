<template>
	<view class="page-container">
		<uni-icons type="arrowleft" size="30" color="#FFF" class="back" @click="goBack()"></uni-icons>
		<image src="/static/img/mine/security.png" style="width:100vw;height: 377.95rpx;" mode="aspectFill"></image>
		<view class="title">账号与安全</view>
		<view class="setting-list">
			<view class="setting-item" @click="navigateTo('bind-email')">
				<text class="item-label">绑定邮箱</text>
				<view class="item-content">
					<text class="email-text">{{ email || '未绑定' }}</text>
					<uni-icons type="arrowright" size="16" color="#999"></uni-icons>
				</view>
			</view>
			<view class="setting-item" @click="navigateTo('reset-password')">
				<text class="item-label">重置密码</text>
				<uni-icons type="arrowright" size="16" color="#999"></uni-icons>
			</view>
		</view>
	</view>
</template>

<script>
	import { mapGetters } from 'vuex'

	export default {
		computed: {
			...mapGetters(['email'])
		},
		methods: {
			goBack() {
			    uni.navigateBack({
			        delta: 1 // 返回上一页
			    });
			},
			navigateTo(page) {
				uni.navigateTo({
					url: `/pages/my/${page}`
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	.page-container {
		background-color: #F2F6FC;
		min-height: 100vh;
		position: relative;
	}
	.back{
		position: absolute;
		margin-top: 100rpx;
		margin-left: 32rpx;
		z-index: 2;
	}
	.title{
		margin-top: -100rpx;
		height: 46rpx;
		font-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30;
		font-weight: normal;
		font-size: 46rpx;
		color: #FFFFFF;
		line-height: 46rpx;
		margin-top: -150rpx;
		margin-left: 32rpx;
		position: absolute;
	}

	.setting-list {
		// background-color: #fff;
		margin-top: -100rpx;
		display: flex;
		flex-direction: column;
		gap: 30rpx;
		padding: 30rpx;
	}

	.setting-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx 20rpx;
		// border-bottom: 1rpx solid #f0f0f0;
		border-radius: 20rpx;
		background-color: #fff;
		// &:last-child {
		// 	border-bottom: none;
		// }
		z-index: 2;
	}

	.item-label {
		font-size: 30rpx;
		color: #23262A;
	}
	.label-demo{
		font-size: 30rpx;
		color: #8D949C;
	}

	.item-content {
		display: flex;
		align-items: center;
	}

	.email-text {
		font-size: 30rpx;
		color: #8D949C;
		margin-right: 10rpx;
	}
</style>