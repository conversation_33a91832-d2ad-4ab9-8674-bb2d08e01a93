<template>
	<view class="page-container">
		<view class="user-info-panel">
			<view class="info-item avatar-item" style="justify-content: center;flex-direction: column;background:#FFFFFF">
				
				<view class="item-content" style="height: 200rpx;position: relative;">
					<image class="avatar" :src="user.F_HeadIcon? baseUrl+user.F_HeadIcon : '/static/img/profile.svg'" @click="changeAvatar"></image>
					<!-- <image class="avatar" src="/static/img/profile.svg" @click="changeAvatar"></image> -->
					<image src="/static/img/mine/photo.svg" @click="changeAvatar" style="position: absolute;left: 50%;width: 95.45rpx;height: 85.34rpx;transform: translateX(-50%);"></image>
				</view>
				<text class="item-label" style="font-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30;font-weight: normal;font-size: 30rpx;color: #23262A;line-height: 30rpx;">头像</text>
			</view>
			<view class="info-item">
				<text class="item-label">姓名</text>
				<view class="item-content">
					<text>{{ userInfo.F_RealName }}</text>
				</view>
			</view>
			<view class="info-item">
				<text class="item-label">部门</text>
				<view class="item-content">
					<text>{{ userInfo.F_OrganizeName }}</text>
					<!-- <uni-icons type="arrowright" size="16" color="#999"></uni-icons> -->
				</view>
			</view>
			<view class="info-item">
				<text class="item-label">手机号码</text>
				<view class="item-content">
					<text>{{ userInfo.F_MobilePhone }}</text>
				</view>
			</view>
			<view class="info-item">
				<text class="item-label">邮箱</text>
				<view class="item-content">
					<text>{{ userInfo.F_Email }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { mapState, mapActions, mapGetters } from 'vuex';
	import globalConfig from '@/config'
	export default {
		data() {
			return {
				indicatorStyle: `height: 50px;`,
				baseUrl:''
			};
		},
		computed: {
			...mapGetters(['themeBgColor', 'darkMode', 'user']),
			// 处理用户头像，返回默认头像或用户自定义头像
			userAvatar() {
				return this.user?.F_HeadIcon || '/static/img/profile.svg'
			},
			userInfo() {
				return { ...this.user } || {}
			},
		},
		onReady() {
			uni.setNavigationBarTitle({
			    title: this.$t('个人信息')
			})
			this.setNavBarColor()
		},
		onShow() {
			this.setNavBarColor()
			this.$minApi.checkLoginState().then(res=>{
				if(res.state == 'success'){
					
				} else{
					uni.reLaunch({
						url: '/pages/login/login'
					});
				}
			})
		},
		onLoad(){
			this.baseUrl = globalConfig.baseUrl.replace("/api","");
		},
		methods: {
			setNavBarColor() {
				// navBar-bg-color
				uni.setNavigationBarColor({
				    frontColor: '#000000',
				    // backgroundColor: '#00984a',
					backgroundColor: '#FFFFFF',
				    animation: {
				        duration: 400,
				        timingFunc: 'easeIn'
				    }
				})
			},
			changeAvatar() {
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					success: (res) => {
						const tempFilePath = res.tempFilePaths[0];
						uni.showLoading({ title: '上传中...' });
						this.$minApi.uploadFile({
							filePath: tempFilePath,
							name: 'file',
							fileby: 'avatar',
							filetype: 1
						}).then(uploadRes => {
							const avatarUrl = uploadRes.data;
							return this.$minApi.saveUserAvatar({ avatar: avatarUrl });
						}).then(res => {
							uni.hideLoading();
							if (res.state === 'success') {
								uni.showToast({
									title: '头像更换成功',
									icon: 'success'
								});
								
								// 修复：解析接口返回的data字符串，获取最准确的用户信息
								let finalUserInfo;
								if (typeof res.data === 'string') {
									try {
										finalUserInfo = JSON.parse(res.data);
									} catch (e) {
										throw new Error('服务器响应数据格式错误');
									}
								} else {
									finalUserInfo = res.data;
								}

								// 使用最终确认的头像URL更新状态
								if (finalUserInfo && finalUserInfo.F_HeadIcon) {
									this.$store.commit('updateUserProfile', { F_HeadIcon: finalUserInfo.F_HeadIcon });
								}
								
							} else {
								throw new Error(res.message || '更新失败');
							}
						}).catch(err => {
							uni.hideLoading();
							uni.showToast({
								// 修复：使用 err.message 获取更详细的错误信息
								title: err.message || '操作失败',
								icon: 'none'
							});
						});
					}
				});
			},
			saveUserInfo() {
				// 调用保存用户信息的API
				// Vue.prototype.$minApi.apis.saveUserInfo(this.userInfo).then(...)
				uni.showToast({
					title: '用户信息保存接口待实现',
					icon: 'none'
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	.page-container {
		padding: 20rpx;
		background-color: #FFFFFF;
		min-height: 100vh;
	}

	.user-info-panel {
		background-color: #fff;
		border-radius: 16rpx;
		padding: 30rpx;
		display: flex;
		flex-direction: column;
		gap: 20rpx;
	}

	.info-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		// padding: 30rpx 0;
		// border-bottom: 1rpx solid #f0f0f0;
		background: #F6F7FC;
		padding: 40rpx 20rpx;
		border-radius: 20rpx;

		&:last-child {
			border-bottom: none;
		}
	}

	.item-label {
		font-size: 30rpx;
		color: #23262A;
	}

	.item-content {
		display: flex;
		align-items: center;
		color: #23262A;
		font-size: 30rpx;
	}

	.avatar-item .item-content {
		height: 80rpx;
	}

	.avatar {
		width: 150rpx;
		height: 150rpx;
		border-radius: 50%;
	}

	.item-input {
		text-align: right;
		font-size: 28rpx;
		color: #333;
	}

	.save-btn {
		margin-top: 40rpx;
		background-color: #007BFF;
		color: #fff;
		border-radius: 50rpx;
		font-size: 32rpx;
	}
	
	.gender-picker {
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 300rpx;
		background-color: white;
		z-index: 999;
	}
	
	.picker-item {
		line-height: 50px;
		text-align: center;
	}
</style>