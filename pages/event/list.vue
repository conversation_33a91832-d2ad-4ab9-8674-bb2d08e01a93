<template>
	<view class="container">
		<view class="filters">
			<!-- <view class="item">
				<image src="/static/img/filter.svg"></image>筛选
			</view> -->
			<view class="item">
				<label>发生时间</label>
				<picker mode="date" @change="onStartDateChange" :value="queryParams.beginDate" :end="queryParams.endDate">
					<view class="uni-input">{{ queryParams.beginDate }}</view>
				</picker>
				<view style="margin-left: 10rpx;margin-right: 10rpx;"> - </view>
				<picker mode="date" @change="onEndDateChange" :start="queryParams.beginDate" :value="queryParams.endDate">
					<view class="uni-input">{{ queryParams.endDate }}</view>
				</picker>
			</view>
			<view class="item">
				<label>事故等级</label>
				<picker mode="selector" @change="onStatusChange" :value="StatusIndex" range-key="text" :range="StatusList">
					<view class="uni-input" style="width: 28vw;">{{StatusList[StatusIndex].text}}</view>
				</picker>
			</view>
			<view class="item">
				<label>事故车间</label>
				<!-- <input type="text" confirm-type="search" v-model="queryParams.bmu" placeholder="请输入车间" class="uni-input" @confirm="onBmuChange" /> -->
				<picker mode="selector" @change="onBmuChange" :value="BmuIndex" range-key="label" :range="BmuList">
					<view class="uni-input" style="width: 28vw;">{{BmuList[BmuIndex].label}}</view>
				</picker>
			</view>
		</view>
		<view class="card-list">
			<view class="title">事故台账（{{total}}）</view>
			<view class="card" v-for="item in EventList" :key="item.F_Id">
				<navigator :url="`/pages/event/info?id=${item.F_Id}`" open-type="navigate" class="navbar" v-if="$checkBtnAuth('sgtzck')">
					<view class="header">
						<view class="title">事故编号：{{ item.F_Num }}</view>
					</view>
					<view class="content">
						<view class="title">{{ item.F_Description }}</view>
						<view>车间：{{ item.F_BMUName}}</view>
						<view>受伤人姓名：{{ item.F_Surname }}</view>
						<view>事故等级：{{ item.F_Level }}</view>
						<view class="bottom">
							<view class="user"><image src="/static/img/profile.svg"></image>报告人{{ item.F_ReportUser }}</view>
							<view class="clock"><image src="/static/img/clock.svg"></image>{{ item.F_Date }}</view>
						</view>
					</view>
				</navigator>
				<view class="navbar" v-else>
					<view class="header">
						<view class="title">事故编号：{{ item.F_Num }}</view>
					</view>
					<view class="content">
						<view class="title">{{ item.F_Description }}</view>
						<view>车间：{{ item.F_BMUName}}</view>
						<view>受伤人姓名：{{ item.F_Surname }}</view>
						<view>事故等级：{{ item.F_Level }}</view>
						<view class="bottom">
							<view class="user"><image src="/static/img/profile.svg"></image>报告人{{ item.F_ReportUser }}</view>
							<view class="clock"><image src="/static/img/clock.svg"></image>{{ item.F_Date }}</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="botomm-btn" v-if="$checkBtnAuth('sgtzxz')">
			<navigator url="/pages/event/edit" open-type="navigate">
				<view class="btn">新增事故</view>
			</navigator>
		</view>
	</view>
</template>

<script>
	import { mapGetters } from 'vuex'
	import uniIcons from '@/components/uni-icons/uni-icons.vue'
	import uniNavBar from '@/components/uni-nav-bar/uni-nav-bar.vue'
	import globalConfig from '@/config'
	export default {
		components: {
			uniIcons,
			uniNavBar
		},
		computed: {
			...mapGetters(['themeBgColor', 'darkMode','user']),
		},
		data() {
			return {
				baseUrl:'',
				searchVal: '',
				winHeight: "",
				value: 1,
				total: 0,
				// 查询参数
				queryParams: {
					page: 1,
					rows: 10,
					beginDate: '',
					endDate: '',
					level: '',
					bmu: ''
				},
				StatusList: [
					{ value: '', text: "全部" },
					{ value: '死亡', text: "死亡" },
					{ value: '致残', text: "致残" },
					{ value: '损工', text: "损工" },
					{ value: '工作受限', text: "工作受限" },
					{ value: '医疗处理', text: "医疗处理" },
					{ value: '急救', text: "急救" },
					{ value: '虚惊', text: "虚惊" }
				],
				BmuList: [
				    { value: "", label: "==请选择==" },
				    { value: "1", label: "生产设备部" },
				    { value: "1-1", label: "机加工业务单元" },
				    { value: "1-2", label: "冲压电机业务单元" },
				    { value: "1-3", label: "组装业务单元" },
				    { value: "1-4", label: "绿色发展科" },
				    { value: "1-5", label: "生产工程科" },
				    { value: "1-6", label: "生产技术创新科" },
				    { value: "2", label: "物流部" },
				    { value: "3", label: "家用产品研发部" },
				    { value: "4", label: "商用产品研发部" },
				    { value: "5", label: "质量和智能项目部" },
				    { value: "6", label: "人力资源部" },
				    { value: "7", label: "财务部" },
				    { value: "8", label: "法务部" },
				    { value: "9", label: "市场销售部" },
				    { value: "10", label: "总经理办公室" },
				    { value: "11", label: "NE NT项目组" }
				],
				StatusIndex: 0,
				BmuIndex: 0,
				EventList: [],
				loading: false,
				isEnd: false // 是否已加载全部数据
			}
		},
		onReady() {
			uni.setNavigationBarTitle({
			    title: this.$t('事故台账')
			})
			this.setNavBarColor()
		},
		onShow() {
			this.setNavBarColor()
		},
		onLoad() {
			this.baseUrl = globalConfig.baseUrl.replace('/api', '');
			uni.getSystemInfo({
				success: res => {
					this.winHeight = res.windowHeight
				}
			})
			this.loadData()
			// 监听刷新事件
			uni.$on('eventListRefresh', this.loadEventList)
		},
		onUnload() {
			// 移除事件监听
			uni.$off('eventListRefresh', this.loadEventList)
		},
		// 监听页面滚动到底部
		onReachBottom() {
			if (!this.loading && !this.isEnd) {
				this.queryParams.page++
				this.loadData()
			}
		},
		onShareAppMessage() {
		    return {
		      title: 'EHS-掌上观纹', // 自定义分享标题
		      path: '/pages/start/index', // 分享页面路径（默认当前页）
		      imageUrl: this.baseUrl+'/images/nideke.jpg', // 分享图片（可选）
		      success(res) {
		        console.log('分享成功', res);
		      },
		      fail(err) {
		        console.error('分享失败', err);
		      }
		    };
		},
		methods: {
			checkPermission(){
				if(this.user.F_RoleId === "08dc7a3d-e5a8-44cf-87ea-15347b9fa336"){
					return false;
				}else{
					return true;
				}
			},
			loadData() {
				if (this.loading || this.isEnd) return
				this.loading = true
				
				// 更新查询参数
				this.queryParams.level = this.StatusList[this.StatusIndex].value
				
				uni.showLoading({
					title: '加载中...'
				})
				
				this.$minApi.getEventList(this.queryParams).then(res => {
					if (res.state === 0) {
						if (this.queryParams.page === 1) {
							this.EventList = res.data
						} else {
							this.EventList = [...this.EventList, ...res.data]
						}
						this.total = res.count || this.EventList.length
						// 判断是否加载完全部数据
						if (res.data.length < this.queryParams.rows) {
							this.isEnd = true
						}
					} else {
						uni.showToast({
							title: res.message || '加载失败',
							icon: 'none'
						})
					}
				}).catch(() => {
					uni.showToast({
						title: '加载失败',
						icon: 'none'
					})
				}).finally(() => {
					uni.hideLoading()
					this.loading = false
				})
			},
			onEndDateChange(e) {
				this.queryParams.endDate = e.detail.value
				this.search()
			},
			onStartDateChange(e) {
				this.queryParams.beginDate = e.detail.value
				this.search()
			},
			onStatusChange(e) {
				this.StatusIndex = e.detail.value
				this.search()
			},
			onBmuChange(e) {
				this.queryParams.bmu = e.detail.value
				this.search()
			},
			setNavBarColor() {
				uni.setNavigationBarColor({
				    frontColor: '#000000',
				    backgroundColor: this.themeBgColor,
				    animation: {
				        duration: 400,
				        timingFunc: 'easeIn'
				    }
				})
			},
			search() {
				this.queryParams.page = 1
				this.isEnd = false
				this.EventList = []
				this.loadData()
			},
			clear() {
				this.searchVal = ''
				this.search()
			},
			loadEventList() {
				this.loadData()
			}
		}
	}
</script>

<style lang="less">
	.container{
		min-height: 100vh;
		background-color: white;
		padding-bottom: 320rpx;
		.filters{
			display: flex;
			flex-direction: row;
			align-items: center;
			background-color: white;
			padding: 20rpx;
			width: 100%;
			flex-wrap: wrap;
			gap: 10rpx;
			.item{
				display: flex;
				flex-direction: row;
				align-items: center;
				flex: 1;
				label{
					color: #4c4c4c;
					// width: 20%;
					padding: 10rpx;
					align-items: center;
					text-align: center;
					line-height: 20rpx;
				}
				.uni-input{
					color: #7c7c7c;
					border: 1px solid #00984a;
					align-items: center;
					text-align: left;
					width: 36vw;
					border-radius: 10rpx !important;
					line-height: 20rpx;
				}
			}
		}
		.card-list{
			display: flex;
			flex-direction: column;
			align-items: left;
			justify-content: start;
			width: 100vw;
			padding: 30rpx;
			gap: 20rpx;
			.title{
				font-size: 32rpx;
				font-weight: 400;
				color: #333333;
			}
			.card{
				color: #999999;
				border: 1rpx solid rgba(242, 242, 242, 1);
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				width: 100%;
				background-color: white;
				border-radius: 10rpx;
				.navbar{
					width: 100%;
				}
				.header{
					width: 100%;
					border-bottom: 1rpx solid rgba(242, 242, 242, 1);
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: space-between;
					padding: 20rpx;
					box-sizing: border-box;
					.title{
						color: #333333;
						font-size: 28rpx;
						font-weight: 300;
					}
					.status{
						
					}
					.processing{
						color: #F77214;
					}
					.complate{
						color: #999999;
					}
				}
				.content{
					padding: 20rpx;
					width: 100%;
					display: flex;
					flex-direction: column;
					align-items: left;
					// font-weight: 300;
					.title{
						color: #333333;
						font-size: 32rpx;
						font-weight: 400;
						margin-bottom: 20rpx;
					}
					.bottom{
						display: flex;
						align-items: center;
						justify-content: space-between;
						gap:10rpx;
						margin-top: 30rpx;
						line-height: 40rpx;
						.user{
							display: flex;
							align-items: center;
							gap: 10rpx;
							line-height: 40rpx;
							image{
								width: 50rpx;
								height: 50rpx;
							}
						}
						.clock{
							display: flex;
							align-items: center;
							line-height: 40rpx;
							gap: 5rpx;
							image{
								width: 30rpx;
								height: 30rpx;
							}
						}
					}
				}
			}
		}
		.botomm-btn {
			padding-bottom: 100rpx;
			height: 230rpx;
			position: fixed; /* 固定在屏幕底部 */
			bottom: 0;
			left: 0;
			width: 100%; /* 占满整个屏幕宽度 */
			background-color: #fff; /* 背景颜色，防止透明 */
			padding: 20rpx 0; /* 上下留白 */
			text-align: center; /* 让按钮居中 */
			align-items: center;
			display: flex;
			justify-content: center;
			box-shadow: 0 -10rpx 20rpx rgba(0, 0, 0, 0.1); /* 添加阴影，提升层次感 */
			z-index: 999; /* 确保层级较高 */
		}
		.btn {
			width: 686rpx;
			height: 80rpx;
			background: linear-gradient(90deg, #00984a 0%, #00984a 100%);
			border-radius: 40rpx;
			line-height: 80rpx;
			text-align: center;
			color: #FFFFFF;
			font-size: 32rpx;
		}
	}
</style>