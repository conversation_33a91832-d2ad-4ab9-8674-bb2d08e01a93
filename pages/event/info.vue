<template>
	<view class="container">
		<view class="header">
			<view class="title">{{ Event.F_Name }}</view>
			<view class="detail">
				<view class="num">事故编号：{{ Event.F_Num }}</view>
			</view>
		</view>
		<view class="info">
			<view class="title">事故信息</view>
			<view class="content">
				<view class="item">
					<label class="label">事故时间</label>
					<view class="text">{{ Event.F_Date }}</view>
				</view>
				<view class="item">
					<label class="label">车间/科室</label>
					<view class="text">{{ Event.F_BMUName }}</view>
				</view>
				<view class="item">
					<label class="label">设施/位置</label>
					<view class="text">{{ Event.F_Name }}</view>
				</view>
				<view class="item">
					<label class="label">事故描述</label>
					<view class="text">{{ Event.F_Description }}</view>
				</view>
				<view class="item">
					<label class="label">事故等级</label>
					<view class="text">{{ Event.F_Level }}</view>
				</view>
				<view class="item">
					<label class="label">高潜在风险</label>
					<view class="text">{{ Event.F_HighRisk ? '是' : '否' }}</view>
				</view>
				<view class="item">
					<label class="label">报告人</label>
					<view class="text">{{ Event.F_ReportUser }}</view>
				</view>
				<view class="item">
					<label class="label">报告人工号</label>
					<view class="text">{{ Event.F_ReportUserNum }}</view>
				</view>
				<view class="item">
					<label class="label">班组</label>
					<view class="text">{{ Event.F_Class }}</view>
				</view>
				<view class="item">
					<label class="label">BPU</label>
					<view class="text">{{ Event.F_BPU }}</view>
				</view>
				<view class="item">
					<label class="label">受伤人员姓名</label>
					<view class="text">{{ Event.F_Surname }}</view>
				</view>
				<view class="item">
					<label class="label">受伤人员工号</label>
					<view class="text">{{ Event.F_Surno || ''}}</view>
				</view>
				<view class="item">
					<label class="label">受伤人员年龄</label>
					<view class="text">{{ Event.F_Surage || ''}}</view>
				</view>
				<view class="item">
					<label class="label">本岗时间</label>
					<view class="text">{{ Event.F_JobTime || '' }}</view>
				</view>
				<view class="item">
					<label class="label">入厂时间</label>
					<view class="text">{{ Event.F_SurJoinTime || '' }}</view>
				</view>
				<view class="item">
					<label class="label">受伤部位</label>
					<view class="text">{{ Event.F_SurBuWei || ''}}</view>
				</view>
				<view class="item">
					<label class="label">受伤类型</label>
					<view class="text">{{ Event.F_SurType || ''}}</view>
				</view>
				<view class="item">
					<label class="label">调查人员</label>
					<view class="text">{{ Event.F_ResearchUser }}</view>
				</view>
				<view class="item">
					<label class="label">立即措施</label>
					<view class="text">{{ Event.F_Actions }}</view>
				</view>
				<view class="item">
					<label class="label">草图或附件</label>
					<view class="file-list">
						<block v-for="(file, index) in getFileList(Event.F_Sketch)" :key="index">
							<image v-if="isImageFile(file)" :src="file" @click="previewImage(file,'sketch')"></image>
							<view v-else class="file-item" @click="openFile(file)">
								<text class="file-name">{{getFileName(file)}}</text>
							</view>
						</block>
					</view>
				</view>
				<view class="item">
					<label class="label">事故报告</label>
					<view class="file-list">
						<block v-for="(file, index) in getFileList(Event.F_ReportFile)" :key="index">
							<image v-if="isImageFile(file)" :src="file" @click="previewImage(file,'report')"></image>
							<view v-else class="file-item" @click="openFile(file)">
								<text class="file-name">{{getFileName(file)}}</text>
							</view>
						</block>
					</view>
				</view>
				<view class="item">
					<label class="label">原因分析</label>
					<view class="text">{{ Event.F_Reason }}</view>
				</view>
				<view class="item">
					<label class="label">行动措施</label>
					<view class="text">{{ Event.F_ActionDesc }}</view>
				</view>
				<view class="item" v-if="Event.F_MeasureGroup && Event.F_MeasureGroup.length > 0">
					<label class="label">行动措施</label>
					<view class="measure-group" v-for="(measure, measureIndex) in Event.F_MeasureGroup" :key="measureIndex">
						<view class="measure-item">
							<view class="text">{{ measure.description }}</view>
							<view class="file-list">
								<block v-for="(file, fileIndex) in getFileList(measure.files)" :key="fileIndex">
									<image v-if="isImageFile(file)" :src="file" @click="previewImage(file, 'measure', measureIndex)"></image>
									<view v-else class="file-item" @click="openFile(file)">
										<text class="file-name">{{getFileName(file)}}</text>
									</view>
								</block>
							</view>
						</view>
					</view>
				</view>
				<view class="item">
					<label class="label">是否全部整改</label>
					<view class="text">{{ Event.F_Correction ? '是' : '否' }}</view>
				</view>
			</view>
		</view>
		
		<view class="botomm-btn" v-if="$checkBtnAuth('sgtzbj')">
			<navigator :url="`/pages/event/edit?id=${id}`" open-type="navigate">
				<view class="btn">编辑事故信息</view>
			</navigator>
		</view>
	</view>
</template>

<script>
	import { mapGetters } from 'vuex'
	import wButton from '@/components/watch-login/watch-button.vue'
	import globalConfig from '@/config'
	
	export default {
		components: {
			wButton,
		},
		computed: {
			...mapGetters(['themeBgColor', 'darkMode','user']),
		},
		data() {
			return {
				baseUrl:'',
				id: '', // 事故ID
				Event: {
					F_Id: '',
					F_Name: '',
					F_Num: '',
					F_Description: '',
					F_Date: '',
					F_Level: '',
					F_Category: '',
					F_BMU: '',
					F_Class: '',
					F_BPU: '',
					F_Surname: '',
					F_Surno: '', // 受伤人员工号
					F_Surage: '', // 受伤人员年龄
					F_JobTime: '', // 本岗时间
					F_SurJoinTime: '', // 入厂时间
					F_SurBuWei: '', // 受伤部位
					F_SurType: '', // 受伤类型
					F_ResearchUser: '',
					F_Actions: '',
					F_Sketch: '',
					F_DeleteMark: false,
					F_EnabledMark: true,
					F_CreatorTime: '',
					F_CreatorUserId: '',
					F_CreatorUserName: '',
					F_LastModifyTime: '',
					F_LastModifyUserId: '',
					F_DeleteTime: '',
					F_DeleteUserId: '',
					F_ReportUser: '',
					F_ReportUserNum: '',
					F_HighRisk: false,
					F_Correction: false,
					F_MeasureGroup: []
				},
				bmuOptions:[]
			}
		},
		onReady() {
			uni.setNavigationBarTitle({
				title: this.$t('事故详情')
			})
			this.setNavBarColor()
		},
		onShow() {
			this.setNavBarColor()
		},
		async onLoad(options) {
			this.baseUrl = globalConfig.baseUrl.replace('/api', '');
			await this.loadOrgList();
			console.log('id',options)
			if (options.id) {
				this.id = options.id
				this.loadEventInfo()
			}
			// 监听刷新事件
			uni.$on('eventInfoRefresh', this.loadEventInfo)
		},
		onUnload() {
			// 移除事件监听
			uni.$off('eventInfoRefresh', this.loadEventInfo)
		},
		onShareAppMessage() {
		    return {
		      title: 'EHS-掌上观纹', // 自定义分享标题
		      path: '/pages/start/index', // 分享页面路径（默认当前页）
		      imageUrl: this.baseUrl+'/images/nideke.jpg', // 分享图片（可选）
		      success(res) {
		        console.log('分享成功', res);
		      },
		      fail(err) {
		        console.error('分享失败', err);
		      }
		    };
		},
		methods: {
			checkPermission(){
				if(this.user.F_RoleId === "08dc7a3d-e5a8-44cf-87ea-15347b9fa336"){
					return false;
				}else{
					return true;
				}
			},
			setNavBarColor() {
				uni.setNavigationBarColor({
					frontColor: '#000000',
					backgroundColor: this.themeBgColor,
					animation: {
						duration: 400,
						timingFunc: 'easeIn'
					}
				})
			},
			async loadOrgList(){
				this.$minApi.getOrgList().then(res=>{
					console.log('sss1')
					this.bmuOptions = [{ value: "", label: "==请选择==" }];
					    
					// 使用正确的 map 语法将 API 数据添加到数组
					const orgOptions = res.map(a => ({ value: a.F_Id, label: a.F_FullName }));
					
					// 合并数组
					this.bmuOptions = this.bmuOptions.concat(orgOptions);
					console.log('sss')
				})
			},
			loadEventInfo() {
				uni.showLoading({
					title: '加载中...'
				})
				console.log('dept',this.bmuOptions)
				this.$minApi.getEventInfo(this.id).then(res => {
					if (res.F_MeasureGroup && typeof res.F_MeasureGroup === 'string') {
						try {
							res.F_MeasureGroup = JSON.parse(res.F_MeasureGroup);
						} catch(e) {
							console.error("Failed to parse F_MeasureGroup", e);
							res.F_MeasureGroup = [];
						}
					} else if (!res.F_MeasureGroup) {
						res.F_MeasureGroup = [];
					}
					this.Event = res
					let dept = this.bmuOptions.find(item => item.value == res.F_BMU);
					
					console.log('dept1',dept,this.bmuOptions)
					this.Event.F_BMUName = dept?.label
				}).catch((e) => {
					console.log('e',e)
					uni.showToast({
						title: '加载失败',
						icon: 'none'
					})
				}).finally(() => {
					uni.hideLoading()
				})
			},
			previewImage(currentFile,type, index) {
				let fileList = []
				if(type == 'sketch'){
					fileList = this.getFileList(this.Event.F_Sketch);
				}else if(type == 'report'){
					console.log('this.Event.F_ReportFile:==>',this.Event.F_ReportFile)
					fileList = this.getFileList(this.Event.F_ReportFile);
				} else if (type === 'measure') {
					if (this.Event.F_MeasureGroup && this.Event.F_MeasureGroup[index]) {
						fileList = this.getFileList(this.Event.F_MeasureGroup[index].files);
					}
				}
				
				const imageList = fileList.filter(file => this.isImageFile(file));
				const currentIndex = imageList.indexOf(currentFile);
				
				if (imageList.length > 0) {
					uni.previewImage({
						urls: imageList,
						current: currentIndex
					});
				}
			},
			goEdit() {
				uni.navigateTo({
					url: `/pages/event/edit?id=${this.id}`
				})
			},
			getFileList(file) {
				if (file) {
					return file.split(',').map(path => this.getFullPath(path.trim()));
				}
				return [];
			},
			isImageFile(file) {
				return file.toLowerCase().endsWith('.jpg') || file.toLowerCase().endsWith('.png') || file.toLowerCase().endsWith('.jpeg');
			},
			getFileName(file) {
				return file.split('/').pop().split('.').slice(0, -1).join('.');
			},
			getFullPath(path) {
				if (!path) return '';
				return globalConfig.baseUrl.replace("/api","") + path;
			},
			openFile(file) {
				uni.downloadFile({
					url: file,
					success: (res) => {
						if (res.statusCode === 200) {
							uni.openDocument({
								filePath: res.tempFilePath,
								success: () => {
									console.log('打开文件成功');
								},
								fail: () => {
									uni.showToast({
										title: '打开文件失败',
										icon: 'none'
									});
								}
							});
						}
					},
					fail: () => {
						uni.showToast({
							title: '下载文件失败',
							icon: 'none'
						});
					}
				});
			}
		}
	}
</script>

<style lang="less" scoped>
	.container{
		display: flex;
		flex-direction: column;
		gap: 20rpx;
		min-height: 100vh;
		padding-bottom: 320rpx;
		.header{
			background-color: white;
			padding: 20rpx;
			display: flex;
			flex-direction: column;
			width: 100vw;
			.title{
				color: #333333;
				font-weight: 650;
				font-size: 36rpx;
				width: 100%;
			}
			.detail{
				display: flex;
				justify-content: space-between;
				.num{
					color: #999999;
					font-size: 24rpx;
				}
				.status {
					width: 140rpx;
					height: 30rpx;
					color: #ffffff;
					font-size: 24rpx;
					align-items: center;
					text-align: center;
					line-height: 34rpx;
					clip-path: polygon(20% 0%, 100% 0%, 80% 100%, 0% 100%);
				}
				.status-green {
				  background-color: green;
				}
				.status-red {
				  background-color: rgb(245, 154, 35);
				}
			}
		}
		.info {
			background-color: white;
			padding: 20rpx;
			.title{
				color: #333333;
				font-weight: 400;
				font-size: 28rpx;
				margin-bottom: 20rpx;
			}
			.content{
				display: flex;
				flex-direction: column;
				gap: 20rpx;
				.item{
					display: flex;
					flex-direction: column;
					.label{
						color: #999999;
						font-size: 24rpx;
					}
					.text{
						color: #666666;
						font-size: 28rpx;
					}
					.photo{
						width: 200rpx;
						height: 160rpx;
					}
				}
			}
		}
		.measure-group {
			display: flex;
			flex-direction: column;
			gap: 15rpx;
			margin-top: 10rpx;
		}
		.staff-list{
			display: flex;
			flex-wrap: wrap;
			.staff-item{
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				width: 24%;
				image{
					width: 120rpx;
					height: 120rpx;
				}
				.label{
					color: #666666;
					font-size: 28rpx;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
				}
			}
		}
		.botomm-btn {
			padding-bottom: 100rpx;
			height: 230rpx;
			position: fixed; /* 固定在屏幕底部 */
			bottom: 0;
			left: 0;
			width: 100%; /* 占满整个屏幕宽度 */
			background-color: #fff; /* 背景颜色，防止透明 */
			padding: 20rpx 0; /* 上下留白 */
			text-align: center; /* 让按钮居中 */
			align-items: center;
			display: flex;
			justify-content: center;
			box-shadow: 0 -10rpx 20rpx rgba(0, 0, 0, 0.1); /* 添加阴影，提升层次感 */
			z-index: 999; /* 确保层级较高 */
			.btn{
				font-size: 30rpx;
				margin-bottom: 140rpx;
				width: 601rpx;
				height: 100rpx;
				line-height: 100rpx;
				text-align: center;
				margin-left: auto;
				margin-right: auto;
				margin-top: 96rpx;
				background: linear-gradient(to right, #00984a, #00984a, #00984a);
				color: #FFFFFF;
				border: none;
				border-radius: 2.5rem;
				box-shadow: 0 0 60rpx 0 rgba(0, 0, 0, .2);
				transition: all 0.4s cubic-bezier(.57, .19, .51, .95);
			}
		}
		.file-list {
			display: flex;
			flex-wrap: wrap;
			gap: 20rpx;
			
			image {
				width: 200rpx;
				height: 160rpx;
				border-radius: 8rpx;
			}
			
			.file-item {
				padding: 10rpx 20rpx;
				background-color: #f5f5f5;
				border-radius: 8rpx;
				
				.file-name {
					font-size: 24rpx;
					color: #666666;
				}
			}
		}
	}
</style>