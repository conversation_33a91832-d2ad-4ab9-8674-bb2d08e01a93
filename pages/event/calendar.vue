<template>
  <view class="container">
    <!-- 统计卡片 -->
    <view class="stats-cards">
      <view class="stat-card">
        <view class="stat-title">无损工事故天数</view>
        <view class="stat-value">{{ DaysSinceLastLoss }}<text class="unit">天</text></view>
      </view>
      <view class="stat-card">
        <view class="stat-title">无损工天数最高纪录</view>
        <view class="stat-value">{{ MaxConsecutiveDays }}<text class="unit">天</text></view>
      </view>
    </view>

    <!-- 日历部分 -->
    <view class="calendar-section">
      <view class="calendar-title">绿十字</view>
      
      <!-- 月份选择器 -->
      <view class="month-picker">
        <label>月份</label>
        <picker mode="date" fields="month" @change="onMonthChange" :value="StartDate">
          <view class="uni-input">{{ StartDate }}</view>
        </picker>
      </view>
	 <view style="display: flex;justify-content: center;width: 95vw;">
      <!-- 日历网格 -->
      <view class="calendar-grid">
        <view 
          v-for="(cell, index) in gridCells" 
          :key="index"
          :class="['grid-cell', {'hidden': cell.hidden}]"
          :style="{ backgroundColor: cell.color }"
          @click="handleCellClick(cell)"
        >
          {{ cell.day }}
        </view>
      </view>
	  </view>

      <!-- 图例 -->
      <view class="legend">
        <view class="legend-item" v-for="(item, index) in legendItems" :key="index">
          <view class="legend-color" :style="{ backgroundColor: item.color }"></view>
          <text class="legend-text">{{ item.text }}</text>
        </view>
      </view>
    </view>

    <!-- 事故列表 -->
    <view class="accident-list">
      <view class="list-title">事故台账 ({{ EventList.length }})</view>
      <view class="accident-item" v-for="(item,index) in EventList"  v-if="EventList.length > 0">
      	<navigator :url="`/pages/event/info?id=${item.F_Id}`" open-type="navigate" class="navbar">
      		<view class="header">
      			<view class="title">事故编号：{{ item.F_Num }}</view>
      		</view>
      		<view class="content">
      			<view class="title">{{ item.F_Description }}</view>
      			<view>车间/科室：{{ item.F_BMUName}}</view>
      			<view>受伤人姓名：{{ item.F_Surname }}</view>
      			<view>事故等级：{{ item.F_Level }}</view>
      			<view class="bottom">
      				<view class="user"><image src="/static/img/profile.svg"></image>报告人{{ item.F_ReportUser }}</view>
      				<view class="clock"><image src="/static/img/clock.svg"></image>{{ item.F_Date }}</view>
      			</view>
      		</view>
      	</navigator>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      searchDate: '',
      colorMap: {
        "空": "#ffffff",
        // "无": "#92D14F",
		"无": "#00984a",
        "虚惊": "#31849b",
        "急救": "#00B0F0",
        "医疗处理": "#FFD966",
        "工作受限": "#ED7D31",
        "损工": "#FF0000",
        "致残": "#C00000",
        "死亡": "#A5A5A5"
      },
      legendItems: [
        { color: '#A5A5A5', text: '死亡' },
        { color: '#C00000', text: '致残' },
        { color: '#FF0000', text: '损工' },
        { color: '#ED7D31', text: '工作受限' },
        { color: '#FFD966', text: '医疗处理' },
        { color: '#00B0F0', text: '急救' },
        { color: '#31849b', text: '虚惊' },
		{ color: '#00984a', text: '无伤害' }
        // { color: '#92D14F', text: '无伤害' }
      ],
	  DaysSinceLastLoss: 0,
	  MaxConsecutiveDays: 0,
      gridCells: [],
      EventList:[],
	  StartDate: new Date().toISOString().slice(0,7)
    }
  },
  created() {
    this.initCalendar()
  },
  onShareAppMessage() {
      return {
        title: 'EHS-掌上观纹', // 自定义分享标题
        path: '/pages/start/index', // 分享页面路径（默认当前页）
        imageUrl: this.baseUrl+'/images/nideke.jpg', // 分享图片（可选）
        success(res) {
          console.log('分享成功', res);
        },
        fail(err) {
          console.error('分享失败', err);
        }
      };
  },
  methods: {
	onMonthChange(e){
		this.StartDate = e.detail.value;
		this.initCalendar();
	},
    initCalendar() {
      // 初始化日历网格
      this.gridCells = this.generateGridCells()
      // 加载当前月份数据
      this.loadData();
    },
	getDaysInMonth(dateString) {
	    const [year, month] = dateString.split('-').map(Number);
	    return new Date(year, month, 0).getDate();
	},
    generateGridCells() {
      const cells = []
      const totalCells = 49 // 7x7 网格

      // 初始化所有单元格为隐藏
      for (let i = 0; i < totalCells; i++) {
        cells.push({ hidden: true })
      }
		const days = this.getDaysInMonth(this.StartDate);
      // 第一行 - 3个格子 (1,2,3)
      cells[2] = { day: 1, color: '#ffffff' }
      cells[3] = { day: 2, color: '#ffffff' }
      cells[4] = { day: 3, color: '#ffffff' }

      // 第二行 - 3个格子 (4,5,6)
      cells[9] = { day: 4, color: '#ffffff' }
      cells[10] = { day: 5, color: '#ffffff' }
      cells[11] = { day: 6, color: '#ffffff' }

      // 第三行 - 7个格子 (7-13)
      cells[14] = { day: 7, color: '#ffffff' }
      cells[15] = { day: 8, color: '#ffffff' }
      cells[16] = { day: 9, color: '#ffffff' }
      cells[17] = { day: 10, color: '#ffffff' }
      cells[18] = { day: 11, color: '#ffffff' }
      cells[19] = { day: 12, color: '#ffffff' }
      cells[20] = { day: 13, color: '#ffffff' }

      // 第四行 - 7个格子 (14-20)
      cells[21] = { day: 14, color: '#ffffff' }
      cells[22] = { day: 15, color: '#ffffff' }
      cells[23] = { day: 16, color: '#ffffff' }
      cells[24] = { day: 17, color: '#ffffff' }
      cells[25] = { day: 18, color: '#ffffff' }
      cells[26] = { day: 19, color: '#ffffff' }
      cells[27] = { day: 20, color: '#ffffff' }

      // 第五行 - 7个格子 (21-27)
      cells[28] = { day: 21, color: '#ffffff' }
      cells[29] = { day: 22, color: '#ffffff' }
      cells[30] = { day: 23, color: '#ffffff' }
      cells[31] = { day: 24, color: '#ffffff' }
      cells[32] = { day: 25, color: '#ffffff' }
      cells[33] = { day: 26, color: '#ffffff' }
      cells[34] = { day: 27, color: '#ffffff' }

      // 第六行 - 3个格子 (28,29,30)
      cells[37] = { day: 28, color: '#ffffff' }
	  if(days == 28){
		  cells[38] = { day: '', color: '#ffffff', hidden: false }
		  cells[39] = { day: '', color: '#ffffff', hidden: false }
		  cells[44] = { day: '', color: '#ffffff', hidden: false }
		  cells[45] = { day: '', color: '#ffffff', hidden: false }
		  cells[46] = { day: '', color: '#ffffff', hidden: false }
	  }else{
		  cells[38] = { day: 29, color: '#ffffff' }
		  cells[39] = { day: 30, color: '#ffffff' }
	  }

      // 第七行 - 3个格子 (31和两个空白格)
	  if(days == 31){
		cells[44] = { day: 31, color: '#ffffff' }
	  }else{
		cells[44] = { day: '', color: '#ffffff', hidden: false }
	  }
      
      cells[45] = { day: '', color: '#ffffff', hidden: false }
      cells[46] = { day: '', color: '#ffffff', hidden: false }

      return cells
    },
    async loadData() {
		try{
			let that = this;
			this.$minApi.loadEventByMonth({month: that.StartDate}).then(res => {
				that.updateCalendar(res)
			})
			
			this.$minApi.getEventDays().then(res=>{
				that.DaysSinceLastLoss = res.DaysSinceLastLoss;
				that.MaxConsecutiveDays = res.MaxConsecutiveDays;
			})
		} catch (error) {
			console.error('加载数据失败:', error)
		}
    },
    updateCalendar(data) {
      this.gridCells = this.gridCells.map(cell => {
        if (cell.hidden) return cell
        const dayData = this.findDayData(data, cell.day)
        return {
          ...cell,
          color: this.colorMap[dayData ? dayData.Level : '空']
        }
      })
    },
    findDayData(data, day) {
      if (!data || !Array.isArray(data)) return null
      const dateStr = this.formatDateString(this.StartDate, day)
      return data.find(item => item.Day === dateStr)
    },
    formatDateString(monthStr, day) {
      const date = new Date(monthStr)
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      return `${year}-${month}-${day.toString().padStart(2, '0')}`
    },
    async handleCellClick(cell) {
      if (cell.hidden || !cell.day) return
      const date = this.formatDateString(this.StartDate, cell.day)
      try {
		this.$minApi.loadEventByDay({day: date}).then( res => {
			this.updateTable(res)
		})
        
      } catch (error) {
        console.error('加载日期数据失败:', error)
      }
    },
    updateTable(data) {
      this.EventList = data.filter(item => item.Level !== '无' && item.Level !== '空')
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
}

.stats-cards {
  display: flex;
  margin-bottom: 10rpx;
  gap: 20rpx;
  padding: 20rpx;
  .stat-card {
    flex: 1;
    background: #fff;
    padding: 15rpx 45rpx ;
    border-radius: 8rpx;
	// border: 1rpx solid #eee;
    
    .stat-title {
      font-size: 24rpx;
      color: #666;
      margin-bottom: 8rpx;
    }
    
    .stat-value {
      font-size: 46rpx;
      font-weight: bold;
      color: #333;
      
      .unit {
        font-size: 22rpx;
        margin-left: 4rpx;
      }
    }
  }
}

.calendar-section {
	padding: 20rpx;
  background: #fff;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  
  .calendar-title {
    font-size: 26rpx;
    font-weight: bold;
    margin-bottom: 10rpx;
  }
  
  .month-picker {
    display: flex;
    align-items: center;
    margin-bottom: 10rpx;
    gap: 10rpx;
    text {
      margin-right: 10rpx;
      font-size: 24rpx;
    }
    .uni-input{
        color: #7c7c7c;
        border: 1px solid #00984a;
        align-items: center;
        text-align: left;
        width: 36vw;
        border-radius: 10rpx !important;
        line-height: 20rpx;
    }
  }
}

.calendar-grid {
	width: 84vw;
  display: grid;
  grid-template-columns: repeat(7, minmax(0, 1fr));
  // grid-auto-columns: 1fr;
  gap: 2rpx;
//   margin-bottom: 15rpx;
  padding: 20rpx;
  // max-width: 600rpx;
  // margin: 0 auto 15rpx;
  
  .grid-cell {
    aspect-ratio: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 28rpx;
    color: #555555;
    border-radius: 0;
    min-height: 80rpx;
    max-height: 85rpx;
    
    &.hidden {
      visibility: hidden;
      background-color: transparent;
    }

    &:not(.hidden) {
      border: 1rpx solid #eee;
    }
  }
}

.legend {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 6rpx;
  padding: 6rpx;
  
  .legend-item {
    display: flex;
    align-items: center;
    gap: 4rpx;
    
    .legend-color {
      width: 16rpx;
      height: 16rpx;
      border-radius: 50%;
    }
    
    .legend-text {
      font-size: 20rpx;
      color: #666;
    }
  }
}

.accident-list {
  background: #f3f4fa;
  padding: 20rpx;
  // padding-bottom: 40rpx;
  border-radius: 8rpx;
  gap: 20rpx;
  display: flex;
	flex-direction: column;
  .list-title {
    font-size: 26rpx;
    font-weight: bold;
    margin-bottom: 15rpx;
  }
  
  .accident-item{
  	color: #999999;
  	border: 1rpx solid rgba(242, 242, 242, 1);
  	display: flex;
  	flex-direction: column;
  	align-items: center;
  	justify-content: center;
  	width: 100%;
  	background-color: white;
  	border-radius: 10rpx;
  	.navbar{
  		width: 100%;
  	}
  	.header{
  		width: 100%;
  		border-bottom: 1rpx solid rgba(242, 242, 242, 1);
  		display: flex;
  		flex-direction: row;
  		align-items: center;
  		justify-content: space-between;
  		padding: 20rpx;
  		box-sizing: border-box;
  		.title{
  			color: #333333;
  			font-size: 28rpx;
  			font-weight: 300;
			border: none;
  		}
  		.status{
  			
  		}
  		.processing{
  			color: #F77214;
  		}
  		.complate{
  			color: #999999;
  		}
  	}
  	.content{
  		padding: 20rpx;
  		width: 100%;
  		display: flex;
  		flex-direction: column;
  		align-items: left;
  		// font-weight: 300;
  		.title{
  			color: #333333;
  			font-size: 32rpx;
  			font-weight: 400;
  			margin-bottom: 20rpx;
			border: none;
  		}
  		.bottom{
  			display: flex;
  			align-items: center;
  			justify-content: space-between;
  			gap:10rpx;
  			margin-top: 30rpx;
  			line-height: 40rpx;
  			.user{
  				display: flex;
  				align-items: center;
  				gap: 10rpx;
  				line-height: 40rpx;
  				image{
  					width: 50rpx;
  					height: 50rpx;
  				}
  			}
  			.clock{
  				display: flex;
  				align-items: center;
  				line-height: 40rpx;
  				gap: 5rpx;
  				image{
  					width: 30rpx;
  					height: 30rpx;
  				}
  			}
  		}
  	}
  }
}
</style> 