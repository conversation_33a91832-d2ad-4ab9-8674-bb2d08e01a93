<template>
  <view class="container">
    <!-- 顶部标签切换 -->
    <view class="tab-section">
      <view 
        class="tab-item" 
        :class="{ active: currentTab === 'calendar' }"
        @click="switchTab('calendar')"
      >
        绿十字
      </view>
	  <view
	    class="tab-item" 
	    :class="{ active: currentTab === 'charts' }"
	    @click="switchTab('charts')"
	  >
	    金字塔
	  </view>
	  <view
	    class="tab-item" 
	    :class="{ active: currentTab === 'bar' }"
	    @click="switchTab('bar')"
	  >事故趋势
	  </view>
    </view>
    
    <!-- 内容区域 -->
    <view class="content-section">
      <!-- 事故统计页面 -->
      <charts-page v-if="currentTab === 'charts'" />
      
      <!-- 事故日历页面 -->
      <calendar-page v-if="currentTab === 'calendar'" />
	  
	  <bar-page v-if="currentTab === 'bar'" />
    </view>
  </view>
</template>

<script>
import ChartsPage from './charts.vue'
import CalendarPage from './calendar.vue'
import BarPage from './bar.vue'
import { mapGetters } from 'vuex'
import globalConfig from '@/config'

export default {
  components: {
    ChartsPage,
    CalendarPage,
	BarPage
  },
  
  computed: {
    ...mapGetters(['themeBgColor', 'darkMode']),
  },
  
  data() {
    return {
		baseUrl: '',
        currentTab: 'calendar' // 默认显示统计页面
    }
  },
  
  onReady() {
    uni.setNavigationBarTitle({
      title: '事故统计'
    })
    this.setNavBarColor()
  },

  onShow() {
    this.setNavBarColor()
  },
  onLoad(){
	  this.baseUrl = globalConfig.baseUrl.replace('/api', '');
  },
  onShareAppMessage() {
      return {
        title: 'EHS-掌上观纹', // 自定义分享标题
        path: '/pages/start/index', // 分享页面路径（默认当前页）
        imageUrl: this.baseUrl+'/images/nideke.jpg', // 分享图片（可选）
        success(res) {
          console.log('分享成功', res);
        },
        fail(err) {
          console.error('分享失败', err);
        }
      };
  },
  methods: {
    switchTab(tab) {
      this.currentTab = tab
    },

    setNavBarColor() {
      uni.setNavigationBarColor({
        frontColor: '#000000',
        backgroundColor: this.themeBgColor,
        animation: {
          duration: 400,
          timingFunc: 'easeIn'
        }
      })
    }
  }
}
</script>

<style lang="scss">
.container {
  min-height: 100vh;
  background-color: #FFFFFF;
  // padding: 10rpx;
}

.tab-section {
  display: flex;
  background: #fff;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  width: 60vw;
  margin: 0 auto;
  
  .tab-item {
    flex: 1;
    text-align: center;
    padding: 15rpx 0;
    font-size: 28rpx;
    color: #666;
    cursor: pointer;
    border-radius: 0;
    
    &:first-child {
      border-top-left-radius: 8rpx;
      border-bottom-left-radius: 8rpx;
    }
    
    &:last-child {
      border-top-right-radius: 8rpx;
      border-bottom-right-radius: 8rpx;
    }
    
    &.active {
      color: #fff;
      background-color: #5B6E8A;
    }
  }
}

.content-section {
  flex: 1;
  overflow: hidden;
}
</style> 