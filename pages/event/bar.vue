<template>
	<view class="container">
		
		<view class="title">
			<image src="../../static/img/tabbar/tongjiHL.png"></image>
			<view class="sub-title">事故趋势</view>
		</view>
		<view class="filters">
			<view class="item">
				<label>类型</label>
				<picker mode="selector" @change="onYearTypeChange" :value="YearTypeIndex" :range="YearType">
					<view class="uni-input">{{YearType[YearTypeIndex]}}</view>
				</picker>
			</view>
			<view class="item">
				<label>事故类型</label>
				<view class="uni-input levels-selector" @tap="showLevelsPopup">
					{{selectedLevelsText || '请选择'}}
				</view>
			</view>
			<view class="item">
				<label>年份</label>
				<picker mode="date" fields="year" @change="onBeginYearChange" :value="StartYearDate">
					<view class="uni-input">{{ StartYearDate }}</view>
				</picker>
				<view style="margin-left: 10rpx;margin-right: 10rpx;"> - </view>
				<picker mode="date" fields="year" @change="onEndYearChange" :start="StartYearDate" :value="EndYearDate">
					<view class="uni-input">{{ EndYearDate }}</view>
				</picker>
			</view>
		</view>
		<view class="pie-charts" v-show="showCharts">
			<canvas canvas-id="PhXOmLiIUTXVkYwkUfQUCKRnXDhvhMOA" id="PhXOmLiIUTXVkYwkUfQUCKRnXDhvhMOA" class="charts" @tap="selectBar"/>
		</view>
		
		<!-- 事故列表 -->
		<view class="accident-list">
		  <view class="list-title">事故台账 ({{ total }})</view>
		  <view v-if="EventList.length > 0">
		  	<view class="accident-item" v-for="item in EventList" :key="item.F_Id">
		  		<navigator :url="'/pages/event/info?id=' + item.F_Id" open-type="navigate" class="navbar">
		  			<view class="header">
		  				<view class="title">事故编号：{{ item.F_Num }}</view>
		  			</view>
		  			<view class="content">
		  				<view class="title">{{ item.F_Description }}</view>
		  				<view>车间/科室：{{ item.F_BMUName}}</view>
		  				<view>受伤人姓名：{{ item.F_Surname }}</view>
		  				<view>事故等级：{{ item.F_Level }}</view>
		  				<view class="bottom">
		  					<view class="user"><image src="/static/img/profile.svg"></image>报告人{{ item.F_ReportUser }}</view>
		  					<view class="clock"><image src="/static/img/clock.svg"></image>{{ item.F_Date }}</view>
		  				</view>
		  			</view>
		  		</navigator>
		  	</view>
		  </view>
		</view>
		
		<!-- 事故类型多选弹出层 -->
		<view class="levels-popup" v-if="showLevelsModal" @tap="hideLevelsPopup">
			<view class="popup-content" @tap.stop>
				<view class="popup-header">
					<text class="popup-title">选择事故类型</text>
					<text class="popup-close" @tap="hideLevelsPopup">×</text>
				</view>
				<view class="popup-body">
					<checkbox-group @change="onLevelsCheckboxChange">
						<label class="checkbox-item" v-for="item in levels" :key="item.id">
							<checkbox :value="item.id" :checked="selectedLevels.includes(item.id)"/>
							<text>{{item.type}}</text>
						</label>
					</checkbox-group>
				</view>
				<view class="popup-footer">
					<button @tap="confirmLevelsSelection" type="primary">确定</button>
				</view>
			</view>
		</view>
		<!-- 自定义多选弹窗 -->
		<!-- <uni-popup ref="taskTypePopup" type="bottom" :mask-click="false">
			<view class="multi-select-popup">
				<view class="popup-header">
					<view class="title">选择事故类型</view>
					<view class="actions">
						<text class="select-all" @click="selectAllTaskTypes">{{isAllSelected ? '取消全选' : '全选'}}</text>
						<text class="close" @click="hideTaskTypeSelector">×</text>
					</view>
				</view>
				<view class="select-list">
					<checkbox-group @change="onLevelsCheckboxChange">
						<label class="select-item" v-for="(item, index) in levels" :key="index">
							<checkbox :value="item.id" :checked="selectedLevels.includes(item.id)" />
							<text class="item-name">{{item.type}}</text>
						</label>
					</checkbox-group>
				</view>
				<view class="popup-footer">
					<button class="confirm-btn" @click="confirmTaskTypeSelect">确定</button>
				</view>
			</view>
		</uni-popup> -->
		
	</view>
</template>

<script>
	import { mapGetters } from 'vuex'
	import uCharts from '@/common/lib/u-charts.min.js'
	
	var _self;
	var funnelCharts = {};
	export default {
		components: {
		},
		computed: {
			...mapGetters(['themeBgColor', 'darkMode']),
		},
		data() {
			return {
				deathData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
				fatalityData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
				lostTimeData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
				restrictedWorkData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
				medicalData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
				firstAidData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
				nearMissData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
				trctData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
				YearTypeIndex: 0,
				MonthTypeIndex: 0,
				cWidth: 750,
				cHeight: 500,
				pixelRatio: 1,
				months:[1,2,3,4,5,6,7,8,9,10,11,12],
				YearType: ['财政年','日历年'],
				funnelChartData:{
					series: [
					  {
						data: [
							{"name":"虚惊","centerText":"虚惊(0)","labelText":"","value":80},
							{"name":"急救","centerText":"急救(0)","labelText":"","value":60},
							{"name":"医疗处理","centerText":"医疗处理(0)","labelText":"","value":50},
							{"name":"工作受限","centerText":"工作受限(0)","labelText":"","value":40},
							{"name":"损工","centerText":"损工(0)","labelText":"","value":30},
							{"name":"致残","centerText":"致残(0)","labelText":"","value":20},
							{"name":"死亡","centerText":"死亡(0)","labelText":"","value":10}]
					  }
					]
				},
				columnChartData: {
					categories: ["2018","2019","2020","2021","2022","2023"],
					series: [
						{
							name: "虚惊",
							textColor: "#FFFFFF",
							data: [35,36,31,33,13,34]
						},
						{
							name: "急救",
							textColor: "#FFFFFF",
							data: [35,36,31,33,13,34]
						},
						{
							name: "医疗处理",
							textColor: "#FFFFFF",
							data: [18,27,21,24,6,28]
						},
						{
							name: "工作受限",
							textColor: "#FFFFFF",
							data: [35,36,31,33,13,34]
						},
						{
							name: "损工",
							textColor: "#FFFFFF",
							data: [18,27,21,24,6,28]
						},
						{
							name: "致残",
							textColor: "#FFFFFF",
							data: [35,36,31,33,13,34]
						},
						{
							name: "死亡",
							textColor: "#FFFFFF",
							data: [35,36,31,33,13,34]
						}
					]
				},
				StartDate: new Date().toISOString().slice(0, 7),
				StartYearDate: (new Date().getFullYear() - 3).toString(),
				EndYearDate: new Date().toISOString().slice(0,4),
				years:[],
				total: 0,
				queryParams: {
					page: 1,
					rows: 999,
					beginDate: '',
					endDate: '',
					level: '',
					bmu: ''
				},
				EventList: [],
				loading: false,
				isEnd: false,
				// 事故类型多选相关
				showLevelsModal: false,
				selectedLevels: [],
				selectedLevelsText: '',
				levels: [
					{ "id":"虚惊", "type":"虚惊" },
					{ "id":"急救", "type":"急救" },
					{ "id":"医疗处理", "type":"医疗处理" },
					{ "id":"工作受限", "type":"工作受限" },
					{ "id":"损工", "type":"损工" },
					{ "id":"致残", "type":"致残" },
					{ "id":"死亡", "type":"死亡" }
				],
				showCharts: true
			}
		},
		mounted() {
			_self = this;
			//#ifdef MP-ALIPAY
			uni.getSystemInfo({
				success: function(res) {
					if (res.pixelRatio > 1) {
						//正常这里给2就行，如果pixelRatio=3性能会降低一点
						//_self.pixelRatio =res.pixelRatio;
						_self.pixelRatio = 2;
					}
				}
			});
			//#endif
			this.cWidth = uni.upx2px(750);
			this.cHeight = uni.upx2px(500);
			console.log('mouted')
			// this.loadPie();
			// this.drawFunnelCharts('oJIDbmRKGqzKSwPDhJystIYqMBYohANg',this.funnelChartData);
			// this.drawColumnCharts('PhXOmLiIUTXVkYwkUfQUCKRnXDhvhMOA', this.columnChartData);
			// this.loadTableCharts();
			this.loadBarCharts();
		},
		onShareAppMessage() {
		    return {
		      title: 'EHS-掌上观纹', // 自定义分享标题
		      path: '/pages/start/index', // 分享页面路径（默认当前页）
		      imageUrl: this.baseUrl+'/images/nideke.jpg', // 分享图片（可选）
		      success(res) {
		        console.log('分享成功', res);
		      },
		      fail(err) {
		        console.error('分享失败', err);
		      }
		    };
		},
		// 监听页面滚动到底部
		onReachBottom() {
			if (!this.loading && !this.isEnd) {
				this.queryParams.page++
				this.loadData()
			}
		},
		methods:{
			loadBarCharts(){
				let yearType = 'Fiscal';
				if(this.YearTypeIndex == 1){
					yearType = 'Calendar';
				}
				this.$minApi.loadBarCharts({yearType:yearType,beginTime: this.StartYearDate,endTime: this.EndYearDate,levelName: this.selectedLevelsText}).then(res=>{
					let yearsLoop = [];
					for(var start = this.StartYearDate;start<=this.EndYearDate;start++){
						yearsLoop.push(start);
					}
					// 定义固定的事故级别顺序
					// const levels = ["虚惊", "急救", "医疗处理", "工作受限", "损工", "致残", "死亡"];
					const levels = ["死亡", "致残", "损工", "工作受限", "医疗处理", "急救", "虚惊"];
					
					
					// 如果返回数据为空，创建默认数据结构
					if (res.length === 0) {
						const result = levels.map(level => ({
							name: level,
							textColor: "#FFFFFF",
							data: yearsLoop.map(() => 0)  // 为每一年填充0
						}));
						
						const columnChartData = {
							categories: yearsLoop,
							series: result
						}
						this.drawColumnCharts('PhXOmLiIUTXVkYwkUfQUCKRnXDhvhMOA', columnChartData);
						return;
					}
					// 获取所有年份
					const years = [...new Set(res.map(item => item.Year))].sort();
					// 如果有数据，按原来的逻辑处理
					const result = levels.map(level => ({
						name: level,
						textColor: "#FFFFFF",
						data: years.map(year => {
							// 查找对应年份和级别的数据
							const found = res.find(item => 
								item.Year === year && item.Level === level
							);
							return found ? found.Count : 0;
						}),
						formatter: function(val) {
							return val > 0 ? val : '';
						}
					}));
					
					const columnChartData = {
						categories: yearsLoop,
						series: result
					}
					this.years = years;
					console.log('ssss',columnChartData)
					this.drawColumnCharts('PhXOmLiIUTXVkYwkUfQUCKRnXDhvhMOA', columnChartData);
				})
			},
			loadTableCharts(){
				let yearType = 'Fiscal';
				if(this.MonthTypeIndex == 1){
					yearType = 'Calendar';
				}
				if(yearType == "Fiscal"){
					this.months = [4,5,6,7,8,9,10,11,12,1,2,3]
				}else{
					this.months = [1,2,3,4,5,6,7,8,9,10,11,12]
				}
				this.$minApi.loadTableCharts({yearType: yearType,month: this.StartDate}).then(res=>{
					// 处理数据
					setTimeout(()=>{
						// 初始化数组
						this.deathData = new Array(12).fill(0);
						this.fatalityData = new Array(12).fill(0);
						this.lostTimeData = new Array(12).fill(0);
						this.restrictedWorkData = new Array(12).fill(0);
						this.medicalData = new Array(12).fill(0);
						this.firstAidData = new Array(12).fill(0);
						this.nearMissData = new Array(12).fill(0);
						this.trctData = new Array(12).fill(0);
						res.forEach(item => {
						    // 月份索引需要减1，因为数组索引从0开始
						    const monthIndex = item.Month - 1;
						    
						    switch(item.Level) {
						        case "死亡":
						            this.$set(this.deathData, monthIndex, item.Count);
						            break;
						        case "致残":
						            this.$set(this.fatalityData, monthIndex, item.Count);
						            break;
						        case "损工":
						            this.$set(this.lostTimeData, monthIndex, item.Count);
						            break;
						        case "工作受限":
						            this.$set(this.restrictedWorkData, monthIndex, item.Count);
						            break;
						        case "医疗处理":
						            this.$set(this.medicalData, monthIndex, item.Count);
						            break;
						        case "急救":
						            this.$set(this.firstAidData, monthIndex, item.Count);
						            break;
						        case "虚惊":
						            this.$set(this.nearMissData, monthIndex, item.Count);
						            break;
								case "trct":
									this.$set(this.trctData, monthIndex, item.Count);
									break;
						    }
						});
						console.log('this.firstAidData',this.firstAidData)
					},500)
					
					const summary = {
					        "死亡": 0,
					        "致残": 0,
					        "损工": 0,
					        "工作受限": 0,
					        "医疗处理": 0,
					        "急救": 0,
					        "虚惊": 0
					    };
					
					    // 统计各级别的总数
					    res.forEach(item => {
					        if (summary.hasOwnProperty(item.Level)) {
					            summary[item.Level] += item.Count;
					        }
					    });
					    // 转换为列表格式
					    const summaryList = [
					        { name: "虚惊",centerText: ""+summary["虚惊"],labelText: "虚惊", value: 70 },
							{ name: "急救",centerText: ""+summary["急救"],labelText: "急救", value: 60 },
							{ name: "医疗处理",centerText: ""+summary["医疗处理"],labelText: "医疗处理", value: 50 },
							{ name: "工作受限",centerText: ""+summary["工作受限"],labelText: "工作受限", value: 40 },
							{ name: "损工",centerText: ""+summary["损工"],labelText: "损工", value: 30 },
					        { name: "致残",centerText: ""+summary["致残"],labelText: "致残", value: 20 },
					        { name: "死亡",centerText: ""+summary["死亡"],labelText: "死亡", value: 10 }
					    ];
						// this.funnelChartData.series[0].data.push(summaryList);
						const funnelChartData = {
							series: [
							  {
								data: summaryList
							  }
							]
						}
						setTimeout(() => {
							this.drawFunnelCharts('oJIDbmRKGqzKSwPDhJystIYqMBYohANg',funnelChartData);
						},500);
				})
			},
			getRowSum(data) {
			  return data.reduce((sum, current) => sum + current, 0)
			},
			onMonthChange(e) {
				this.StartDate = e.detail.value;
				this.loadTableCharts();
			},
			onYearTypeChange(e){
				this.YearTypeIndex = e.detail.value;
				this.loadBarCharts();
			},
			onMonthTypeChange(e){
				this.MonthTypeIndex = e.detail.value;
				this.loadTableCharts();
			},
			onBeginYearChange(e){
				this.StartYearDate = e.detail.value;
				this.loadBarCharts();
			},
			onEndYearChange(e){
				this.EndYearDate = e.detail.value;
				this.loadBarCharts();
			},
			// 显示事故类型选择弹出层
			showLevelsPopup() {
				this.showCharts = false;
				this.showLevelsModal = true;
			},
			// 隐藏事故类型选择弹出层
			hideLevelsPopup() {
				this.showCharts = true;
				this.showLevelsModal = false;
			},
			// 处理复选框变化
			onLevelsCheckboxChange(e) {
				this.selectedLevels = e.detail.value;
			},
			// 确认选择
			confirmLevelsSelection() {
				// 更新显示文本
				if (this.selectedLevels.length > 0) {
					// 如果选择项目超过3个，显示"已选择X项"
					// if (this.selectedLevels.length > 3) {
					// 	this.selectedLevelsText = `已选择${this.selectedLevels.length}项`;
					// } else {
						this.selectedLevelsText = this.selectedLevels.join(',');
					// }
				} else {
					this.selectedLevelsText = '';
				}
				this.hideLevelsPopup();
				// 重新加载数据
				this.loadBarCharts();
			},
			drawFunnelCharts(id,data){
				const ctx = uni.createCanvasContext(id, this);
				funnelCharts[id] = new uCharts({
					type: "funnel",
					context: ctx,
					width: _self.cWidth * _self.pixelRatio,
					height: _self.cHeight * _self.pixelRatio,
					series: data.series,
					animation: true,
					background: "#FFFFFF",
					color: ["#31849b","#00B0F0","#FFD966","#ED7D31","#FF0000","#C00000","#A5A5A5"],
					padding: [15,15,0,15],
					dataLabel: true,
					enableScroll: false,
					extra: {
					  funnel: {
						activeOpacity: 0.3,
						activeWidth: 10,
						border: true,
						borderWidth: 2,
						borderColor: "#FFFFFF",
						fillOpacity: 1,
						labelAlign: "left",
						type: "pyramid"
					  }
					}
				});
			},
			drawColumnCharts(id,data){
			  const ctx = uni.createCanvasContext(id, this);
			  funnelCharts[id] = new uCharts({
				type: "column",
				context: ctx,
				width: _self.cWidth * _self.pixelRatio,
				height: _self.cHeight * _self.pixelRatio,
				categories: data.categories,
				series: data.series,
				animation: true,
				background: "#FFFFFF",
				// color: ["#31849b","#00B0F0","#FFD966","#ED7D31","#FF0000","#C00000","#A5A5A5"],
				color: ["#A5A5A5", "#C00000", "#FF0000", "#ED7D31", "#FFD966", "#00B0F0", "#31849b"],
				padding: [15,15,0,5],
				enableScroll: false,
				legend: {},
				xAxis: {
				  disableGrid: true,
				 //  format: function(val) {
					// return parseInt(val); // 将纵轴的值转为整数
				 //  },
				},
				yAxis: {
					min: 0,
					max: 10,
					// format: function(val) {
					//   return val>0? val:''; // 将纵轴的值转为整数
					// }
				 //  data: [
					// {
					//   min: 1
					// }
				 //  ]
				},
				extra: {
				  column: {
					type: "stack",
					width: 30,
					activeBgColor: "#000000",
					activeBgOpacity: 0.08,
					labelPosition: "center"
				  }
				}
			  });
			},
			selectBar(e){
				
				const bar = funnelCharts[e.target.id].getCurrentDataIndex(e);
				const year = this.years[bar.index];
				this.queryParams.beginDate = year + '-01-01 00:00:00';
				this.queryParams.endDate = year+ '-12-31 23:59:59'
				if(this.YearTypeIndex == 0){
					this.queryParams.beginDate = year + '-04-01 00:00:00';
					this.queryParams.endDate = (parseInt(year)+1) + '-03-31 23:59:59'
				}
				this.queryParams.page = 1;
				
				this.loadData()
			},
			loadData() {
				if (this.loading || this.isEnd) return
				this.loading = true
				
				uni.showLoading({
					title: '加载中...'
				})
				
				this.$minApi.getEventList(this.queryParams).then(res => {
					if (res.state === 0) {
						if (this.queryParams.page === 1) {
							this.EventList = res.data
						} else {
							this.EventList = [...this.EventList, ...res.data]
						}
						this.total = res.count || this.EventList.length
						// 判断是否加载完全部数据
						// if (res.count < this.queryParams.rows) {
						// 	this.isEnd = true
						// }
					} else {
						uni.showToast({
							title: res.message || '加载失败',
							icon: 'none'
						})
					}
				}).catch(() => {
					uni.showToast({
						title: '加载失败',
						icon: 'none'
					})
				}).finally(() => {
					uni.hideLoading()
					this.loading = false
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.container{
		background-color: white;
		padding: 10rpx;
		display: flex;
		flex-direction: column;
		gap: 10rpx;
		.title{
			display: flex;
			gap: 6rpx;
			align-items: center;
			padding: 10rpx;
			border-bottom: 1rpx solid #dddddd;
			image{
				width: 40rpx;
				height: 40rpx;
			}
			.sub-title{
				font-size: 30rpx;
				font-weight: 650;
				color: #666666;
			}
		}
		.filters{
			display: flex;
			flex-direction: row;
			align-items: center;
			background-color: white;
			padding: 20rpx;
			width: 100%;
			flex-wrap: wrap;
			gap: 10rpx;
			.item{
				display: flex;
				flex-direction: row;
				align-items: center;
				flex: 1;
				label{
					color: #4c4c4c;
					// width: 20%;
					padding: 10rpx;
					align-items: center;
					text-align: center;
					line-height: 20rpx;
				}
				.uni-input{
					color: #7c7c7c;
					border: 1px solid #00984a;
					align-items: center;
					text-align: left;
					width: 36vw;
					border-radius: 10rpx !important;
					line-height: 20rpx;
				}
			}
		}
		.table {
		  // width: 99%;
		  // border: 1rpx solid #ddd;
		  // background: #fff;
		  transform: scale(0.95); /* 整体缩小以适应屏幕 */
		  transform-origin: top left;
		  padding-bottom: 60rpx;
		}
		
		.table-row {
		  display: flex;
		  border-bottom: 1rpx solid #ddd;
		}
		
		.cell {
		  display: flex;
		  align-items: center;
		  justify-content: center;
		  padding: 12rpx 4rpx; /* 减小内边距 */
		  font-size: 20rpx; /* 减小字体大小 */
		  border-right: 1rpx solid #ddd;
			border-bottom: 1rpx solid #ddd;
		  box-sizing: border-box;
		  margin-right: -1rpx; /* 抵消右边框 */
			margin-bottom: -1rpx; /* 抵消下边框 */
		}
		
		.month-cell {
		  flex: 1;
		  min-width: 8.2%; /* 设置最小宽度 */
		}
		
		/* 每一行的背景色样式 */
		.death .cell {
		  background-color: #a5a5a5;
		  color: white;
		}
		
		.fatality .cell {
		  background-color: #c00000;
		  color: white;
		}
		
		.lost-time .cell {
		  background-color: #ff0000;
		  color: white;
		}
		
		.restricted-work .cell {
		  background-color: #ffa514;
		  color: white;
		}
		
		.medical .cell {
		  background-color: #ffff00;
		}
		
		.first-aid .cell {
		  background-color: #00B0F0;
		  color: white;
		}
		
		.near-miss .cell {
		  background-color: #31849b;
		  color: white;
		}
		
		.header .cell {
		  background-color: #f5f5f5;
		  font-weight: bold;
		}
	}
	.charts{
	    width: 750rpx;
	    height: 500rpx;
	  }
	  .accident-list {
	    background: #f3f4fa;
	    padding: 20rpx;
	    // padding-bottom: 40rpx;
	    border-radius: 8rpx;
	    gap: 20rpx;
	    display: flex;
	  	flex-direction: column;
	    .list-title {
	      font-size: 26rpx;
	      font-weight: bold;
	      margin-bottom: 15rpx;
	    }
	    
	    .accident-item{
	    	color: #999999;
	    	border: 1rpx solid rgba(242, 242, 242, 1);
	    	display: flex;
	    	flex-direction: column;
	    	align-items: center;
	    	justify-content: center;
	    	width: 100%;
	    	background-color: white;
	    	border-radius: 10rpx;
	    	.navbar{
	    		width: 100%;
	    	}
	    	.header{
	    		width: 100%;
	    		border-bottom: 1rpx solid rgba(242, 242, 242, 1);
	    		display: flex;
	    		flex-direction: row;
	    		align-items: center;
	    		justify-content: space-between;
	    		padding: 20rpx;
	    		box-sizing: border-box;
	    		.title{
	    			color: #333333;
	    			font-size: 28rpx;
	    			font-weight: 300;
	  			border: none;
	    		}
	    		.status{
	    			
	    		}
	    		.processing{
	    			color: #F77214;
	    		}
	    		.complate{
	    			color: #999999;
	    		}
	    	}
	    	.content{
	    		padding: 20rpx;
	    		width: 100%;
	    		display: flex;
	    		flex-direction: column;
	    		align-items: left;
	    		// font-weight: 300;
	    		.title{
	    			color: #333333;
	    			font-size: 32rpx;
	    			font-weight: 400;
	    			margin-bottom: 20rpx;
	  			border: none;
	    		}
	    		.bottom{
	    			display: flex;
	    			align-items: center;
	    			justify-content: space-between;
	    			gap:10rpx;
	    			margin-top: 30rpx;
	    			line-height: 40rpx;
	    			.user{
	    				display: flex;
	    				align-items: center;
	    				gap: 10rpx;
	    				line-height: 40rpx;
	    				image{
	    					width: 50rpx;
	    					height: 50rpx;
	    				}
	    			}
	    			.clock{
	    				display: flex;
	    				align-items: center;
	    				line-height: 40rpx;
	    				gap: 5rpx;
	    				image{
	    					width: 30rpx;
	    					height: 30rpx;
	    				}
	    			}
	    		}
	    	}
	    }
	  }
	
	/* 事故类型多选弹出层样式 */
	.levels-popup {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 999999 !important;
	}
	
	.popup-content {
		background-color: white;
		border-radius: 10rpx;
		width: 80%;
		max-height: 70%;
		display: flex;
		flex-direction: column;
		z-index: 999999 !important;
		background-color: #fff;
	}
	
	.popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #eee;
	}
	
	.popup-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}
	
	.popup-close {
		font-size: 40rpx;
		color: #999;
		cursor: pointer;
	}
	
	.popup-body {
		padding: 20rpx 30rpx;
		max-height: 400rpx;
		overflow-y: auto;
	}
	
	.checkbox-item {
		display: flex;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #f5f5f5;
	}
	
	.checkbox-item:last-child {
		border-bottom: none;
	}
	
	.checkbox-item text {
		margin-left: 20rpx;
		font-size: 28rpx;
		color: #333;
	}
	
	.popup-footer {
		padding: 30rpx;
		border-top: 1rpx solid #eee;
	}
	
	.popup-footer button {
		width: 100%;
		height: 80rpx;
		line-height: 80rpx;
		border-radius: 10rpx;
		font-size: 28rpx;
	}
	
	.levels-selector {
		cursor: pointer;
		position: relative;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		max-width: 100%;
	}
	
	.levels-selector::after {
		content: "";
		position: absolute;
		right: 20rpx;
		top: 50%;
		transform: translateY(-50%);
		width: 0;
		height: 0;
		border-left: 10rpx solid transparent;
		border-right: 10rpx solid transparent;
		border-top: 10rpx solid #999;
	}
</style>