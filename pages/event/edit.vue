<template>
	<view class="container">
		<view class="info">
			<view class="title">事故信息</view>
			<view class="content">
				<view class="item">
					<label class="label">事故编号<text class="required">*</text></label>
					<input v-model="Event.F_Num" class="text" name="F_Num" maxlength="11" placeholder="请输入编号" />
				</view>
				<view class="item">
					<label class="label">事故时间<text class="required">*</text></label>
					<picker mode="date" @change="onDateChange" :value="Event.F_Date">
						<view class="uni-input">{{Event.F_Date || '请选择事故时间'}}</view>
					</picker>
				</view>
				<view class="item">
					<label class="label">车间/科室<text class="required">*</text></label>
					<picker mode="selector" @change="onBMUChange" :value="bmuIndex" range-key="label" :range="bmuOptions">
						<view class="uni-input">{{bmuOptions[bmuIndex].label}}</view>
					</picker>
				</view>
				<view class="item">
					<label class="label">设施/位置<text class="required">*</text></label>
					<input v-model="Event.F_Name" class="text" name="F_Name" maxlength="20" placeholder="请输入名称" />
				</view>
				<view class="item">
					<label class="label">事故描述<text class="required">*</text></label>
					<input v-model="Event.F_Description" class="text" name="F_Description" maxlength="200" placeholder="请输入事故描述" />
				</view>
				<view class="item">
					<label class="label">事故等级<text class="required">*</text></label>
					<picker mode="selector" @change="onLevelChange" :value="levelIndex" range-key="text" :range="levelOptions">
						<view class="uni-input">{{levelOptions[levelIndex].text}}</view>
					</picker>
				</view>
				<view class="item">
					<label class="label">高潜在风险</label>
					<switch :checked="Event.F_HighRisk" @change="e => Event.F_HighRisk = e.detail.value" />
				</view>
				<view class="item">
					<label class="label">报告人</label>
					<input v-model="Event.F_ReportUser" class="text" name="F_ReportUser" maxlength="11" placeholder="请输入报告人" />
				</view>
				<view class="item">
					<label class="label">报告人工号</label>
					<input v-model="Event.F_ReportUserNum" class="text" name="F_ReportUserNum" maxlength="11" placeholder="请输入报告人工号" />
				</view>
				<view class="item">
					<label class="label">班组<text class="required">*</text></label>
					<picker mode="selector" @change="onClassChange" :value="classIndex" range-key="text" :range="classOptions">
						<view class="uni-input">{{selectedClass}}</view>
					</picker>
				</view>
				<view class="item">
					<label class="label">BPU<text class="required">*</text></label>
					<input v-model="Event.F_BPU" class="text" name="F_BPU" maxlength="11" placeholder="请输入BPU" />
				</view>
				<view class="item">
					<label class="label">受伤人员姓名</label>
					<input v-model="Event.F_Surname" class="text" name="F_Surname" maxlength="11" placeholder="请输入受伤人员姓名" />
				</view>
				<view class="item">
					<label class="label">受伤人员工号</label>
					<input v-model="Event.F_Surno" class="text" name="F_Surno" maxlength="50" placeholder="请输入受伤人员工号" />
				</view>
				<view class="item">
					<label class="label">受伤人员年龄</label>
					<input v-model="Event.F_Surage" class="text" name="F_Surage" maxlength="3" placeholder="请输入受伤人员年龄" type="number" />
				</view>
				<view class="item">
					<label class="label">本岗时间</label>
					<!-- <picker mode="date" @change="e => Event.F_JobTime = e.detail.value" :value="Event.F_JobTime">
						<view class="uni-input">{{ Event.F_JobTime || '请选择本岗时间' }}</view>
					</picker> -->
					<input v-model="Event.F_JobTime" class="text" name="F_JobTime" placeholder="请输入本岗时间"/>
				</view>
				<view class="item">
					<label class="label">入厂时间</label>
					<!-- <picker mode="date" @change="e => Event.F_SurJoinTime = e.detail.value" :value="Event.F_SurJoinTime">
						<view class="uni-input">{{ Event.F_SurJoinTime || '请选择入厂时间' }}</view>
					</picker> -->
					<input v-model="Event.F_SurJoinTime" class="text" name="F_SurJoinTime" placeholder="请输入入厂时间"/>
				</view>
				<view class="item">
					<label class="label">受伤部位</label>
					<input v-model="Event.F_SurBuWei" class="text" name="F_SurBuWei" maxlength="100" placeholder="请输入受伤部位" />
				</view>
				<view class="item">
					<label class="label">受伤类型</label>
					<input v-model="Event.F_SurType" class="text" name="F_SurType" maxlength="50" placeholder="请输入受伤类型" />
				</view>
				<view class="item">
					<label class="label">调查人员</label>
					<input v-model="Event.F_ResearchUser" class="text" name="F_ResearchUser" maxlength="11" placeholder="请输入调查人员" />
				</view>
				<view class="item">
					<label class="label">立即措施</label>
					<input v-model="Event.F_Actions" class="text" name="F_Actions" maxlength="200" placeholder="请输入立即措施" />
				</view>
				<view class="item">
					<label class="label">草图或附件</label>
				</view>
				<view class="file-list">
					<view class="file-upload" @click="chooseImage('sketch')">
						<text class="upload-icon">+</text>
						<text class="upload-text">上传文件</text>
					</view>
					<block v-for="(file, index) in getFileList(Event.F_Sketch)" :key="index">
						<view class="file-item">
							<image v-if="isImageFile(file)" :src="file" mode="aspectFill" @click="previewImage(file, 'sketch')"></image>
							<view v-else class="file-name" @click="openFile(file)">
								<text>{{getFileName(file)}}</text>
							</view>
							<text class="delete-btn" @click="deleteFile(index, 'sketch')">×</text>
						</view>
					</block>
				</view>
				<view class="item">
					<label class="label">事故报告</label>
				</view>
				<view class="file-list">
					<view class="file-upload" @click="chooseImage('report')">
						<text class="upload-icon">+</text>
						<text class="upload-text">上传文件</text>
					</view>
					<block v-for="(file, index) in getFileList(Event.F_ReportFile)" :key="index">
						<view class="file-item">
							<image v-if="isImageFile(file)" :src="file" mode="aspectFill" @click="previewImage(file, 'report')"></image>
							<view v-else class="file-name" @click="openFile(file)">
								<text>{{getFileName(file)}}</text>
							</view>
							<text class="delete-btn" @click="deleteFile(index, 'report')">×</text>
						</view>
					</block>
				</view>
				<view class="item">
					<label class="label">原因分析</label>
					<input v-model="Event.F_Reason" class="text" name="F_Reason" maxlength="200" placeholder="请输入原因分析" />
				</view>
				<view class="item">
					<label class="label">行动措施</label>
					<input v-model="Event.F_ActionDesc" class="text" name="F_ActionDesc" maxlength="200" placeholder="请输入行动措施" />
				</view>
				<view class="item">
					<label class="label">行动措施</label>
				</view>
				<view class="measure-group-container">
					<view v-for="(measure, index) in Event.F_MeasureGroup" :key="index" class="measure-item">
						<textarea v-model="measure.description" placeholder="请输入行动措施描述" class="measure-textarea"></textarea>
						<view class="file-list-in-measure">
							<view class="file-upload" @click="chooseImage('measure', index)">
								<text class="upload-icon">+</text>
							</view>
							<block v-for="(file, fileIndex) in getFileList(measure.files)" :key="fileIndex">
								<view class="file-item">
									<image v-if="isImageFile(file)" :src="file" mode="aspectFill" @click="previewImage(file, 'measure', index)"></image>
									<view v-else class="file-name" @click="openFile(file)">
										<text>{{getFileName(file)}}</text>
									</view>
									<text class="delete-btn" @click="deleteFile(fileIndex, 'measure', index)">×</text>
								</view>
							</block>
						</view>
						<button @click="deleteMeasure(index)" class="delete-measure-btn">删除此项措施</button>
					</view>
					<button @click="addMeasure" class="add-measure-btn">添加行动措施</button>
				</view>
				<view class="item">
					<label class="label">是否全部整改</label>
					<switch :checked="Event.F_Correction" @change="e => Event.F_Correction = e.detail.value" />
				</view>
			</view>
		</view>
		<view class="botomm-btn">
			<wButton style='margin-bottom: 140rpx;' text="完成" :rotate="isRotate" @click.native="submitForm"></wButton>
		</view>
	</view>
</template>

<script>
	import { mapGetters } from 'vuex'
	import wButton from '@/components/watch-login/watch-button.vue'
	import wInput from '@/components/watch-login/watch-input.vue'
	import globalConfig from '@/config'
	export default{
		components: {
			wButton,
			wInput
		},
		computed: {
			...mapGetters(['themeBgColor', 'darkMode']),
			pageTitle() {
				return this.id ? '编辑事故' : '新增事故'
			},
			selectedClass() {
				return this.selectedClassValues.join(',') || '请选择班组'
			}
		},
		data(){
			return{
				baseUrl: '',
				isRotate: false,
				EnabledMarkList: [
					{ value: true, text: "有效" },
					{ value: false, text: "无效" },
				],
				EnabledMarkIndex:0,
				id: '', // 事故ID，编辑时有值，新增时为空
				Event: {
				    F_Id: '',
				    F_Name: '',
				    F_Num: '',
				    F_Description: '',
				    F_Date: '',
				    F_Level: '',
				    F_Category: '',
				    F_BMU: '',
				    F_Class: '',
				    F_BPU: '',
				    F_Surname: '',
				    F_ResearchUser: '',
				    F_Actions: '',
					F_Description: '',
				    F_Sketch: '',
				    F_DeleteMark: false,
				    F_EnabledMark: true,
				    F_CreatorTime: '',
				    F_CreatorUserId: '',
				    F_CreatorUserName: '',
				    F_LastModifyTime: '',
				    F_LastModifyUserId: '',
				    F_DeleteTime: '',
				    F_DeleteUserId: '',
				    F_ReportUser: '',
				    F_ReportUserNum: '',
				    F_SurNo: '',
				    F_Surage: '',
				    F_JobTime: null,
				    F_SurJoinTime: null,
				    F_SurBuWei: '',
				    F_SurType: '',
					F_HighRisk: false,
					F_Correction: false,
					F_MeasureGroup: []
				},
				// 事故等级选项
				levelOptions: [
					{ value: '虚惊', text: "虚惊" },
					{ value: '急救', text: "急救" },
					{ value: '医疗处理', text: "医疗处理" },
					{ value: '工作受限', text: "工作受限" },
					{ value: '损工', text: "损工" },
					{ value: '致残', text: "致残" },
					{ value: '死亡', text: "死亡" }
				],
				levelIndex: 0,
				isLoading: false,
				classOptions: [
					{ value: 'A', text: 'A' },
					{ value: 'B', text: 'B' },
					{ value: 'C', text: 'C' },
					{ value: 'X', text: 'X' },
					{ value: 'Y', text: 'Y' }
				],
				classIndex: 0,
				selectedClassValues: [], // 已选择的班组值
				// 车间选项
				bmuOptions: [
					// { value: "", label: "==请选择==" },
					// { value: "1", label: "生产设备部" },
					// { value: "1-1", label: "机加工业务单元" },
					// { value: "1-2", label: "冲压电机业务单元" },
					// { value: "1-3", label: "组装业务单元" },
					// { value: "1-4", label: "绿色发展科" },
					// { value: "1-5", label: "生产工程科" },
					// { value: "1-6", label: "生产技术创新科" },
					// { value: "2", label: "物流部" },
					// { value: "3", label: "家用产品研发部" },
					// { value: "4", label: "商用产品研发部" },
					// { value: "5", label: "质量和智能项目部" },
					// { value: "6", label: "人力资源部" },
					// { value: "7", label: "财务部" },
					// { value: "8", label: "法务部" },
					// { value: "9", label: "市场销售部" },
					// { value: "10", label: "总经理办公室" },
					// { value: "11", label: "NE NT项目组" }
				],
				bmuIndex: 0,
			}
		},
		onReady() {
			uni.setNavigationBarTitle({
			    title: this.pageTitle
			})
			this.setNavBarColor()
		},
		onShow() {
			this.setNavBarColor()
		},
		async onLoad(options) {
			this.baseUrl = globalConfig.baseUrl.replace('/api', '');
			await this.loadOrgList();
			if (options.id) {
				this.id = options.id
				this.loadEventInfo()
			}
		},
		onShareAppMessage() {
		    return {
		      title: 'EHS-掌上观纹', // 自定义分享标题
		      path: '/pages/start/index', // 分享页面路径（默认当前页）
		      imageUrl: this.baseUrl+'/images/nideke.jpg', // 分享图片（可选）
		      success(res) {
		        console.log('分享成功', res);
		      },
		      fail(err) {
		        console.error('分享失败', err);
		      }
		    };
		},
		methods:{
			 onContractorChange(e) {
			   // 更新当前选择的索引
			   this.ContractorIndex = e.detail.value;
			 },
			editor(){
				uni.navigateTo({
					url: '/pages/contactor/edit?id=111',
				})
			},
			async loadOrgList(){
				this.$minApi.getOrgList().then(res=>{
					this.bmuOptions = [{ value: "", label: "==请选择==" }];
					    
					// 使用正确的 map 语法将 API 数据添加到数组
					const orgOptions = res.map(a => ({ value: a.F_Id, label: a.F_FullName }));
					
					// 合并数组
					this.bmuOptions = this.bmuOptions.concat(orgOptions);
				})
			},
			setNavBarColor() {
				// navBar-bg-color
				uni.setNavigationBarColor({
				    frontColor: '#000000',
				    backgroundColor: this.themeBgColor,
				    animation: {
				        duration: 400,
				        timingFunc: 'easeIn'
				    }
				})
			},
			loadEventInfo() {
				uni.showLoading({
					title: '加载中...'
				})
				
				this.$minApi.getEventInfo(this.id).then(res => {
					if (res.F_MeasureGroup && typeof res.F_MeasureGroup === 'string') {
						try {
							res.F_MeasureGroup = JSON.parse(res.F_MeasureGroup);
						} catch(e) {
							res.F_MeasureGroup = [];
						}
					} else if (!res.F_MeasureGroup) {
						res.F_MeasureGroup = [];
					}
					this.Event = res
					// 如果有班组数据，解析并设置选中状态
					if (this.Event.F_Class) {
						this.selectedClassValues = this.Event.F_Class.split(',')
						// 找到对应的索引
						this.classIndex = this.classOptions.findIndex(option => option.value === this.selectedClassValues[0])
					}
					// 设置事故等级的索引
					if (this.Event.F_Level) {
						const levelIndex = this.levelOptions.findIndex(item => item.value === this.Event.F_Level)
						if (levelIndex !== -1) {
							this.levelIndex = levelIndex
						}
					}
					// 设置车间的索引
					if (this.Event.F_BMU) {
						const bmuIndex = this.bmuOptions.findIndex(item => item.value === this.Event.F_BMU)
						if (bmuIndex !== -1) {
							this.bmuIndex = bmuIndex
						}
					}
				}).catch(() => {
					uni.showToast({
						title: '加载失败',
						icon: 'none'
					})
				}).finally(() => {
					uni.hideLoading()
				})
			},
			onLevelChange(e) {
				this.levelIndex = e.detail.value
				this.Event.F_Level = this.levelOptions[this.levelIndex].value
			},
			onDateChange(e) {
				this.Event.F_Date = e.detail.value
			},
			chooseImage(type, measureIndex) {
				uni.chooseImage({
					count: 9,
					success: (res) => {
						// 上传文件到服务器
						const files = res.tempFilePaths;
						files.forEach(file => {
							this.uploadFile(file, type, measureIndex);
						});
					}
				});
			},
			uploadFile(filePath, type, measureIndex) {
				uni.showLoading({
					title: '上传中...'
				});
				
				const params = {
					filePath: filePath,
					name: 'file',
					fileby: type === 'sketch' ? 'sketch' : 'report',
					filetype: 1  // 1为图片类型
				};
				
				this.$minApi.uploadFile(params).then(res => {
					if (res.code === 200) {
						const path = res.data;
						if (type === 'measure') {
							const measure = this.Event.F_MeasureGroup[measureIndex];
							measure.files = measure.files ? `${measure.files},${path}` : path;
						} else if (type === 'sketch') {
							this.Event.F_Sketch = this.Event.F_Sketch ? 
								this.Event.F_Sketch + ',' + path : path;
						} else {
							this.Event.F_ReportFile = this.Event.F_ReportFile ? 
								this.Event.F_ReportFile + ',' + path : path;
						}
					} else {
						uni.showToast({
							title: '上传失败',
							icon: 'none'
						});
					}
				}).catch(() => {
					uni.showToast({
						title: '上传失败',
						icon: 'none'
					});
				}).finally(() => {
					uni.hideLoading();
				});
			},
			previewImage(file, type, measureIndex) {
				// 获取对应类型的文件列表
				let fileList = [];
				if (type === 'sketch') {
					fileList = this.getFileList(this.Event.F_Sketch);
				} else if (type === 'report') {
					fileList = this.getFileList(this.Event.F_ReportFile);
				} else if (type === 'measure') {
					if (this.Event.F_MeasureGroup && this.Event.F_MeasureGroup[measureIndex]) {
						fileList = this.getFileList(this.Event.F_MeasureGroup[measureIndex].files);
					}
				}
				
				// 过滤出图片文件
				const imageList = fileList.filter(f => this.isImageFile(f));
				
				// 找到当前图片在列表中的索引
				const currentIndex = imageList.indexOf(file);
				
				// 预览图片
				uni.previewImage({
					urls: imageList,
					current: currentIndex
				});
			},
			onClassChange(e) {
				this.classIndex = e.detail.value
				const value = this.classOptions[this.classIndex].value
				// 如果已经选择了该值，则移除；否则添加
				const index = this.selectedClassValues.indexOf(value)
				if (index > -1) {
					this.selectedClassValues.splice(index, 1)
				} else {
					this.selectedClassValues.push(value)
				}
				this.Event.F_Class = this.selectedClassValues.join(',')
			},
			onBMUChange(e) {
				this.bmuIndex = e.detail.value
				this.Event.F_BMU = this.bmuOptions[this.bmuIndex].value
			},
			submitForm() {
				console.log('3223')
				if (this.isLoading) return
				
				// 必填字段校验
				if (!this.Event.F_Name) {
					uni.showToast({
						title: '请输入设施/位置',
						icon: 'none'
					})
					return
				}
				if (!this.Event.F_Num) {
					uni.showToast({
						title: '请输入事故编号',
						icon: 'none'
					})
					return
				}
				if (!this.Event.F_Description) {
					uni.showToast({
						title: '请输入事故描述',
						icon: 'none'
					})
					return
				}
				if (!this.Event.F_Date) {
					uni.showToast({
						title: '请选择事故时间',
						icon: 'none'
					})
					return
				}
				if (!this.Event.F_Level) {
					uni.showToast({
						title: '请选择事故等级',
						icon: 'none'
					})
					return
				}
				if (!this.Event.F_BMU || this.Event.F_BMU === '' || this.Event.F_BMU === this.bmuOptions[0].value) {
					uni.showToast({
						title: '请选择车间/科室',
						icon: 'none'
					})
					return
				}
				if (!this.Event.F_Class) {
					uni.showToast({
						title: '请选择班组',
						icon: 'none'
					})
					return
				}
				if (!this.Event.F_BPU) {
					uni.showToast({
						title: '请输入BPU',
						icon: 'none'
					})
					return
				}
				
				this.isLoading = true
				this.isRotate = true
				
				uni.showLoading({
					title: '保存中...'
				})
				
				const payload = JSON.parse(JSON.stringify(this.Event));
				if (payload.F_MeasureGroup && Array.isArray(payload.F_MeasureGroup)) {
					payload.F_MeasureGroup = JSON.stringify(payload.F_MeasureGroup);
				}
				
				this.$minApi.saveEvent(payload).then(res => {
					if (res.state === 'success') {
						uni.showToast({
							title: '保存成功',
							icon: 'success'
						})
						// 发送刷新事件到列表页和详情页
						uni.$emit('eventListRefresh')
						uni.$emit('eventInfoRefresh')
						// 返回上一页
						setTimeout(() => {
							uni.navigateBack()
						}, 1500)
					} else {
						uni.showToast({
							title: res.message || '保存失败',
							icon: 'none'
						})
					}
				}).catch(() => {
					uni.showToast({
						title: '保存失败',
						icon: 'none'
					})
				}).finally(() => {
					uni.hideLoading()
					this.isLoading = false
					this.isRotate = false
				})
			},
			getFileList(file) {
				if (file) {
					return file.split(',').map(path => this.getFullPath(path.trim()));
				}
				return [];
			},
			getFullPath(path) {
				if (!path) return '';
				return globalConfig.baseUrl.replace("/api","") + path;
			},
			isImageFile(file) {
				return file.toLowerCase().endsWith('.jpg') || 
					   file.toLowerCase().endsWith('.jpeg') || 
					   file.toLowerCase().endsWith('.png') || 
					   file.toLowerCase().endsWith('.gif');
			},
			getFileName(file) {
				return file.split('/').pop().split('.').slice(0, -1).join('.');
			},
			deleteFile(index, type, measureIndex) {
				uni.showModal({
					title: '提示',
					content: '确定要删除该文件吗？',
					success: (res) => {
						if (res.confirm) {
							if (type === 'measure') {
								const measure = this.Event.F_MeasureGroup[measureIndex];
								const files = measure.files.split(',');
								files.splice(index, 1);
								measure.files = files.join(',');
							} else if (type === 'sketch') {
								const files = this.Event.F_Sketch.split(',');
								files.splice(index, 1);
								this.Event.F_Sketch = files.join(',');
							} else {
								const files = this.Event.F_ReportFile.split(',');
								files.splice(index, 1);
								this.Event.F_ReportFile = files.join(',');
							}
						}
					}
				});
			},
			openFile(file) {
				uni.downloadFile({
					url: file,
					success: (res) => {
						if (res.statusCode === 200) {
							uni.openDocument({
								filePath: res.tempFilePath,
								fileType: 'file',
								success: () => {
									console.log('文件打开成功')
								},
								fail: (err) => {
									console.error('打开文件失败', err)
								}
							})
						}
					},
					fail: (err) => {
						console.error('下载文件失败', err)
					}
				})
			},
			addMeasure() {
				if (!this.Event.F_MeasureGroup) {
					this.Event.F_MeasureGroup = [];
				}
				this.Event.F_MeasureGroup.push({ description: '', files: '' });
			},
			deleteMeasure(index) {
				this.Event.F_MeasureGroup.splice(index, 1);
			}
		}
	}
</script>

<style lang="less" scoped>
	.container{
		display: flex;
		flex-direction: column;
		gap: 20rpx;
		min-height: 100vh;
		padding-bottom: 320rpx;
		.info {
			background-color: white;
			.title{
				color: #7F7F7F;
				font-weight: 400;
				font-size: 28rpx;
				margin-bottom: 20rpx;
				background-color: rgba(242, 242, 242, 1);
				padding: 20rpx;
			}
			.content{
				padding: 20rpx;
				display: flex;
				flex-direction: column;
				gap: 20rpx;
				.item{
					display: flex;
					flex-direction: row;
					align-items: center;
					line-height: 66rpx;
					justify-content: space-between;
					border-bottom: 1rpx solid rgba(242, 242, 242, 1);
					.label{
						color: #666666;
						font-size: 28rpx;
					}
					.text{
						color: #666666;
						font-size: 28rpx;
						text-align: right;
					}
					.uni-input{
						text-align: right;
						line-height: 30rpx;
						padding-right: 0;
						color: #666666;
					}
					.photo{
						width: 200rpx;
						height: 160rpx;
					}
				}
			}
		}
		.staff-container{
			background-color: white;
			display: flex;
			flex-direction: column;
			.title{
				background-color: #7F7F7F;
				color: #333333;
				font-weight: 400;
				font-size: 28rpx;
				margin-bottom: 20rpx;
				padding: 20rpx;
			}
			.staff-list{
				padding: 20rpx;
				display: flex;
				flex-wrap: wrap;
				.staff-item{
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					width: 24%;
					image{
						width: 120rpx;
						height: 120rpx;
					}
					.label{
						color: #666666;
						font-size: 28rpx;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
					}
					.del{
						position: absolute;
						margin-bottom: 140rpx;
						margin-left: 128rpx;
						width: 50rpx;
					}
				}
			}
		}
		.botomm-btn {
			padding-bottom: 100rpx;
			height: 230rpx;
			position: fixed; /* 固定在屏幕底部 */
			bottom: 0;
			left: 0;
			width: 100%; /* 占满整个屏幕宽度 */
			background-color: #fff; /* 背景颜色，防止透明 */
			padding: 20rpx 0; /* 上下留白 */
			text-align: center; /* 让按钮居中 */
			align-items: center;
			display: flex;
			justify-content: center;
			box-shadow: 0 -10rpx 20rpx rgba(0, 0, 0, 0.1); /* 添加阴影，提升层次感 */
			z-index: 999; /* 确保层级较高 */
		}
		.file-list {
			display: flex;
			flex-wrap: wrap;
			gap: 20rpx;
			padding: 20rpx;
			background-color: #fff;
			
			.file-upload {
				width: 200rpx;
				height: 160rpx;
				border: 2rpx dashed #ddd;
				border-radius: 8rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				background-color: #f8f8f8;
				
				.upload-icon {
					font-size: 48rpx;
					color: #999;
					margin-bottom: 10rpx;
				}
				
				.upload-text {
					font-size: 24rpx;
					color: #999;
				}
			}
			
			.file-item {
				position: relative;
				width: 200rpx;
				height: 160rpx;
				
				image {
					width: 100%;
					height: 100%;
					border-radius: 8rpx;
					object-fit: cover;
				}
				
				.file-name {
					width: 100%;
					height: 100%;
					background-color: #f5f5f5;
					border-radius: 8rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					padding: 10rpx;
					
					text {
						font-size: 24rpx;
						color: #666666;
						text-align: center;
						word-break: break-all;
					}
				}
				
				.delete-btn {
					position: absolute;
					top: -20rpx;
					right: -20rpx;
					width: 40rpx;
					height: 40rpx;
					background-color: rgba(0, 0, 0, 0.5);
					color: #fff;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 32rpx;
				}
			}
		}
		.required {
			color: #ff0000;
			margin-left: 4rpx;
		}
		.measure-group-container {
			padding: 20rpx;
			background-color: white;
			.measure-item {
				border: 1rpx solid #eee;
				padding: 20rpx;
				margin-bottom: 20rpx;
				border-radius: 8rpx;
			}
			.measure-textarea {
				width: 100%;
				height: 150rpx;
				border: 1rpx solid #ddd;
				border-radius: 8rpx;
				padding: 10rpx;
				margin-bottom: 20rpx;
			}
			.delete-measure-btn {
				background-color: #f56c6c;
				color: white;
				font-size: 24rpx;
				margin-top: 20rpx;
			}
			.add-measure-btn {
				background-color: #00984a;
				color: white;
				font-size: 28rpx;
			}
			.file-list-in-measure {
				display: flex;
				flex-wrap: wrap;
				gap: 10rpx;
				.file-upload {
					width: 120rpx;
					height: 120rpx;
					border: 2rpx dashed #ddd;
					border-radius: 8rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					.upload-icon {
						font-size: 40rpx;
						color: #999;
					}
				}
				.file-item {
					position: relative;
					width: 120rpx;
					height: 120rpx;
					image {
						width: 100%;
						height: 100%;
						border-radius: 8rpx;
						object-fit: cover;
					}
					.file-name {
						width: 100%;
						height: 100%;
						background-color: #f5f5f5;
						border-radius: 8rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						padding: 10rpx;
						text {
							font-size: 24rpx;
							color: #666666;
							text-align: center;
							word-break: break-all;
						}
					}
					.delete-btn {
						position: absolute;
						top: -10rpx;
						right: -10rpx;
						width: 30rpx;
						height: 30rpx;
						background-color: rgba(0, 0, 0, 0.5);
						color: #fff;
						border-radius: 50%;
						display: flex;
						align-items: center;
						justify-content: center;
						font-size: 24rpx;
					}
				}
			}
		}
	}
</style>