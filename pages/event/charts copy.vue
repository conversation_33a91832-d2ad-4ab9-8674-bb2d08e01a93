<template>
	<view class="container">
		
		<view class="title">
			<image src="../../static/img/tabbar/tongjiHL.png"></image>
			<view class="sub-title">事故分析</view>
		</view>
		<view class="filters">
			<view class="item">
				<label>类型</label>
				<picker style="width: 240rpx;" mode="selector" @change="onMonthTypeChange" :value="MonthTypeIndex" :range="YearType">
					<view class="uni-input">{{YearType[MonthTypeIndex]}}</view>
				</picker>
			</view>
			<view class="item">
				<label>月份</label>
				<picker style="width: 240rpx;" mode="date" fields="month" @change="onMonthChange" :value="StartDate">
					<view class="uni-input">{{ StartDate }}</view>
				</picker>
			</view>
		</view>
		<view class="pie-charts">
			<canvas canvas-id="oJIDbmRKGqzKSwPDhJystIYqMBYohANg" id="oJIDbmRKGqzKSwPDhJystIYqMBYohANg" class="charts"/>
		</view>
		
		<view class="table">
		  <!-- 表头月份行 -->
		  <view class="table-row header">
		    <view class="cell month-cell">合计</view>
		    <view class="cell month-cell" v-for="month in months" :key="month">
		      {{month}}月
		    </view>
		  </view>
		  
		  <!-- 死亡行 -->
		  <view class="table-row">
		    <view class="cell month-cell">{{getRowSum(deathData)}}</view>
		    <view class="cell month-cell" v-for="(value, index) in deathData" :key="index">
		      {{value}}
		    </view>
		  </view>
		  
		  <view class="table-row">
		    <view class="cell month-cell">{{getRowSum(fatalityData)}}</view>
		    <view class="cell month-cell" v-for="(value, index) in fatalityData" :key="index">
		      {{value}}
		    </view>
		  </view>
		  
		  <view class="table-row">
		    <view class="cell month-cell">{{getRowSum(lostTimeData)}}</view>
		    <view class="cell month-cell" v-for="(value, index) in lostTimeData" :key="index">
		      {{value}}
		    </view>
		  </view>
		  
		  <view class="table-row">
		    <view class="cell month-cell">{{getRowSum(restrictedWorkData)}}</view>
		    <view class="cell month-cell" v-for="(value, index) in restrictedWorkData" :key="index">
		      {{value}}
		    </view>
		  </view>
		  
		  <view class="table-row">
		    <view class="cell month-cell">{{getRowSum(medicalData)}}</view>
		    <view class="cell month-cell" v-for="(value, index) in medicalData" :key="index">
		      {{value}}
		    </view>
		  </view>
		  
		  <view class="table-row">
		    <view class="cell month-cell">{{getRowSum(firstAidData)}}</view>
		    <view class="cell month-cell" v-for="(value, index) in firstAidData" :key="index">
		      {{value}}
		    </view>
		  </view>
		  
		  <view class="table-row">
		    <view class="cell month-cell">{{getRowSum(nearMissData)}}</view>
		    <view class="cell month-cell" v-for="(value, index) in nearMissData" :key="index">
		      {{value}}
		    </view>
		  </view>
		
		<view class="table-row">
		    <view class="cell month-cell">TRCT</view>
		    <view class="cell month-cell" v-for="(value, index) in trctData" :key="index">
		      {{value}}
		    </view>
		  </view>
		</view>
		
		
		<view class="title">
			<image src="../../static/img/tabbar/tongjiHL.png"></image>
			<view class="sub-title">事故统计</view>
		</view>
		<view class="filters">
			<view class="item">
				<label>类型</label>
				<picker mode="selector" @change="onYearTypeChange" :value="YearTypeIndex" :range="YearType">
					<view class="uni-input">{{YearType[YearTypeIndex]}}</view>
				</picker>
			</view>
			<view class="item">
				<label>年份</label>
				<picker mode="date" fields="year" @change="onBeginYearChange" :value="StartYearDate">
					<view class="uni-input">{{ StartYearDate }}</view>
				</picker>
				<view style="margin-left: 10rpx;margin-right: 10rpx;"> - </view>
				<picker mode="date" fields="year" @change="onEndYearChange" :start="StartYearDate" :value="EndYearDate">
					<view class="uni-input">{{ EndYearDate }}</view>
				</picker>
			</view>
		</view>
		<view class="pie-charts">
			<canvas canvas-id="PhXOmLiIUTXVkYwkUfQUCKRnXDhvhMOA" id="PhXOmLiIUTXVkYwkUfQUCKRnXDhvhMOA" class="charts"/>
		</view>
		
		
	</view>
</template>

<script>
	import { mapGetters } from 'vuex'
	import uCharts from '@/common/lib/u-charts.min.js'
	
	var _self;
	var funnelCharts = {};
	export default {
		components: {
		},
		computed: {
			...mapGetters(['themeBgColor', 'darkMode']),
		},
		data() {
			return {
				deathData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
				fatalityData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
				lostTimeData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
				restrictedWorkData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
				medicalData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
				firstAidData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
				nearMissData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
				trctData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
				YearTypeIndex: 0,
				MonthTypeIndex: 0,
				cWidth: 750,
				cHeight: 500,
				pixelRatio: 1,
				months:[1,2,3,4,5,6,7,8,9,10,11,12],
				YearType: ['Fiscal Year','Calendar Year'],
				funnelChartData:{
					series: [
					  {
						data: [
							{"name":"虚惊","centerText":"虚惊(0)","labelText":"","value":80},
							{"name":"急救","centerText":"急救(0)","labelText":"","value":60},
							{"name":"医疗处理","centerText":"医疗处理(0)","labelText":"","value":50},
							{"name":"工作受限","centerText":"工作受限(0)","labelText":"","value":40},
							{"name":"损工","centerText":"损工(0)","labelText":"","value":30},
							{"name":"致残","centerText":"致残(0)","labelText":"","value":20},
							{"name":"死亡","centerText":"死亡(0)","labelText":"","value":10}]
					  }
					]
				},
				columnChartData: {
					categories: ["2018","2019","2020","2021","2022","2023"],
					series: [
						{
							name: "虚惊",
							textColor: "#FFFFFF",
							data: [35,36,31,33,13,34]
						},
						{
							name: "急救",
							textColor: "#FFFFFF",
							data: [35,36,31,33,13,34]
						},
						{
							name: "医疗处理",
							textColor: "#FFFFFF",
							data: [18,27,21,24,6,28]
						},
						{
							name: "工作受限",
							textColor: "#FFFFFF",
							data: [35,36,31,33,13,34]
						},
						{
							name: "损工",
							textColor: "#FFFFFF",
							data: [18,27,21,24,6,28]
						},
						{
							name: "致残",
							textColor: "#FFFFFF",
							data: [35,36,31,33,13,34]
						},
						{
							name: "死亡",
							textColor: "#FFFFFF",
							data: [35,36,31,33,13,34]
						}
					]
				},
				StartDate: new Date().toISOString().slice(0, 7),
				StartYearDate: new Date().toISOString().slice(0,4),
				EndYearDate: new Date().toISOString().slice(0,4),
			}
		},
		mounted() {
			_self = this;
			//#ifdef MP-ALIPAY
			uni.getSystemInfo({
				success: function(res) {
					if (res.pixelRatio > 1) {
						//正常这里给2就行，如果pixelRatio=3性能会降低一点
						//_self.pixelRatio =res.pixelRatio;
						_self.pixelRatio = 2;
					}
				}
			});
			//#endif
			this.cWidth = uni.upx2px(750);
			this.cHeight = uni.upx2px(500);
			console.log('mouted')
			// this.loadPie();
			// this.drawFunnelCharts('oJIDbmRKGqzKSwPDhJystIYqMBYohANg',this.funnelChartData);
			// this.drawColumnCharts('PhXOmLiIUTXVkYwkUfQUCKRnXDhvhMOA', this.columnChartData);
			this.loadTableCharts();
			this.loadBarCharts();
		},
		methods:{
			loadBarCharts(){
				let yearType = 'Fiscal';
				if(this.YearTypeIndex == 1){
					yearType = 'Calendar';
				}
				this.$minApi.loadBarCharts({yearType:yearType,beginTime: this.StartYearDate,endTime: this.EndYearDate}).then(res=>{
					let yearsLoop = [];
					for(var start = this.StartYearDate;start<=this.EndYearDate;start++){
						yearsLoop.push(start);
					}
					// 定义固定的事故级别顺序
					const levels = ["虚惊", "急救", "医疗处理", "工作受限", "损工", "致残", "死亡"];
					
					
					// 如果返回数据为空，创建默认数据结构
					if (res.length === 0) {
						const result = levels.map(level => ({
							name: level,
							textColor: "#FFFFFF",
							data: yearsLoop.map(() => 0)  // 为每一年填充0
						}));
						
						const columnChartData = {
							categories: yearsLoop,
							series: result
						}
						this.drawColumnCharts('PhXOmLiIUTXVkYwkUfQUCKRnXDhvhMOA', columnChartData);
						return;
					}
					// 获取所有年份
					const years = [...new Set(res.map(item => item.Year))].sort();
					// 如果有数据，按原来的逻辑处理
					const result = levels.map(level => ({
						name: level,
						textColor: "#FFFFFF",
						data: years.map(year => {
							// 查找对应年份和级别的数据
							const found = res.find(item => 
								item.Year === year && item.Level === level
							);
							return found ? found.Count : 0;
						})
					}));
					
					const columnChartData = {
						categories: yearsLoop,
						series: result
					}
					
					console.log('ssss',columnChartData)
					this.drawColumnCharts('PhXOmLiIUTXVkYwkUfQUCKRnXDhvhMOA', columnChartData);
				})
			},
			loadTableCharts(){
				let yearType = 'Fiscal';
				if(this.MonthTypeIndex == 1){
					yearType = 'Calendar';
				}
				if(yearType == "Fiscal"){
					this.months = [4,5,6,7,8,9,10,11,12,1,2,3]
				}else{
					this.months = [1,2,3,4,5,6,7,8,9,10,11,12]
				}
				this.$minApi.loadTableCharts({yearType: yearType,month: this.StartDate}).then(res=>{
					// 处理数据
					setTimeout(()=>{
						// 初始化数组
						this.deathData = new Array(12).fill(0);
						this.fatalityData = new Array(12).fill(0);
						this.lostTimeData = new Array(12).fill(0);
						this.restrictedWorkData = new Array(12).fill(0);
						this.medicalData = new Array(12).fill(0);
						this.firstAidData = new Array(12).fill(0);
						this.nearMissData = new Array(12).fill(0);
						this.trctData = new Array(12).fill(0);
						res.forEach(item => {
						    // 月份索引需要减1，因为数组索引从0开始
						    const monthIndex = item.Month - 1;
						    
						    switch(item.Level) {
						        case "死亡":
						            this.$set(this.deathData, monthIndex, item.Count);
						            break;
						        case "致残":
						            this.$set(this.fatalityData, monthIndex, item.Count);
						            break;
						        case "损工":
						            this.$set(this.lostTimeData, monthIndex, item.Count);
						            break;
						        case "工作受限":
						            this.$set(this.restrictedWorkData, monthIndex, item.Count);
						            break;
						        case "医疗处理":
						            this.$set(this.medicalData, monthIndex, item.Count);
						            break;
						        case "急救":
						            this.$set(this.firstAidData, monthIndex, item.Count);
						            break;
						        case "虚惊":
						            this.$set(this.nearMissData, monthIndex, item.Count);
						            break;
								case "trct":
									this.$set(this.trctData, monthIndex, item.Count);
									break;
						    }
						});
						console.log('this.firstAidData',this.firstAidData)
					},500)
					
					const summary = {
					        "死亡": 0,
					        "致残": 0,
					        "损工": 0,
					        "工作受限": 0,
					        "医疗处理": 0,
					        "急救": 0,
					        "虚惊": 0
					    };
					
					    // 统计各级别的总数
					    res.forEach(item => {
					        if (summary.hasOwnProperty(item.Level)) {
					            summary[item.Level] += item.Count;
					        }
					    });
					    // 转换为列表格式
					    const summaryList = [
					        { name: "虚惊",centerText: ""+summary["虚惊"],labelText: "虚惊", value: 70 },
							{ name: "急救",centerText: ""+summary["急救"],labelText: "急救", value: 60 },
							{ name: "医疗处理",centerText: ""+summary["医疗处理"],labelText: "医疗处理", value: 50 },
							{ name: "工作受限",centerText: ""+summary["工作受限"],labelText: "工作受限", value: 40 },
							{ name: "损工",centerText: ""+summary["损工"],labelText: "损工", value: 30 },
					        { name: "致残",centerText: ""+summary["致残"],labelText: "致残", value: 20 },
					        { name: "死亡",centerText: ""+summary["死亡"],labelText: "死亡", value: 10 }
					    ];
						// this.funnelChartData.series[0].data.push(summaryList);
						const funnelChartData = {
							series: [
							  {
								data: summaryList
							  }
							]
						}
						setTimeout(() => {
							this.drawFunnelCharts('oJIDbmRKGqzKSwPDhJystIYqMBYohANg',funnelChartData);
						},500);
				})
			},
			getRowSum(data) {
			  return data.reduce((sum, current) => sum + current, 0)
			},
			onMonthChange(e) {
				this.StartDate = e.detail.value;
				this.loadTableCharts();
			},
			onYearTypeChange(e){
				this.YearTypeIndex = e.detail.value;
				this.loadBarCharts();
			},
			onMonthTypeChange(e){
				this.MonthTypeIndex = e.detail.value;
				this.loadTableCharts();
			},
			onBeginYearChange(e){
				this.StartYearDate = e.detail.value;
				this.loadBarCharts();
			},
			onEndYearChange(e){
				this.EndYearDate = e.detail.value;
				this.loadBarCharts();
			},
			drawFunnelCharts(id,data){
				const ctx = uni.createCanvasContext(id, this);
				funnelCharts[id] = new uCharts({
					type: "funnel",
					context: ctx,
					width: _self.cWidth * _self.pixelRatio,
					height: _self.cHeight * _self.pixelRatio,
					series: data.series,
					animation: true,
					background: "#FFFFFF",
					color: ["#31849b","#4472C4","#FFD966","#ED7D31","#FF0000","#C00000","#A5A5A5"],
					padding: [15,15,0,15],
					dataLabel: true,
					enableScroll: false,
					extra: {
					  funnel: {
						activeOpacity: 0.3,
						activeWidth: 10,
						border: true,
						borderWidth: 2,
						borderColor: "#FFFFFF",
						fillOpacity: 1,
						labelAlign: "left",
						type: "pyramid"
					  }
					}
				});
			},
			drawColumnCharts(id,data){
			  const ctx = uni.createCanvasContext(id, this);
			  funnelCharts[id] = new uCharts({
				type: "column",
				context: ctx,
				width: _self.cWidth * _self.pixelRatio,
				height: _self.cHeight * _self.pixelRatio,
				categories: data.categories,
				series: data.series,
				animation: true,
				background: "#FFFFFF",
				color: ["#31849b","#4472C4","#FFD966","#ED7D31","#FF0000","#C00000","#A5A5A5"],
				padding: [15,15,0,5],
				enableScroll: false,
				legend: {},
				xAxis: {
				  disableGrid: true
				},
				yAxis: {
				  data: [
					{
					  min: 0
					}
				  ]
				},
				extra: {
				  column: {
					type: "stack",
					width: 30,
					activeBgColor: "#000000",
					activeBgOpacity: 0.08,
					labelPosition: "center"
				  }
				}
			  });
			},
		}
	}
</script>

<style lang="scss" scoped>
	.container{
		background-color: white;
		padding: 10rpx;
		display: flex;
		flex-direction: column;
		gap: 10rpx;
		.title{
			display: flex;
			gap: 6rpx;
			align-items: center;
			padding: 10rpx;
			border-bottom: 1rpx solid #dddddd;
			image{
				width: 40rpx;
				height: 40rpx;
			}
			.sub-title{
				font-size: 30rpx;
				font-weight: 650;
				color: #666666;
			}
		}
		.filters{
			display: flex;
			flex-direction: row;
			align-items: center;
			background-color: white;
			padding: 20rpx;
			width: 100%;
			flex-wrap: wrap;
			gap: 10rpx;
			.item{
				display: flex;
				flex-direction: row;
				align-items: center;
				flex: 1;
				label{
					color: #4c4c4c;
					// width: 20%;
					padding: 10rpx;
					align-items: center;
					text-align: center;
					line-height: 20rpx;
				}
				.uni-input{
					color: #7c7c7c;
					border: 1px solid #00984a;
					align-items: center;
					text-align: left;
					width: 36vw;
					border-radius: 10rpx !important;
					line-height: 20rpx;
				}
			}
		}
		.table {
		  // width: 99%;
		  // border: 1rpx solid #ddd;
		  // background: #fff;
		  transform: scale(0.95); /* 整体缩小以适应屏幕 */
		  transform-origin: top left;
		  padding-bottom: 60rpx;
		}
		
		.table-row {
		  display: flex;
		  border-bottom: 1rpx solid #ddd;
		}
		
		.cell {
		  display: flex;
		  align-items: center;
		  justify-content: center;
		  padding: 12rpx 4rpx; /* 减小内边距 */
		  font-size: 20rpx; /* 减小字体大小 */
		  border-right: 1rpx solid #ddd;
			border-bottom: 1rpx solid #ddd;
		  box-sizing: border-box;
		  margin-right: -1rpx; /* 抵消右边框 */
			margin-bottom: -1rpx; /* 抵消下边框 */
		}
		
		.month-cell {
		  flex: 1;
		  min-width: 8.2%; /* 设置最小宽度 */
		}
		
		/* 每一行的背景色样式 */
		.death .cell {
		  background-color: #a5a5a5;
		  color: white;
		}
		
		.fatality .cell {
		  background-color: #c00000;
		  color: white;
		}
		
		.lost-time .cell {
		  background-color: #ff0000;
		  color: white;
		}
		
		.restricted-work .cell {
		  background-color: #ffa514;
		  color: white;
		}
		
		.medical .cell {
		  background-color: #ffff00;
		}
		
		.first-aid .cell {
		  background-color: #0070c0;
		  color: white;
		}
		
		.near-miss .cell {
		  background-color: #31849b;
		  color: white;
		}
		
		.header .cell {
		  background-color: #f5f5f5;
		  font-weight: bold;
		}
	}
	.charts{
	    width: 750rpx;
	    height: 500rpx;
	  }
</style>