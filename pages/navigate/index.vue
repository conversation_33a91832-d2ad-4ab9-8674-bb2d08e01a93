<template>
	<view class="container">
		<view class="navs">
			<view class="nav" v-if="type=='cheng' && $checkMenuAuth('cbstzgl')">
				<navigator url="/pages/contractor/list" open-type="navigate" class="navbar">
					<image class="icon" src="/static/img/tabbar/image1.png" mode="aspectFit"></image>
					<view class="text">承包商管理台账</view>
					<image src="/static/img/tabbar/right_arrow.svg" class="arrow" alt=""></image>
				</navigator>
			</view>
			<view class="nav" v-if="type=='cheng' && $checkMenuAuth('cbszytzgl')">
				<navigator url="/pages/task/list" open-type="navigate" class="navbar">
					<image class="icon" src="/static/img/tabbar/image2.png" mode="aspectFit"></image>
					<view class="text">承包商作业台账</view>
					<image src="/static/img/tabbar/right_arrow.svg" class="arrow" alt=""></image>
				</navigator>
			</view>
			<view class="nav" v-if="type=='cheng' && $checkMenuAuth('cbszytjgl')">
				<navigator url="/pages/task/charts" open-type="navigate" class="navbar">
					<image class="icon" src="/static/img/tabbar/image3.png" mode="aspectFit"></image>
					<view class="text">承包商作业统计</view>
					<image src="/static/img/tabbar/right_arrow.svg" class="arrow" alt=""></image>
				</navigator>
			</view>
			
			<view class="nav" v-if="type=='shigu' && $checkMenuAuth('sgtz')">
				<navigator url="/pages/event/list" open-type="navigate" class="navbar">
					<image class="icon" src="/static/img/tabbar/image1.png" mode="aspectFit"></image>
					<view class="text">事故台账</view>
					<image src="/static/img/tabbar/right_arrow.svg" class="arrow" alt=""></image>
				</navigator>
			</view>
			<view class="nav" v-if="type=='shigu' && $checkMenuAuth('sgtj')">
				<navigator url="/pages/event/analysis" open-type="navigate" class="navbar">
					<image class="icon" src="/static/img/tabbar/image2.png" mode="aspectFit"></image>
					<view class="text">事故统计</view>
					<image src="/static/img/tabbar/right_arrow.svg" class="arrow" alt=""></image>
				</navigator>
			</view>
			<!-- <view class="nav" v-if="type=='flow'">
				<navigator url="/pages/flow/change-ledger" open-type="navigate" class="navbar">
					<image class="icon" src="/static/img/home/<USER>" mode="aspectFit"></image>
					<view class="text">变更审批</view>
					<image src="/static/img/tabbar/right_arrow.svg" class="arrow" alt=""></image>
				</navigator>
			</view> -->
			<view class="nav" v-if="type=='flow'">
				<navigator url="/pages/flow/chemical-ledger" open-type="navigate" class="navbar">
					<image class="icon" src="/static/img/home/<USER>" mode="aspectFit"></image>
					<view class="text">化学品审批</view>
					<image src="/static/img/tabbar/right_arrow.svg" class="arrow" alt=""></image>
				</navigator>
			</view>
			<view class="nav" v-if="type=='flow'">
				<navigator url="/pages/flow/external-chemical-ledger" open-type="navigate" class="navbar">
					<image class="icon" src="/static/img/home/<USER>" mode="aspectFit"></image>
					<view class="text">外部持入审批</view>
					<image src="/static/img/tabbar/right_arrow.svg" class="arrow" alt=""></image>
				</navigator>
			</view>
			<!-- <view class="nav" v-if="type=='flow'">
				<navigator url="/pages/flow/holiday-work-ledger" open-type="navigate" class="navbar">
					<image class="icon" src="/static/img/home/<USER>" mode="aspectFit"></image>
					<view class="text">双休日审批</view>
					<image src="/static/img/tabbar/right_arrow.svg" class="arrow" alt=""></image>
				</navigator>
			</view> -->
		</view>
	</view>
</template>

<script>
	import { mapGetters } from 'vuex'
	import globalConfig from '@/config'
	export default{
		computed: {
			...mapGetters(['themeBgColor', 'darkMode']),
		},
		data(){
			return{
				type: 'cheng',
				baseUrl: ''
			}
		},
		onLoad(options){
			console.log('sssss',options)
			this.baseUrl = globalConfig.baseUrl.replace('/api', '');
			this.type = options.type
		},
		onReady() {
			this.setNavBarColor()
		},
		onShow() {
			this.setNavBarColor()
		},
		onShareAppMessage() {
		    return {
		      title: 'EHS-掌上观纹', // 自定义分享标题
		      path: '/pages/start/index', // 分享页面路径（默认当前页）
		      imageUrl: this.baseUrl+'/images/nideke.jpg', // 分享图片（可选）
		      success(res) {
		        console.log('分享成功', res);
		      },
		      fail(err) {
		        console.error('分享失败', err);
		      }
		    };
		},
		methods:{
			setNavBarColor() {
				// navBar-bg-color
				uni.setNavigationBarColor({
				    frontColor: '#000000',
				    backgroundColor: this.themeBgColor,
				    animation: {
				        duration: 400,
				        timingFunc: 'easeIn'
				    }
				})
			},
		}
	}
</script>

<style lang="scss">
	.container{
		background-color: rgba(249, 249, 249, 1);
		height: 100vh;
		padding: 20rpx;
		.navs{
			display: flex;
			flex-direction: column;
			gap: 20rpx;
			.nav{
				box-shadow: 0rpx 0rpx 20rpx rgba(0, 0, 0, 0.047058823529411764);
				background-color: white;
				border-radius: 10rpx;
				padding-left: 20rpx;
				.navbar{
					display: flex;
					flex-direction: row;
					gap: 10rpx;
					height: 100rpx;
					align-items: center;
					.icon{
						width: 70rpx;
						height: 70rpx;
					}
					.text{
						width: 75vw;
					}
					.arrow{
						width: 20rpx;
						height: 30rpx;
					}
				}
				
				
			}
		}
	}
</style>