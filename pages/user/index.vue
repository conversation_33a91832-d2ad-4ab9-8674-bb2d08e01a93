<template>
	<view class="container">
		<uni-nav-bar :fixed="true" color="#333333" :background-color="themeBgColor" :border="false">
			<view class="input-view">
				<uni-icons type="search" size="22" color="#666666" />
				<input v-model="searchVal" confirm-type="search" class="input" type="text" placeholder="搜索" @confirm="search">
				<uni-icons :color="'#999999'" v-if="searchVal!==''" class="icon-clear" type="clear" size="22" @click="clear" />
			</view>
		</uni-nav-bar>
		<view class="card-list">
			<view class="card" v-for="item in UserList" :key="item.F_Id">
				<image :src="item.F_HeadIcon ? (baseUrl+item.F_HeadIcon) : '/static/img/profile.svg'"></image>
				<view class="content">
					<view class="name">{{ item.F_RealName }}</view>
					<view class="signature">{{ item.F_OrganizeName}}</view>
				</view>
				<view class="role1">
					<!-- <view @click="navigateTo('/pages/user/info?id='+item.F_id)">编辑</view> -->
					<view class="edit-btn" @click="navigateTo('/pages/user/info?id=' + item.F_Id)">编辑</view>
					
					<!-- <view class="edit-btn" @click="navigateTo('/pages/user/info?id=' + item.F_Id)">重置密码</view> -->
					<!-- <picker mode="selector" @change="onRoleChange($event,item)" :disabled="!$checkBtnAuth('yhjsfp')" range-key="F_FullName" :range="RoleList">
						<view class="role">
							<view class="uni-input">{{item.F_RoleName?item.F_RoleName:"分配角色"}}</view>
							<image src="../../static/img/down_arrow.svg"></image>
						</view>
					</picker> -->
				</view>
			</view>
		</view>
		<!-- <view class="botomm-btn" v-if="$checkBtnAuth('sgtzxz')"> -->
		<view class="botomm-btn">
			<navigator url="/pages/user/info" open-type="navigate">
				<view class="btn">新增成员</view>
			</navigator>
		</view>
	</view>
</template>

<script>
	import { mapGetters } from 'vuex'
	import uniIcons from '@/components/uni-icons/uni-icons.vue'
	import uniNavBar from '@/components/uni-nav-bar/uni-nav-bar.vue'
	import globalConfig from '@/config'
	export default {
		components: {
			uniIcons,
			uniNavBar
		},
		computed: {
			...mapGetters(['user','themeBgColor', 'darkMode','user']),
		},
		data() {
			return {
				baseUrl: "",
				searchVal: "",
				// 窗口高度
				winHeight: "",
				RoleList:[],
				UserList:[],
				totalUser: 0,
				queryParams :{
					page: 1,
					rows: 10,
					keyword : ''
				},
				value: 1,
				total: 0,
				loading: false,
				isEnd: false // 是否已加载全部数据
			}
		},
		onReady() {
			uni.setNavigationBarTitle({
			    title: this.$t('权限管理')
			})
			this.setNavBarColor()
		},
		onShow() {
			this.setNavBarColor()
			this.this.loadData()
		},
		onLoad() {
			//  高度自适应
			uni.getSystemInfo({
				success: res => {
					this.winHeight = res.windowHeight
				}
			})
			this.loadRole()
			this.loadData()
			this.baseUrl = globalConfig.baseUrl.replace("/api","");
		},
		// 监听页面滚动到底部
		onReachBottom() {
			if (!this.loading && !this.isEnd) {
				this.queryParams.page++
				this.loadData()
			}
		},
		onShareAppMessage() {
		    return {
		      title: 'EHS-掌上观纹', // 自定义分享标题
		      path: '/pages/start/index', // 分享页面路径（默认当前页）
		      imageUrl: this.baseUrl+'/images/nideke.jpg', // 分享图片（可选）
		      success(res) {
		        console.log('分享成功', res);
		      },
		      fail(err) {
		        console.error('分享失败', err);
		      }
		    };
		},
		methods: {
			checkPermission(){
				if(this.user.F_RoleId === "08dc7a3d-e5a8-44cf-87ea-15347b9fa336"){
					return false;
				}else{
					return true;
				}
			},
			loadRole(){
				let that = this;
				this.$minApi.listRole({keyword: ''}).then(res=>{
					if(res.state == 'success'){
						that.RoleList = res.data;
					}
				})
			},
			loadData() {
				if (this.loading || this.isEnd) return
				this.loading = true
				
				uni.showLoading({
					title: '加载中...'
				})
				
				this.$minApi.listUser(this.queryParams).then(res => {
					if (res.state === 0) {
						if (this.queryParams.page === 1) {
							this.UserList = res.data
						} else {
							this.UserList = [...this.UserList, ...res.data]
						}
						this.total = res.total || this.UserList.length
						// 判断是否加载完全部数据
						if (res.data.length < this.queryParams.rows) {
							this.isEnd = true
						}
					} else {
						uni.showToast({
							title: res.message || '加载失败',
							icon: 'none'
						})
					}
				}).catch(() => {
					uni.showToast({
						title: '加载失败',
						icon: 'none'
					})
				}).finally(() => {
					uni.hideLoading()
					this.loading = false
				})
			},
			onRoleChange(e,item){
				const selectedIndex = e.detail.value;
				if (selectedIndex >= 0 && selectedIndex < this.RoleList.length) {
					const selectedRole = this.RoleList[selectedIndex];
					item.F_RoleName = selectedRole?.F_FullName;
					item.F_RoleId = selectedRole?.F_Id;
					this.$minApi.settingRole(item).then(res=>{
						if(res.state == 'success'){
							uni.showToast({
								title: '设置成功',
								icon: 'success'
							})
						}else{
							uni.showToast({
								title: '设置失败',
								icon: 'error'
							})
						}
					})
				} else {
					console.log('无效的索引');
				}
			},
			setNavBarColor() {
				// navBar-bg-color
				uni.setNavigationBarColor({
				    frontColor: '#000000',
				    backgroundColor: this.themeBgColor,
				    animation: {
				        duration: 400,
				        timingFunc: 'easeIn'
				    }
				})
			},
			search() {
				this.queryParams.page = 1
				this.isEnd = false
				this.UserList = []
				this.queryParams.keyword = this.searchVal
				this.loadData()
			},
			clear() {
				this.searchVal = ''
				this.search()
			},
			navigateTo(url) {
				uni.navigateTo({
					url: url
				});
			}
		}
	}
</script>

<style lang="less">

	.container{
		position: relative;
		height: 100vh;
		background-color: white;
		.card-list{
			display: flex;
			flex-direction: column;
			align-items: left;
			justify-content: start;
			width: 100vw;
			padding: 30rpx;
			padding-bottom: 160rpx;
			gap: 20rpx;
			.title{
				font-size: 32rpx;
				font-weight: 400;
				color: #333333;
			}
			.card{
				color: #999999;
				border-bottom: 1rpx solid rgba(242, 242, 242, 1);
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: center;
				width: 100%;
				background-color: white;
				border-radius: 10rpx;
				
				image{
					width: 140rpx;
					height: 140rpx;
					border-radius: 50%;
					object-fit: cover;
				}
				.content{
					padding: 20rpx;
					width: 80%;
					display: flex;
					flex-direction: column;
					align-items: left;
					.name{
						color: #333333;
						font-size: 32rpx;
						font-weight: 400;
						margin-bottom: 20rpx;
					}
					.signature{
						color: #AAAAAA;
						font-size: 24rpx;
						font-weight: 400;
						margin-bottom: 20rpx;
					}
				}
				.role {
					color: #3370FF;
					font-size: 20rpx;
					text-align: center;
					border: 1rpx solid #3370FF;
					width: 160rpx;
					padding: 5rpx 2rpx 5rpx 8rpx;
					display: flex;
					display: flex;
					justify-content: center;
					align-items: center;
					border-radius: 8rpx;
					image{
						width: 40rpx;
						height: 40rpx;
					}
					.uni-input{
						font-size: 20rpx;
						padding: 0rpx;
					}
				}
				.edit-btn{
					border-radius: 20rpx;
					background: #37994C;
					width: 100rpx;
					text-align: center;
					color: #FFFFFF;
				}
			}
		}
		.botomm-btn {
			position: fixed;
			bottom: 0;
			left: 0;
			width: 100%;
			background-color: #fff;
			padding: 20rpx 0;
			padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
			padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
			display: flex;
			justify-content: center;
			align-items: center;
			box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
			z-index: 999;
		}
		.btn {
			width: 686rpx;
			height: 80rpx;
			background: linear-gradient(90deg, #00984a 0%, #00984a 100%);
			border-radius: 40rpx;
			line-height: 80rpx;
			text-align: center;
			color: #FFFFFF;
			font-size: 32rpx;
		}
	}
</style>