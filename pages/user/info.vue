<template>
	<view class="page-container">
		<!-- 表单内容区域 -->
		<view class="form-content">
			<!-- 员工信息 -->
			<view class="info-section">
				<view class="section-title">员工信息</view>
				<view class="form-card">
					<!-- 姓名 -->
					<view class="form-row">
						<view class="label"><text class="required">*</text>姓名</view>
						<view class="value-area">
							<input type="text" v-model="formData.name" placeholder="请输入姓名" class="form-input" placeholder-class="placeholder" />
						</view>
					</view>
					<!-- 角色 -->
					<picker mode="selector" :range="roleList" range-key="F_FullName" @change="onRoleChange">
						<view class="form-row">
							<view class="label"><text class="required">*</text>角色</view>
							<view class="value-area picker">
								<text :class="{'placeholder': !formData.roleName}">{{ formData.roleName || '请选择角色' }}</text>
								<uni-icons type="right" size="16" color="#999"></uni-icons>
							</view>
						</view>
					</picker>
					<!-- 部门 -->
					<picker mode="selector" :range="departmentList" range-key="F_FullName" @change="onDepartmentChange">
						<view class="form-row">
							<view class="label"><text class="required">*</text>部门</view>
							<view class="value-area picker">
								<text :class="{'placeholder': !formData.departmentName}">{{ formData.departmentName || '请选择部门' }}</text>
								<uni-icons type="right" size="16" color="#999"></uni-icons>
							</view>
						</view>
					</picker>
				</view>
			</view>

			<!-- 账号信息 -->
			<view class="info-section">
				<view class="section-title">账号信息</view>
				<view class="form-card">
					<!-- 手机号 -->
					<view class="form-row">
						<view class="label"><text class="required">*</text>手机号</view>
						<view class="value-area">
							<input type="number" v-model="formData.phone" placeholder="请输入手机号码" class="form-input" placeholder-class="placeholder" maxlength="11" />
						</view>
					</view>
					<!-- 邮箱 -->
					<view class="form-row">
						<view class="label"><text class="required">*</text>邮箱</view>
						<view class="value-area">
							<input type="text" v-model="formData.email" placeholder="请输入邮箱" class="form-input" placeholder-class="placeholder" />
						</view>
					</view>
					<!-- 账号 -->
					<view class="form-row">
						<view class="label"><text class="required">*</text>账号</view>
						<view class="value-area">
							<input type="text" v-model="formData.account" placeholder="请输入账号" class="form-input" placeholder-class="placeholder" />
						</view>
					</view>
					<!-- 密码 -->
					<!-- <view class="form-row with-helper">
						<view class="label"><text class="required">*</text>密码</view>
						<view class="value-area">
							<input type="password" v-model="formData.password" placeholder="请输入密码" class="form-input" placeholder-class="placeholder" />
						</view>
					</view>
					<view class="helper-text">默认密码为123456</view> -->
				</view>
			</view>

			<!-- 状态 -->
			<view class="info-section">
				<view class="form-card">
					<view class="form-row">
						<view class="label">状态</view>
						<view class="value-area">
							<text class="switch-label" :class="{'active': formData.status}">{{ formData.status ? '启动' : '停用' }}</text>
							<switch :checked="formData.status" @change="onStatusChange" color="#28A745" style="transform:scale(0.8)" />
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部操作栏 -->
		<view class="bottom-bar">
			<view class="button-wrapper">
				<button v-if="isEditMode" class="btn delete-btn" @click="handleDelete">删除</button>
				<button class="btn save-btn" @click="handleSave">保存</button>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 判断是否为编辑模式
				isEditMode: false,
				// 角色和部门列表
				roleList: [],
				departmentList: [],
				// 表单数据
				formData: {
					id: null,
					name: '',
					roleId: null,
					roleName: '',
					departmentId: null,
					departmentName: '',
					phone: '',
					email: '',
					account: '',
					password: '',
					status: true, // true: 启动, false: 停用
				}
			};
		},
		onReady() {
			this.setNavBarColor()
		},
		onShow() {
			this.setNavBarColor()
		},
		onLoad(options) {
			// 加载基础数据（角色、部门），然后根据模式（新增/编辑）执行后续操作
			this.loadInitialData().then(() => {
				if (options.id) {
					this.isEditMode = true;
					uni.setNavigationBarTitle({ title: '编辑员工' });
					this.fetchEmployeeData(options.id);
				} else {
					this.isEditMode = false;
					uni.setNavigationBarTitle({ title: '新增员工' });
				}
			});
		},
		methods: {
			// 在页面加载时获取所有必需的列表数据
			async loadInitialData() {
				uni.showLoading({ title: '加载基础数据...' });
				try {
					// 并行获取角色和部门列表
					const [roleRes, deptRes] = await Promise.all([
						this.$minApi.listRole(),
						this.$minApi.getOrgList()
					]);
			
					if (roleRes.state === 'success') {
						this.roleList = roleRes.data;
					} else {
						uni.showToast({ title: '角色列表加载失败', icon: 'none' });
					}
			
					// 部门列表直接返回数组，没有外层包装
					if (Array.isArray(deptRes)) {
						this.departmentList = deptRes;
					} else {
						uni.showToast({ title: '部门列表加载失败', icon: 'none' });
					}
				} catch (error) {
					console.error("Initial data loading failed:", error);
					uni.showToast({ title: '基础数据加载异常', icon: 'none' });
				} finally {
					uni.hideLoading();
				}
			},
			
			// 获取员工数据并回显
			fetchEmployeeData(id) {
				uni.showLoading({ title: '加载中...' });
				this.$minApi.getUser(id).then(res => {
					uni.hideLoading();
					let userData;
					if (res.state === 'success' && typeof res.data === 'string') {
						userData = JSON.parse(res.data);
					} else {
						userData = res.data;
					}
			
					// 根据ID在预加载的列表中查找并设置名称
					const role = this.roleList.find(r => r.F_Id === userData.roleId);
					if (role) {
						userData.roleName = role.F_FullName;
					}
			
					const department = this.departmentList.find(d => d.F_Id === userData.departmentId);
					if (department) {
						userData.departmentName = department.F_FullName;
					}
			
					this.formData = userData;
					// 编辑时，密码框通常是空的，除非用户想修改
					this.formData.password = '';
				}).catch(() => {
					uni.hideLoading();
					uni.showToast({
						title: '加载失败',
						icon: 'none'
					});
				});
			},

			// 角色选择器变化
			onRoleChange(e) {
				const selectedIndex = e.detail.value;
				const selectedRole = this.roleList[selectedIndex];
				if (selectedRole) {
					this.formData.roleId = selectedRole.F_Id;
					this.formData.roleName = selectedRole.F_FullName;
				}
			},
			
			// 部门选择器变化
			onDepartmentChange(e) {
				const selectedIndex = e.detail.value;
				const selectedDepartment = this.departmentList[selectedIndex];
				if (selectedDepartment) {
					this.formData.departmentId = selectedDepartment.F_Id;
					this.formData.departmentName = selectedDepartment.F_FullName;
				}
			},

			// 状态开关变化
			onStatusChange(e) {
				this.formData.status = e.detail.value;
			},
			
			// 校验表单
			validateForm() {
				const rules = {
					name: '姓名',
					roleName: '角色',
					departmentName: '部门',
					phone: '手机号',
					email: '邮箱',
					account: '账号',
				};
				// 新增时密码必填
				if (!this.isEditMode) {
					rules.password = '密码';
				}
				
				for (const key in rules) {
					if (!this.formData[key]) {
						uni.showToast({
							title: `${rules[key]}不能为空`,
							icon: 'none'
						});
						return false;
					}
				}
				// 手机号和邮箱格式校验
				if (!/^1[3-9]\d{9}$/.test(this.formData.phone)) {
					uni.showToast({ title: '手机号格式不正确', icon: 'none' });
					return false;
				}
				if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(this.formData.email)) {
					uni.showToast({ title: '邮箱格式不正确', icon: 'none' });
					return false;
				}
				return true;
			},

			// 点击保存
			handleSave() {
				if (!this.validateForm()) {
					return;
				}
				uni.showLoading({ title: '保存中...' });
				const api = this.isEditMode ? this.$minApi.updateUser : this.$minApi.addUser;
				api(this.formData).then(() => {
					uni.hideLoading();
					uni.showToast({
						title: '保存成功',
						icon: 'success'
					});
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				}).catch(() => {
					uni.hideLoading();
					uni.showToast({
						title: '保存失败',
						icon: 'none'
					});
				});
			},

			// 点击删除
			handleDelete() {
				uni.showModal({
					title: '确认删除',
					content: '您确定要删除该员工吗？此操作不可撤销。',
					success: (res) => {
						if (res.confirm) {
							uni.showLoading({ title: '删除中...' });
							this.$minApi.deleteUser(this.formData.id).then(() => {
								uni.hideLoading();
								uni.showToast({
									title: '删除成功',
									icon: 'success'
								});
								setTimeout(() => {
									uni.navigateBack();
								}, 1500);
							}).catch(() => {
								uni.hideLoading();
								uni.showToast({
									title: '删除失败',
									icon: 'none'
								});
							});
						}
					}
				});
			},
			setNavBarColor() {
				// navBar-bg-color
				uni.setNavigationBarColor({
				    frontColor: '#000000',
				    backgroundColor: '#F6F7FC',
					// backgroundColor: '#FFFFFF',
				    animation: {
				        duration: 400,
				        timingFunc: 'easeIn'
				    }
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.page-container {
		background-color: #f5f6fa;
		min-height: 100vh;
		padding-bottom: calc(180rpx + constant(safe-area-inset-bottom));
		box-sizing: border-box;
	}

	.form-content {
		padding: 24rpx;
	}

	.info-section {
		margin-bottom: 24rpx;
	}

	.section-title {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 16rpx;
		padding-left: 10rpx;
	}

	.form-card {
		background-color: #fff;
		border-radius: 16rpx;
		padding: 0 32rpx;
	}

	.form-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		min-height: 100rpx;
		border-bottom: 1rpx solid #f0f0f0;

		// 移除最后一个元素的下边框
		&:last-child {
			border-bottom: none;
		}
		
		// 带有辅助文本的行，减少下边距
		&.with-helper {
			padding-bottom: 10rpx;
		}
	}

	.label {
		font-size: 30rpx;
		color: #333;
		white-space: nowrap; // 防止标签换行
		.required {
			color: #ff4d4f;
			margin-right: 4rpx;
		}
	}

	.value-area {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		flex: 1;
		min-width: 0; // 防止flex item内容溢出
		.form-input {
			text-align: right;
			font-size: 30rpx;
			color: #333;
			width: 100%;
		}
		.placeholder {
			color: #999;
			font-size: 30rpx;
		}
		&.picker {
			color: #333;
			font-size: 30rpx;
			.uni-icons {
				margin-left: 8rpx;
			}
		}
	}
	
	.helper-text {
		font-size: 24rpx;
		color: #999;
		padding-bottom: 20rpx;
		text-align: right;
	}

	.switch-label {
		font-size: 30rpx;
		color: #999;
		margin-right: 20rpx;
		&.active {
			color: #28a745;
		}
	}
	
	// 底部操作栏
	.bottom-bar {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		background-color: #fff;
		padding: 20rpx 32rpx;
		padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
		box-sizing: border-box;

		.button-wrapper {
			display: flex;
			gap: 24rpx;
			.btn {
				flex: 1;
				height: 88rpx;
				line-height: 88rpx;
				border-radius: 44rpx;
				font-size: 32rpx;
				margin: 0;
				&::after {
					border: none;
				}
			}
			.save-btn {
				background-color: #28a745;
				color: #fff;
			}
			.delete-btn {
				background-color: #fff;
				color: #e64340;
				border: 1rpx solid #e64340;
			}
		}
	}
</style>