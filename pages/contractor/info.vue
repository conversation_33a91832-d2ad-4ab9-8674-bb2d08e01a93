<template>
	<view class="container">
		<view class="header">
			<view class="title">{{ ContractorInfo.F_CompanyName }}</view>
			<view class="detail">
				<view class="num">公司编号：{{ ContractorInfo.F_Num }}</view>
				<view class="status" :class="{'status-green': ContractorInfo.F_EnabledMark, 'status-red': !ContractorInfo.F_EnabledMark}">
					{{ ContractorInfo.F_EnabledMark ? '有效' : '无效' }}
				</view>
			</view>
		</view>
		<view class="info">
			<view class="title">基本信息</view>
			<view class="content">
				<view class="item">
					<label class="label">公司地址</label>
					<view class="text">{{ ContractorInfo.F_Address }}</view>
				</view>
				<view class="item">
					<label class="label">承包商种类</label>
					<view class="text">{{ ContractorInfo.F_TypeName == 'DailyType' ? '日常性承包商' : '间断性承包商' }}</view>
				</view>
				<view class="item">
					<label class="label">承包商负责人</label>
					<view class="text">{{ ContractorInfo.F_HeadName }}</view>
				</view>
				<view class="item">
					<label class="label">承包商内部联系人</label>
					<view class="text">{{ ContractorInfo.F_ContactsName }}</view>
				</view>
				<view class="item">
					<label class="label">提供的服务类型</label>
					<view class="text">{{ ContractorInfo.F_ServeId }}</view>
				</view>
				<view class="item">
					<label class="label">签署日期</label>
					<view class="text">{{ ContractorInfo.F_StartTime }}</view>
				</view>
				<view class="item">
					<label class="label">过期日期</label>
					<view class="text">{{ ContractorInfo.F_EndTime }}</view>
				</view>
				<view class="item">
					<label class="label">安全协议签署人</label>
					<view class="text">{{ ContractorInfo.F_SignName }}</view>
				</view>
				<view class="item">
					<label class="label">承包商安全评价</label>
					<view class="text">{{ ContractorInfo.F_Evaluate }}</view>
				</view>
				<view class="item">
					<label class="label">备注</label>
					<view class="text">{{ ContractorInfo.F_Remark }}</view>
				</view>
				<view class="item">
					<label class="label">营业执照</label>
					<image class="photo" :src="ContractorInfo.F_License? (baseUrl+ContractorInfo.F_License) : '/static/img/license.svg'" @click="previewLicense"></image>
				</view>
			</view>
		</view>
		<view class="staff-container">
			<view class="title">作业人员</view>
			<view class="staff-list">
				<view class="staff-item" v-for="(item,index) in StaffList">
					<image :src="item.F_Photo ? (baseUrl+item.F_Photo) : '/static/img/profile.svg'"></image>
					<label class="label">{{ item.F_Name }}</label>
				</view>
			</view>
		</view>
		<view class="botomm-btn" v-if="$checkBtnAuth('cbstzbj')">
			<navigator :url="`/pages/contractor/edit?id=${serverId}`" open-type="navigate">
				<!-- <wButton style='margin-bottom: 140rpx;' text="编辑承包商台账信息" @click.native="editor()"></wButton> -->
				<view class="btn">编辑承包商台账信息</view>
			</navigator>
		</view>
	</view>
</template>

<script>
	import { mapGetters } from 'vuex'
	import wButton from '@/components/watch-login/watch-button.vue'
	import globalConfig from '@/config'
	export default{
		components: {
			wButton,
		},
		computed: {
			...mapGetters(['themeBgColor', 'darkMode','user']),
		},
		data(){
			return{
				isRotate: false,
				ContractorInfo: {},
				StaffList: [],
				serverId: '',
				baseUrl: "",
			}
		},
		onReady() {
			uni.setNavigationBarTitle({
			    title: this.$t('承包商台账详情')
			})
			this.setNavBarColor()
		},
		onShow() {
			this.setNavBarColor()
		},
		onLoad(options){
			if (options.id) {
				this.serverId = options.id
				this.loadContractor()
				this.loadStaff()
			}
			// 监听刷新事件
			uni.$on('contractorInfoRefresh', this.loadContractor)
			uni.$on('staffListRefresh', this.loadStaff)
			this.baseUrl = globalConfig.baseUrl.replace("/api","");
		},
		onUnload() {
			// 移除事件监听
			uni.$off('contractorInfoRefresh', this.loadContractor)
			uni.$off('staffListRefresh', this.loadStaff)
		},
		onShareAppMessage() {
		    return {
		      title: 'EHS-掌上观纹', // 自定义分享标题
		      path: '/pages/start/index', // 分享页面路径（默认当前页）
		      imageUrl: this.baseUrl+'/images/nideke.jpg', // 分享图片（可选）
		      success(res) {
		        console.log('分享成功', res);
		      },
		      fail(err) {
		        console.error('分享失败', err);
		      }
		    };
		},
		methods:{
			checkPermission(){
				if(this.user.F_RoleId === "08dc7a3d-e5a8-44cf-87ea-15347b9fa336"){
					return false;
				}else{
					return true;
				}
			},
			loadContractor(){
				this.$minApi.getContractor({serverId:this.serverId}).then(res=>{
					this.ContractorInfo = res;
				})
			},
			loadStaff(){
				this.$minApi.listStaff({serverId:this.serverId}).then(res=>{
					this.StaffList = res;
				})
			},
			// editor(){
			// 	console.log('editor')
			// 	uni.reLaunch({
			// 		url: '/pages/contactor/edit?id=111',
			// 		//open-type: 'redirect'
			// 	})
			// },
			setNavBarColor() {
				uni.setNavigationBarColor({
				    frontColor: '#000000',
				    backgroundColor: this.themeBgColor,
				    animation: {
				        duration: 400,
				        timingFunc: 'easeIn'
				    }
				})
			},
			previewLicense() {
				if(this.ContractorInfo.F_License) {
					uni.previewImage({
						urls: [this.baseUrl + this.ContractorInfo.F_License],
						current: this.baseUrl + this.ContractorInfo.F_License
					});
				}
			},
		}
	}
</script>

<style lang="less" scoped>
	.container{
		display: flex;
		flex-direction: column;
		gap: 20rpx;
		min-height: 100vh;
		padding-bottom: 320rpx;
		.header{
			background-color: white;
			padding: 20rpx;
			display: flex;
			flex-direction: column;
			width: 100vw;
			.title{
				color: #333333;
				font-weight: 650;
				font-size: 36rpx;
				width: 100%;
			}
			.detail{
				display: flex;
				justify-content: space-between;
				.num{
					color: #999999;
					font-size: 24rpx;
				}
				.status {
					width: 100rpx;
					height: 30rpx;
					color: #ffffff;
					font-size: 24rpx;
					align-items: center;
					text-align: center;
					line-height: 34rpx;
					clip-path: polygon(20% 0%, 100% 0%, 80% 100%, 0% 100%);
				}
				.status-green {
				  background-color: green;
				}
				.status-red {
				  background-color: red;
				}
			}
		}
		.info {
			background-color: white;
			padding: 20rpx;
			.title{
				color: #333333;
				font-weight: 400;
				font-size: 28rpx;
				margin-bottom: 20rpx;
			}
			.content{
				display: flex;
				flex-direction: column;
				gap: 20rpx;
				.item{
					display: flex;
					flex-direction: column;
					.label{
						color: #999999;
						font-size: 24rpx;
					}
					.text{
						color: #666666;
						font-size: 28rpx;
					}
					.photo{
						width: 200rpx;
						height: 160rpx;
					}
				}
			}
		}
		.staff-container{
			background-color: white;
			padding: 20rpx;
			display: flex;
			flex-direction: column;
			.title{
				color: #333333;
				font-weight: 400;
				font-size: 28rpx;
				margin-bottom: 20rpx;
			}
			.staff-list{
				display: flex;
				flex-wrap: wrap;
				.staff-item{
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					width: 24%;
					image{
						width: 120rpx;
						height: 120rpx;
					}
					.label{
						color: #666666;
						font-size: 28rpx;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
					}
				}
			}
		}
		.botomm-btn {
			padding-bottom: 100rpx;
			height: 230rpx;
			position: fixed; /* 固定在屏幕底部 */
			bottom: 0;
			left: 0;
			width: 100%; /* 占满整个屏幕宽度 */
			background-color: #fff; /* 背景颜色，防止透明 */
			padding: 20rpx 0; /* 上下留白 */
			text-align: center; /* 让按钮居中 */
			align-items: center;
			display: flex;
			justify-content: center;
			box-shadow: 0 -10rpx 20rpx rgba(0, 0, 0, 0.1); /* 添加阴影，提升层次感 */
			z-index: 999; /* 确保层级较高 */
			.btn{
				font-size: 30rpx;
				margin-bottom: 140rpx;
				width: 601rpx;
				height: 100rpx;
				line-height: 100rpx;
				text-align: center;
				margin-left: auto;
				margin-right: auto;
				margin-top: 96rpx;
				background: linear-gradient(to right, #00984a, #00984a, #00984a);
				color: #FFFFFF;
				border: none;
				border-radius: 2.5rem;
				box-shadow: 0 0 60rpx 0 rgba(0, 0, 0, .2);
				transition: all 0.4s cubic-bezier(.57, .19, .51, .95);
			}
		}
	}
</style>