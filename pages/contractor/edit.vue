<template>
	<view class="container">
		<view class="info">
			<view class="title">承包商信息</view>
			<view class="content">
				<view class="item">
					<label class="label">公司名称<text class="required">*</text></label>
					<input v-model="ContractorInfo.F_CompanyName" class="text" name="F_CompanyName" maxlength="11" placeholder="请输入公司名称" />
				</view>
				<view class="item">
					<label class="label">公司编号<text class="required">*</text></label>
					<input class="text" v-model="ContractorInfo.F_Num" name="F_Num" maxlength="6" placeholder="请输入公司编号"/>
				</view>
				<view class="item">
					<label class="label">公司地址<text class="required">*</text></label>
					<input class="text" v-model="ContractorInfo.F_Address" name="F_Address" maxlength="6" placeholder="请输入公司地址"/>
				</view>
				<view class="item">
					<label class="label">承包商种类<text class="required">*</text></label>
					<picker mode="selector" @change="onContractorTypeChange" :value="ContractorTypeIndex" range-key="text" :range="ContractorTypeList">
						<view class="uni-input">{{ContractorInfo.F_TypeName?ContractorTypeList[ContractorTypeIndex].text:"请选择承包商种类"}}</view>
					</picker>
				</view>
				<view class="item">
					<label class="label">承包商负责人<text class="required">*</text></label>
					<input class="text" v-model="ContractorInfo.F_HeadName" name="F_HeadName" maxlength="6" placeholder="请输入承包商负责人"/>
				</view>
				<view class="item">
					<label class="label">承包商内部联系人<text class="required">*</text></label>
					<input class="text" v-model="ContractorInfo.F_ContactsName" name="F_ContactsName" maxlength="6" placeholder="请输入承包商内部联系人"/>
				</view>
				<view class="item">
					<label class="label">提供的服务类型<text class="required">*</text></label>
					<input class="text" v-model="ContractorInfo.F_ServeId" name="F_ServeId" maxlength="6" placeholder="请输入提供的服务类型"/>
				</view>
				<view class="item">
					<label class="label">签署日期<text class="required">*</text></label>
					<picker mode="date" @change="onStartDateChange" :value="ContractorInfo.F_StartTime" :end="EndDate">
						<view class="uni-input">{{ ContractorInfo.F_StartTime }}</view>
					</picker>
				</view>
				<view class="item">
					<label class="label">过期日期<text class="required">*</text></label>
					<picker mode="date" @change="onEndDateChange" :value="ContractorInfo.F_EndTime">
						<view class="uni-input">{{ ContractorInfo.F_EndTime }}</view>
					</picker>
				</view>
				<view class="item">
					<label class="label">状态<text class="required">*</text></label>
					<radio-group @change="onStatusChange" class="text" style="width:80%">
						<label class="radio-item" v-for="item in EnabledMarkList" :key="item.value">
						    <radio style="transform: scale(0.6);" :value="item.value" :checked="item.value === ContractorInfo.F_EnabledMark" />
						    {{ item.text }}
						</label>
					</radio-group>
				</view>
				<view class="item">
					<label class="label">安全协议签署人<text class="required">*</text></label>
					<input class="text" v-model="ContractorInfo.F_SignName" name="F_SignName" maxlength="10" placeholder="请输入安全协议签署人"/>
				</view>
				<view class="item">
					<label class="label">承包商安全评价<text class="required">*</text></label>
					<picker mode="selector" @change="onEvaluateChange" :value="EvaluateIndex" :range="EvaluateList">
						<view class="uni-input">{{EvaluateList[EvaluateIndex]}}</view>
					</picker>
				</view>
				<view class="item">
					<label class="label">备注</label>
					<input class="text" v-model="ContractorInfo.F_Remark" name="F_Remark" maxlength="50" placeholder="请输入备注"/>
				</view>
				<view class="item">
					<label class="label">营业执照</label>
					<view class="license-container">
						<view v-if="ContractorInfo.F_License" class="license-preview">
							<image class="photo" :src="ContractorInfo.F_License ? (baseUrl+ContractorInfo.F_License) : '/static/img/license.svg'" @click="previewLicense"></image>
							<image class="del" src="/static/img/del.svg" @click="deleteLicense"></image>
						</view>
						<view class="upload-btn" v-else @click="chooseLicense">
							<text class="upload-icon">+</text>
							<text>上传营业执照</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="staff-container">
			<view class="title">作业人员</view>
			<view class="staff-list">
				<view class="staff-item" v-for="(item,index) in StaffList">
					<image :src="item.F_Photo ? (baseUrl+item.F_Photo) : '/static/img/profile.svg'" @click="toUser(item.F_Id)"></image>
					<label class="label" @click="toUser(item.F_Id)">{{ item.F_Name }}</label>
					<image class="del" src="/static/img/del.svg" @click="del(item.F_Id)"></image>
				</view>
				<view class="staff-item">
					<image src="/static/img/add_user.svg" @click="toUser()"></image>
					<label class="label" @click="toUser()">新增</label>
				</view>
			</view>
		</view>
		<view class="botomm-btn">
			<wButton style='margin-bottom: 140rpx;' text="完成" :rotate="isRotate" @click.native="save()"></wButton>
		</view>
	</view>
</template>

<script>
	import { mapGetters } from 'vuex'
	import wButton from '@/components/watch-login/watch-button.vue'
	import wInput from '@/components/watch-login/watch-input.vue'
	import globalConfig from '@/config'
	export default{
		components: {
			wButton,
			wInput
		},
		computed: {
			...mapGetters(['themeBgColor', 'darkMode']),
		},
		
		data(){
			return{
				isRotate: false,
				EnabledMarkList: [
					{ value: true, text: "有效" },
					{ value: false, text: "无效" },
				],
				EnabledMarkIndex:0,
				ContractorTypeList: [
					{ value: 'DailyType', text: "日常性承包商" },
					{ value: 'DiscreteType', text: "间断性承包商" },
				],
				ContractorTypeIndex: 0,
				ContractorInfo: {},
				StaffList: [],
				serverId: '',
				baseUrl: "",
				uploadUrl: globalConfig.baseUrl + "/api/file/upload",
				EvaluateList: ['OK', 'NOK'],
				EvaluateIndex: 0,
			}
		},
		onReady() {
			uni.setNavigationBarTitle({
			    title: this.$t('编辑承包商台账信息')
			})
			this.setNavBarColor()
		},
		onShow(options) {
			this.setNavBarColor()
			this.loadContractor();
			this.loadStaff();
		},
		onLoad(options){
			this.serverId = options.id;
			this.baseUrl = globalConfig.baseUrl.replace("/api","");
			this.loadContractor();
			this.loadStaff();
		},
		onShareAppMessage() {
		    return {
		      title: 'EHS-掌上观纹', // 自定义分享标题
		      path: '/pages/start/index', // 分享页面路径（默认当前页）
		      imageUrl: this.baseUrl+'/images/nideke.jpg', // 分享图片（可选）
		      success(res) {
		        console.log('分享成功', res);
		      },
		      fail(err) {
		        console.error('分享失败', err);
		      }
		    };
		},
		methods:{
			loadContractor(){
				this.$minApi.getContractor({serverId:this.serverId}).then(res=>{
					this.ContractorInfo = res;
				})
			},
			loadStaff(){
				this.$minApi.listStaff({serverId:this.serverId}).then(res=>{
					this.StaffList = res;
				})
			},
			onContractorTypeChange(e) {
			   // 更新当前选择的索引
			   this.ContractorTypeIndex = e.detail.value;
			   const info = this.ContractorTypeList[this.ContractorTypeIndex];
			   this.ContractorInfo.F_TypeName = info.value;
			},
			save(){
				// 表单验证
				if(!this.ContractorInfo.F_CompanyName) {
					uni.showToast({
						title: '请输入公司名称',
						icon: 'none'
					});
					return;
				}
				if(!this.ContractorInfo.F_Num) {
					uni.showToast({
						title: '请输入公司编号',
						icon: 'none'
					});
					return;
				}
				if(!this.ContractorInfo.F_Address) {
					uni.showToast({
						title: '请输入公司地址',
						icon: 'none'
					});
					return;
				}
				if(!this.ContractorInfo.F_TypeName) {
					uni.showToast({
						title: '请选择承包商种类',
						icon: 'none'
					});
					return;
				}
				if(!this.ContractorInfo.F_HeadName) {
					uni.showToast({
						title: '请输入承包商负责人',
						icon: 'none'
					});
					return;
				}
				if(!this.ContractorInfo.F_ContactsName) {
					uni.showToast({
						title: '请输入承包商内部联系人',
						icon: 'none'
					});
					return;
				}
				if(!this.ContractorInfo.F_ServeId) {
					uni.showToast({
						title: '请输入提供的服务类型',
						icon: 'none'
					});
					return;
				}
				if(!this.ContractorInfo.F_StartTime) {
					uni.showToast({
						title: '请选择签署日期',
						icon: 'none'
					});
					return;
				}
				if(!this.ContractorInfo.F_EndTime) {
					uni.showToast({
						title: '请选择过期日期',
						icon: 'none'
					});
					return;
				}
				if(this.ContractorInfo.F_EnabledMark === undefined) {
					uni.showToast({
						title: '请选择状态',
						icon: 'none'
					});
					return;
				}
				if(!this.ContractorInfo.F_SignName) {
					uni.showToast({
						title: '请输入安全协议签署人',
						icon: 'none'
					});
					return;
				}
				if(!this.ContractorInfo.F_Evaluate) {
					uni.showToast({
						title: '请选择承包商安全评价',
						icon: 'none'
					});
					return;
				}

				this.isRotate = true;
				this.$minApi.saveContractor(this.ContractorInfo).then(res=>{
					this.isRotate = false;
					if(res.state == 'success'){
						uni.showToast({
							title: '保存成功',
							icon: 'success'
						})
						// 发送刷新事件
						uni.$emit('contractorListRefresh')
						uni.$emit('contractorInfoRefresh')
						// 返回上一页
						setTimeout(() => {
							uni.navigateBack()
						}, 1500)
					}else{
						uni.showToast({
							title: '保存失败',
							icon: 'error'
						})
					}
				})
			},
			onStatusChange(e) {
				this.ContractorInfo.F_EnabledMark = e.detail.value === 'true';
			},
			setNavBarColor() {
				// navBar-bg-color
				uni.setNavigationBarColor({
				    frontColor: '#000000',
				    backgroundColor: this.themeBgColor,
				    animation: {
				        duration: 400,
				        timingFunc: 'easeIn'
				    }
				})
			},
			del(id){
				let that = this;
				uni.showModal({
					title: '提示',
					content: '确定删除该作业人员？',
					success: function (res) {
						if (res.confirm) {
							that.$minApi.delStaff({F_Id: id}).then(res => {
								that.loadStaff();
							})
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			toUser(id){
				uni.navigateTo({
					url: '/pages/contractor/staff?id=' + id+'&serverId='+this.serverId,
				})
			},
			onStartDateChange(e){
				this.ContractorInfo.F_StartTime = e.detail.value;
			},
			onEndDateChange(e){
				this.ContractorInfo.F_EndTime = e.detail.value;
			},
			onEvaluateChange(e) {
				this.EvaluateIndex = e.detail.value;
				this.ContractorInfo.F_Evaluate = this.EvaluateList[this.EvaluateIndex];
			},
			chooseLicense() {
				uni.chooseImage({
					count: 1,
					success: (res) => {
						const tempFilePath = res.tempFilePaths[0];
						const params = {
							filePath: tempFilePath,
							name: 'file',
							fileby: '营业执照',
							filetype: 1
						};
						this.$minApi.uploadFile(params).then(res => {
							console.log(res);
							if(res.code === 200) {
								
								this.ContractorInfo.F_License = res.data;
								uni.showToast({
									title: '上传成功',
									icon: 'success'
								});
							} else {
								uni.showToast({
									title: '上传失败1',
									icon: 'none'
								});
							}
						}).catch(() => {
							uni.showToast({
								title: '上传失败2',
								icon: 'none'
							});
						});
					}
				});
			},
			previewLicense() {
				if(this.ContractorInfo.F_License) {
					uni.previewImage({
						urls: [this.baseUrl + this.ContractorInfo.F_License],
						current: this.baseUrl + this.ContractorInfo.F_License
					});
				}
			},
			deleteLicense() {
				uni.showModal({
					title: '提示',
					content: '确定要删除营业执照吗？',
					success: (res) => {
						if(res.confirm) {
							this.ContractorInfo.F_License = '';
							uni.showToast({
								title: '删除成功',
								icon: 'success'
							});
						}
					}
				});
			},
		}
	}
</script>

<style lang="less" scoped>
	.container{
		display: flex;
		flex-direction: column;
		gap: 20rpx;
		min-height: 100vh;
		padding-bottom: 320rpx;
		.info {
			background-color: white;
			.title{
				color: #7F7F7F;
				font-weight: 400;
				font-size: 28rpx;
				margin-bottom: 20rpx;
				background-color: rgba(242, 242, 242, 1);
				padding: 20rpx;
			}
			.content{
				padding: 20rpx;
				display: flex;
				flex-direction: column;
				gap: 20rpx;
				.item{
					display: flex;
					flex-direction: row;
					align-items: center;
					line-height: 66rpx;
					justify-content: space-between;
					border-bottom: 1rpx solid rgba(242, 242, 242, 1);
					.label{
						color: #666666;
						font-size: 28rpx;
					}
					.text{
						color: #666666;
						font-size: 28rpx;
						text-align: right;
					}
					.uni-input{
						text-align: right;
						line-height: 30rpx;
						padding-right: 0;
						color: #666666;
					}
					.photo{
						width: 200rpx;
						height: 160rpx;
					}
				}
			}
		}
		.staff-container{
			background-color: white;
			display: flex;
			flex-direction: column;
			.title{
				background-color: rgba(242, 242, 242, 1);
				color: #333333;
				font-weight: 400;
				font-size: 28rpx;
				margin-bottom: 20rpx;
				padding: 20rpx;
			}
			.staff-list{
				padding: 20rpx;
				display: flex;
				flex-wrap: wrap;
				.staff-item{
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					width: 24%;
					image{
						width: 120rpx;
						height: 120rpx;
					}
					.label{
						color: #666666;
						font-size: 28rpx;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
					}
					.del{
						position: absolute;
						margin-bottom: 140rpx;
						margin-left: 128rpx;
						width: 50rpx;
					}
				}
			}
		}
		.botomm-btn {
			padding-bottom: 100rpx;
			height: 230rpx;
			position: fixed; /* 固定在屏幕底部 */
			bottom: 0;
			left: 0;
			width: 100%; /* 占满整个屏幕宽度 */
			background-color: #fff; /* 背景颜色，防止透明 */
			padding: 20rpx 0; /* 上下留白 */
			text-align: center; /* 让按钮居中 */
			align-items: center;
			display: flex;
			justify-content: center;
			box-shadow: 0 -10rpx 20rpx rgba(0, 0, 0, 0.1); /* 添加阴影，提升层次感 */
			z-index: 999; /* 确保层级较高 */
		}
	}
	.required {
		color: #ff0000;
		margin-left: 4rpx;
	}
	.license-container {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		.upload-btn {
			width: 200rpx;
			height: 160rpx;
			border: 1px dashed #dcdcdc;
			border-radius: 8rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			image {
				width: 60rpx;
				height: 60rpx;
				margin-bottom: 10rpx;
			}
			text {
				font-size: 24rpx;
				color: #999;
			}
		}
	}
	.license-preview {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		position: relative;
		.photo {
			width: 200rpx;
			height: 160rpx;
		}
		.del {
			position: absolute;
			top: -20rpx;
			right: -20rpx;
			width: 50rpx;
			height: 50rpx;
		}
	}
</style>