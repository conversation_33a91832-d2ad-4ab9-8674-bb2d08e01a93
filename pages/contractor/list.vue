<template>
	<view class="container">
		<uni-nav-bar :fixed="true" color="#333333" :background-color="themeBgColor" :border="false">
			<view class="input-view">
				<uni-icons type="search" size="22" color="#666666" />
				<input v-model="searchVal" confirm-type="search" class="input" type="text" placeholder="搜索" @confirm="search">
				<uni-icons :color="'#999999'" v-if="searchVal!==''" class="icon-clear" type="clear" size="22" @click="clear" />
			</view>
		</uni-nav-bar>
		<view class="filters">
			<view class="item">
				<label>种类</label>
				<picker mode="selector" @change="onContractorTypeChange" :value="ContractorTypeIndex" range-key="text" :range="ContractorTypeList">
					<view class="uni-input">{{ContractorTypeList[ContractorTypeIndex].text}}</view>
				</picker>
			</view>
			<view class="item">
				<label>状态</label>
				<picker mode="selector" @change="onEnabledMarkChange" :value="EnabledMarkIndex" range-key="text" :range="EnabledMarkList">
					<view class="uni-input">{{EnabledMarkList[EnabledMarkIndex].text}}</view>
				</picker>
			</view>
		</view>
		<view class="card-list">
			<view class="card" v-for="(item,index) in ContractorList">
				<navigator :url="`/pages/contractor/info?id=${item.F_Id}`" open-type="navigate" class="navbar" v-if="$checkBtnAuth('cbstzck')">
					<view class="header">
						<view class="title">{{ item.F_CompanyName}}</view>
						<view class="status">
							<view :class="{'dot': true, 'green': item.F_EnabledMark, 'red': !item.F_EnabledMark}"></view>
							<text>{{ item.F_EnabledMark ? '有效' : '无效' }}</text>
						</view>
					</view>
					<view class="content">
						<view>公司地址：{{ item.F_Address }}</view>
						<view>承包商种类：{{ item.F_TypeName == 'DailyType' ? '日常性承包商' : '间断性承包商'}}</view>
						<view>承包商负责人：{{ item.F_HeadName }}</view>
						<view>承包商内部联系人：{{ item.F_ContactsName }}</view>
						<view class="clock"><image src="/static/img/clock.svg"></image>签署日期：{{ item.F_StartTime }}</view>
					</view>
				</navigator>
				<view class="navbar" v-else>
					<view class="header">
						<view class="title">{{ item.F_CompanyName}}</view>
						<view class="status">
							<view :class="{'dot': true, 'green': item.F_EnabledMark, 'red': !item.F_EnabledMark}"></view>
							<text>{{ item.F_EnabledMark ? '有效' : '无效' }}</text>
						</view>
					</view>
					<view class="content">
						<view>公司地址：{{ item.F_Address }}</view>
						<view>承包商种类：{{ item.F_TypeName == 'DailyType' ? '日常性承包商' : '间断性承包商'}}</view>
						<view>承包商负责人：{{ item.F_HeadName }}</view>
						<view>承包商内部联系人：{{ item.F_ContactsName }}</view>
						<view class="clock"><image src="/static/img/clock.svg"></image>签署日期：{{ item.F_StartTime }}</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { mapGetters } from 'vuex'
	import uniIcons from '@/components/uni-icons/uni-icons.vue'
	import uniNavBar from '@/components/uni-nav-bar/uni-nav-bar.vue'
	import globalConfig from '@/config'
	export default {
		components: {
			uniIcons,
			uniNavBar
		},
		computed: {
			...mapGetters(['themeBgColor', 'darkMode','user']),
		},
		data() {
			return {
				baseUrl:'',
				searchVal: '',
				winHeight: "",
				value: 1,
				total: 0,
				EnabledMarkList: [
					{ value: '', text: "全部" },
					{ value: 'true', text: "有效" },
					{ value: 'false', text: "无效" },
				],
				EnabledMarkIndex:0,
				ContractorTypeList: [
					{ value: '', text: "全部" },
					{ value: 'DailyType', text: "日常性承包商" },
					{ value: 'DiscreteType', text: "间断性承包商" },
				],
				ContractorTypeIndex: 0,
				ContractorList: [],
				loading: false,
				isEnd: false,
				queryParams: {
					page: 1,
					rows: 10,
					keyword: ''
				}
			}
		},
		onReady() {
			uni.setNavigationBarTitle({
			    title: this.$t('承包商管理台账')
			})
			this.setNavBarColor()
		},
		onShow() {
			this.setNavBarColor()
		},
		onLoad() {
			this.baseUrl = globalConfig.baseUrl.replace('/api', '');
			uni.getSystemInfo({
				success: res => {
					this.winHeight = res.windowHeight
				}
			})
			this.loadData()
			// 监听刷新事件
			uni.$on('contractorListRefresh', this.loadData)
		},
		onUnload() {
			// 移除事件监听
			uni.$off('contractorListRefresh', this.loadData)
		},
		onReachBottom() {
			if (!this.loading && !this.isEnd) {
				this.queryParams.page++
				this.loadData()
			}
		},
		onShareAppMessage() {
		    return {
		      title: 'EHS-掌上观纹', // 自定义分享标题
		      path: '/pages/start/index', // 分享页面路径（默认当前页）
		      imageUrl: this.baseUrl+'/images/nideke.jpg', // 分享图片（可选）
		      success(res) {
		        console.log('分享成功', res);
		      },
		      fail(err) {
		        console.error('分享失败', err);
		      }
		    };
		},
		methods: {
			loadData() {
				if (this.loading || this.isEnd) return
				this.loading = true
				
				uni.showLoading({
					title: '加载中...'
				})
				
				this.$minApi.listContractor(this.queryParams).then(res => {
					if (res.state === 0) {
						if (this.queryParams.page === 1) {
							this.ContractorList = res.data
						} else {
							this.ContractorList = [...this.ContractorList, ...res.data]
						}
						this.total = res.total || this.ContractorList.length
						if (res.data.length < this.queryParams.rows) {
							this.isEnd = true
						}
					} else {
						uni.showToast({
							title: res.message || '加载失败',
							icon: 'none'
						})
					}
				}).catch(() => {
					uni.showToast({
						title: '加载失败',
						icon: 'none'
					})
				}).finally(() => {
					uni.hideLoading()
					this.loading = false
				})
			},
			onEnabledMarkChange(e) {
			  // 更新当前选择的索引
			  this.EnabledMarkIndex = e.detail.value;
			  const info = this.EnabledMarkList[this.EnabledMarkIndex];
			  this.queryParams.status = info.value;
			  this.loadData();
			},
			onContractorTypeChange(e) {
			  // 更新当前选择的索引
			  this.ContractorTypeIndex = e.detail.value;
			  const info = this.ContractorTypeList[this.ContractorTypeIndex];
			  this.queryParams.contractorType = info.value;
			  this.loadData();
			},
			setNavBarColor() {
				// navBar-bg-color
				uni.setNavigationBarColor({
				    frontColor: '#000000',
				    backgroundColor: this.themeBgColor,
				    animation: {
				        duration: 400,
				        timingFunc: 'easeIn'
				    }
				})
			},
			search() {
				console.log('queryParams',this.searchVal)
				this.queryParams.keyword = this.searchVal;
				this.queryParams.page = 1
				this.isEnd = false
				this.ContractorList = []
				this.loadData()
			},
			clear() {
				this.searchVal = ''
				this.queryParams.keyword = ''
				this.search()
			}
		}
	}
</script>

<style lang="less">
	.dot {
	  height: 10px;
	  width: 10px;
	  border-radius: 50%;
	  display: inline-block;
	  margin-right: 5px;
	}
	
	.green {
	  background-color: green;
	}
	
	.red {
	  background-color: red;
	}

	.container{
		height: 100vh;
		.filters{
			display: flex;
			flex-direction: row;
			align-items: center;
			background-color: white;
			padding: 20rpx;
			width: 100%;
			.item{
				display: flex;
				flex-direction: row;
				align-items: center;
				flex: 1;
				label{
					color: #4c4c4c;
					width: 20%;
					align-items: center;
					text-align: center;
					line-height: 20rpx;
				}
				.uni-input{
					color: #7c7c7c;
					border: 1px solid #00984a;
					align-items: center;
					text-align: left;
					width: 36vw;
					border-radius: 10rpx !important;
					line-height: 20rpx;
				}
			}
		}
		.card-list{
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			width: 100vw;
			padding: 30rpx;
			gap: 20rpx;
			.card{
				color: #999999;
				// font-size: 12rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				width: 100%;
				background-color: white;
				padding: 20rpx;
				border-radius: 10rpx;
				gap: 20rpx;
				.navbar{
					width: 100%;
				}
				.header{
					width: 100%;
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: space-between;
					.title{
						color: #333333;
						font-size: 34rpx;
						font-weight: 400;
					}
					.status{
						// color: #333333;
						// font-size: 14rpx;
					}
				}
				.content{
					width: 100%;
					display: flex;
					flex-direction: column;
					align-items: left;
					.clock{
						margin-top: 10rpx;
						display: flex;
						align-items: center;
						gap: 5rpx;
						image{
							width: 30rpx;
							height: 30rpx;
						}
					}
				}
			}
		}
	}
</style>