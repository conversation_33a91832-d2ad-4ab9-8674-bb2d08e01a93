<template>
	<view class="container">
		<view class="info">
			<view class="title">作业人员</view>
			<view class="content">
				<view class="item">
					<label class="label">姓名<text class="required">*</text></label>
					<input v-model="StaffInfo.F_Name" class="text" name="F_Name" maxlength="11" placeholder="请输入作业人员姓名" />
				</view>
				<view class="item">
					<label class="label">状态<text class="required">*</text></label>
					<radio-group @change="onStatusChange" class="text" style="width:80%">
						<label class="radio-item" v-for="item in EnabledMarkList" :key="item.value">
						    <radio style="transform: scale(0.6);" :value="item.value" :checked="item.value === StaffInfo.F_EnabledMark" />
						    {{ item.text }}
						</label>
					</radio-group>
				</view>
				<view class="item">
					<label class="label">头像</label>
				</view>
				<view class="avatar-upload">
					<view class="avatar-item" v-if="StaffInfo.F_Photo">
						<image :src="getFullPath(StaffInfo.F_Photo)" mode="aspectFill" @click="previewImage"></image>
						<text class="delete-btn" @click="deletePhoto">×</text>
					</view>
					<view v-else class="upload-btn" @click="chooseImage">
						<text class="upload-icon">+</text>
						<text class="upload-text">上传头像</text>
					</view>
				</view>
			</view>
		</view>
		<view class="botomm-btn">
			<wButton style='margin-bottom: 140rpx;' text="完成" :rotate="isRotate" @click.native="save()"></wButton>
		</view>
	</view>
</template>

<script>
	import { mapGetters } from 'vuex'
	import wButton from '@/components/watch-login/watch-button.vue'
	import wInput from '@/components/watch-login/watch-input.vue'
	import globalConfig from '@/config'
	export default{
		components: {
			wButton
		},
		computed: {
			...mapGetters(['themeBgColor', 'darkMode']),
		},
		data(){
			return{
				baseUrl:'',
				isRotate: false,
				EnabledMarkList: [
					{ value: true, text: "有效" },
					{ value: false, text: "无效" },
				],
				EnabledMarkIndex:0,
				StaffInfo: {
					F_Photo: ''
				},
				staffId: ''
			}
		},
		onLoad(options){
			this.baseUrl = globalConfig.baseUrl.replace('/api', '');
			uni.setNavigationBarTitle({
			    title: this.$t(options.id=='undefined'?'新增作业人员':'编辑作业人员')
			})
			this.staffId = options.id
			this.StaffInfo.F_ContractorId = options.serverId
			if(this.staffId){
				this.loadStaff();
			}
		},
		onReady() {
			this.setNavBarColor()
		},
		onShow() {
			this.setNavBarColor()
		},
		onShareAppMessage() {
		    return {
		      title: 'EHS-掌上观纹', // 自定义分享标题
		      path: '/pages/start/index', // 分享页面路径（默认当前页）
		      imageUrl: this.baseUrl+'/images/nideke.jpg', // 分享图片（可选）
		      success(res) {
		        console.log('分享成功', res);
		      },
		      fail(err) {
		        console.error('分享失败', err);
		      }
		    };
		},
		methods:{
			loadStaff(){
				this.$minApi.getStaff({staffId: this.staffId}).then(res=>{
					this.StaffInfo = res;
				})
			},
			save(){
				if (!this.StaffInfo.F_Name) {
					uni.showToast({
						title: '请输入姓名',
						icon: 'none'
					})
					return;
				}
				
				if (this.StaffInfo.F_EnabledMark === undefined) {
					uni.showToast({
						title: '请选择状态',
						icon: 'none'
					})
					return;
				}

				this.isRotate = true;
				this.$minApi.saveStaff(this.StaffInfo).then(res=>{
					this.isRotate = false;
					if(res.state == 'success'){
						uni.showToast({
							title: '保存成功',
							icon: 'success'
						})
						uni.$emit('staffListRefresh')
						setTimeout(() => {
							uni.navigateBack()
						}, 1500)
					}else{
						uni.showToast({
							title: '保存失败',
							icon: 'error'
						})
					}
				})
			},
			setNavBarColor() {
				uni.setNavigationBarColor({
				    frontColor: '#000000',
				    backgroundColor: this.themeBgColor,
				    animation: {
				        duration: 400,
				        timingFunc: 'easeIn'
				    }
				})
			},
			chooseImage() {
				uni.chooseImage({
					count: 1,
					success: (res) => {
						this.uploadFile(res.tempFilePaths[0]);
					}
				});
			},
			uploadFile(filePath) {
				uni.showLoading({
					title: '上传中...'
				});
				
				const params = {
					filePath: filePath,
					name: 'file',
					fileby: 'staff',
					filetype: 1
				};
				
				this.$minApi.uploadFile(params).then(res => {
					if (res.code === 200) {
						this.StaffInfo.F_Photo = res.data;
					} else {
						uni.showToast({
							title: '上传失败',
							icon: 'none'
						});
					}
				}).catch(() => {
					uni.showToast({
						title: '上传失败',
						icon: 'none'
					});
				}).finally(() => {
					uni.hideLoading();
				});
			},
			getFullPath(path) {
				if (!path) return '';
				return globalConfig.baseUrl.replace("/api","") + path;
			},
			previewImage() {
				if (this.StaffInfo.F_Photo) {
					uni.previewImage({
						urls: [this.getFullPath(this.StaffInfo.F_Photo)]
					});
				}
			},
			deletePhoto() {
				uni.showModal({
					title: '提示',
					content: '确定要删除头像吗？',
					success: (res) => {
						if (res.confirm) {
							this.StaffInfo.F_Photo = '';
						}
					}
				});
			},
			onStatusChange(e) {
				this.StaffInfo.F_EnabledMark = e.detail.value === 'true';
			}
		}
	}
</script>

<style lang="less" scoped>
	.container{
		display: flex;
		flex-direction: column;
		gap: 20rpx;
		min-height: 100vh;
		padding-bottom: 320rpx;
		.info {
			background-color: white;
			.title{
				color: #7F7F7F;
				font-weight: 400;
				font-size: 28rpx;
				margin-bottom: 20rpx;
				background-color: rgba(242, 242, 242, 1);
				padding: 20rpx;
			}
			.content{
				padding: 20rpx;
				display: flex;
				flex-direction: column;
				gap: 20rpx;
				.item{
					display: flex;
					flex-direction: row;
					align-items: center;
					line-height: 66rpx;
					justify-content: space-between;
					border-bottom: 1rpx solid rgba(242, 242, 242, 1);
					.label{
						color: #666666;
						font-size: 28rpx;
					}
					.text{
						color: #666666;
						font-size: 28rpx;
						text-align: right;
					}
					.uni-input{
						text-align: right;
						line-height: 30rpx;
						padding-right: 0;
						color: #666666;
					}
					.photo{
						width: 200rpx;
						height: 160rpx;
					}
				}
			}
		}
		.botomm-btn {
			padding-bottom: 100rpx;
			height: 230rpx;
			position: fixed;
			bottom: 0;
			left: 0;
			width: 100%;
			background-color: #fff;
			padding: 20rpx 0;
			text-align: center;
			align-items: center;
			display: flex;
			justify-content: center;
			box-shadow: 0 -10rpx 20rpx rgba(0, 0, 0, 0.1);
			z-index: 999;
		}
		.avatar-upload {
			padding: 20rpx;
			background-color: #fff;
			
			.avatar-item {
				position: relative;
				width: 200rpx;
				height: 200rpx;
				
				image {
					width: 100%;
					height: 100%;
					border-radius: 8rpx;
					object-fit: cover;
				}
				
				.delete-btn {
					position: absolute;
					top: -20rpx;
					right: -20rpx;
					width: 40rpx;
					height: 40rpx;
					background-color: rgba(0, 0, 0, 0.5);
					color: #fff;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 32rpx;
				}
			}
			
			.upload-btn {
				width: 200rpx;
				height: 200rpx;
				border: 2rpx dashed #ddd;
				border-radius: 8rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				background-color: #f8f8f8;
				
				.upload-icon {
					font-size: 48rpx;
					color: #999;
					margin-bottom: 10rpx;
				}
				
				.upload-text {
					font-size: 24rpx;
					color: #999;
				}
			}
		}
		.required {
			color: #ff0000;
			margin-left: 4rpx;
		}
	}
</style>