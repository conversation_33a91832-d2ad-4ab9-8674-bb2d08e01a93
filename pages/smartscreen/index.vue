<template>
	<view class="container" :style="'background-image: url(' + baseUrl + '/images/back_home.png);background-size: 210vw 60vh;background-repeat: no-repeat;'">
		<view class="top">
			<image :src="baseUrl+'/images/home-head.png'" style="width:100vh;height:103rpx;"></image>
			<!-- <view class="left">
				<image src="../../static/img/logo.png"></image>承包商EHS管理系统
			</view>
			<view class="right">
				<navigator url="/pages/home/<USER>" open-type="reLaunch">返回</navigator>
			</view> -->
			<image class="logo" src="../../static/img/logo.png" mode="aspectFit"></image>
			<view class="tool">
				<view class="item">
					<image :src="baseUrl+'/images/date.png'"></image>
					<text>{{ currentDate }}</text>
				</view>
				<view class="item">
					<image :src="baseUrl+'/images/time.png'"></image>
					<text>{{ currentWeekday }} {{ currentTime }}</text>
				</view>
				<view class="item">
					<image :src="userAvatar" mode="aspectFit"></image>
					<text class="name">{{ user.F_RealName }}</text>
				</view>
				<view class="item">
					<navigator url="/pages/home/<USER>" open-type="reLaunch"><image :src="baseUrl+'/images/quit.png'"></image></navigator>
				</view>
			</view>
		</view>
		<view class="middle">
			<view class="left" style="position: relative;">
				<image :src="imageUrl"></image>
				<view v-for="item in indexData.taskList" :key="item.F_Id" class="task-list" :class="[item.denger ? 'denger' : 'narmal']" :style="{ top: (item.PostionY * 2219/3650 * 1.15) + 'vw', left: calculateLeftPosition(item.PostionX) + 'vh'}" @tap.stop="handleTaskTap(item)">
					<view style="display: flex;">
						<view class="tubiao" :style="'background-image: url(' + baseUrl + '/images/yellow.png);background-size: 40rpx 40rpx;'" v-if="item.F_Type == '101'">{{ item.F_Num }}</view>
						<view class="tubiao" :style="'background-image: url('+ baseUrl +'/images/green.png);background-size: 40rpx 40rpx;'" v-if="item.F_Type == '102'">{{ item.F_Num }}</view>
						<image src="../../static/img/fire.png" v-if="item.HasFire" class="tubiao"></image>
						<image src="../../static/img/ladder.png" v-if="item.HasHight" class="tubiao"></image>
						<image src="../../static/img/cliver.png" v-if="item.HasDiaoZhuang" class="tubiao"></image>
						<image src="../../static/img/box.png" v-if="item.HasKongjian" class="tubiao"></image>
					</view>
				</view>
				
				<!-- 重叠区域弹出列表 -->
				<view v-if="showPopup" class="task-popup">
					<view class="popup-header">
						<text>该区域作业列表</text>
						<text class="close-btn" @tap="closePopup">×</text>
					</view>
					<scroll-view scroll-y class="popup-content">
						<view v-for="task in nearbyTasks" :key="task.F_Id" 
								class="popup-task-item" 
								:class="{ danger: task.denger }"
								@tap="navigateToTask(task)">
							<view class="task-info">
								<view class="task-icons">
									<view class="tubiao-small" :style="'background-image: url(' + baseUrl + '/images/yellow.png);background-size: 36rpx 36rpx;'" v-if="task.F_Type == '101'">{{ task.F_Num }}</view>
									<view class="tubiao-small" :style="'background-image: url('+ baseUrl +'/images/green.png);background-size: 36rpx 36rpx;'" v-if="task.F_Type == '102'">{{ task.F_Num }}</view>
									<image src="../../static/img/fire.png" v-if="task.HasFire" class="icon-small"></image>
									<image src="../../static/img/ladder.png" v-if="task.HasHight" class="icon-small"></image>
									<image src="../../static/img/cliver.png" v-if="task.HasDiaoZhuang" class="icon-small"></image>
									<image src="../../static/img/box.png" v-if="task.HasKongjian" class="icon-small"></image>
								</view>
								<text class="task-type">{{ task.F_Type == '101' ? '承包商' : '内部' }}作业</text>
							</view>
						</view>
					</scroll-view>
				</view>
			</view>
			<view class="right">
				<view class="data-list">
					<view class="item">
						<view class="title">承包商作业</view>
						<view class="shuju">
							<image class="yuan" src="../../static/img/yellow.png"></image>
							<view class="wenzi"><view class="num">{{ indexData.taskCountTotalVo.ServerCount }}</view>个</view>
						</view>
					</view>
					<view class="item">
						<view class="title">内部作业</view>
						<view class="shuju">
							<image class="yuan" src="../../static/img/green.png"></image>
							<view class="wenzi"><view class="num">{{ indexData.taskCountTotalVo.InterlCount }}</view>个</view>
						</view>
					</view>
					<view class="item">
						<view class="title">动火作业</view>
						<view class="shuju">
							<image class="yuan" src="../../static/img/fire.png"></image>
							<view class="wenzi"><view class="num">{{ indexData.taskCountTotalVo.CountFire }}</view>个</view>
						</view>
					</view>
					<view class="item">
						<view class="title">登高作业</view>
						<view class="shuju">
							<image class="yuan" src="../../static/img/ladder.png"></image>
							<view class="wenzi"><view class="num">{{ indexData.taskCountTotalVo.CountHight }}</view>个</view>
						</view>
					</view>
					<view class="item">
						<view class="title">吊装作业</view>
						<view class="shuju">
							<image class="yuan" src="../../static/img/cliver.png"></image>
							<view class="wenzi"><view class="num">{{ indexData.taskCountTotalVo.CountDiaoZhuang }}</view>个</view>
						</view>
					</view>
					<view class="item">
						<view class="title">有限空间作业</view>
						<view class="shuju">
							<image class="yuan" src="../../static/img/box.png"></image>
							<view class="wenzi"><view class="num">{{ indexData.taskCountTotalVo.CountKongjian }}</view>个</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<!-- <view class="bottom">
			<view class="item-list">
				<view class="item">
					<image class="yuan" src="../../static/img/yellow.png"></image>
					<view class="title">承包商作业</view>
				</view>
				<view class="item">
					<image class="yuan" src="../../static/img/fire.png"></image>
					<view class="title">动火作业</view>
				</view>
				<view class="item">
					<image class="yuan" src="../../static/img/ladder.png"></image>
					<view class="title">登高作业</view>
				</view>
				<view class="item">
					<image class="yuan" src="../../static/img/cliver.png"></image>
					<view class="title">吊装作业</view>
				</view>
				<view class="item">
					<image class="yuan" src="../../static/img/box.png"></image>
					<view class="title">有限空间作业</view>
				</view>
				<view class="item">
					<view class="title">*图上任务位置均可点击</view>
				</view>
			</view>
		</view> -->
	</view>
</template>

<script>
	import globalConfig from '@/config'
	import { mapGetters } from 'vuex'
	export default{
		data(){
			return {
				baseUrl: "",
				imageUrl: "",
				indexData: {},
				intervalId: null,
				currentDate: "",
				currentWeekday: "",
				currentTime: "",
				timeIntervalId: null,
				// 新增数据
				showPopup: false,
				nearbyTasks: [],
				gridSize: 30, // 网格大小
				overlapThreshold: 2 // 定义重叠的任务数量阈值
			}
		},
		mounted(){
			this.loadIndexData()
			this.updateDateTime()
			// 设置定时器，每秒更新一次时间
			this.timeIntervalId = setInterval(() => {
				this.updateDateTime()
			}, 1000)
		},
		computed: {
			...mapGetters(['user']),
			// 处理用户头像，返回默认头像或用户自定义头像
			userAvatar() {
				return this.user?.F_HeadIcon || '/static/img/profile.svg'
			}
		},
		onLoad(){
			this.baseUrl = globalConfig.baseUrl.replace('/api','');
			// 设置定时器，每10秒调用一次
			this.intervalId = setInterval(() => {
				this.loadIndexData();
			}, 10000);
		},
		onUnload() {
		    // 页面卸载时清除定时器
		    if (this.intervalId) {
		        clearInterval(this.intervalId);
		    }
		    if (this.timeIntervalId) {
		        clearInterval(this.timeIntervalId);
		    }
		},
		onShareAppMessage() {
		    return {
		      title: 'EHS-掌上观纹', // 自定义分享标题
		      path: '/pages/start/index', // 分享页面路径（默认当前页）
		      imageUrl: this.baseUrl+'/images/nideke.jpg', // 分享图片（可选）
		      success(res) {
		        console.log('分享成功', res);
		      },
		      fail(err) {
		        console.error('分享失败', err);
		      }
		    };
		},
		methods:{
			updateDateTime() {
				const now = new Date()
				
				// 格式化日期 YYYY-MM-DD
				const year = now.getFullYear()
				const month = String(now.getMonth() + 1).padStart(2, '0')
				const day = String(now.getDate()).padStart(2, '0')
				this.currentDate = `${year}-${month}-${day}`
				
				// 获取星期几
				const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
				this.currentWeekday = weekdays[now.getDay()]
				
				// 格式化时间 HH:MM:SS
				const hours = String(now.getHours()).padStart(2, '0')
				const minutes = String(now.getMinutes()).padStart(2, '0')
				const seconds = String(now.getSeconds()).padStart(2, '0')
				this.currentTime = `${hours}:${minutes}:${seconds}`
			},
			calculateLeftPosition(positionX) {
				// 当PostionX大于50时，减小倍数
				if (positionX > 65) {
					// 随着positionX增大，倍数逐渐减小
					const factor = 0.45 - ((positionX - 50) / 100) * 0.15; // 从0.45逐渐减小到0.3
					return positionX * 3650/2219 * Math.max(0.42, factor);
				} else {
					// 原来的计算方式
					return positionX * 3650/2219 * 0.45;
				}
			},
			loadIndexData(){
				this.$minApi.loadIndexData().then(res => {
					this.indexData = res;
					this.indexData.taskList.forEach(item => {
						item.denger = (new Date(item.MainTask.F_ExpectTime+" 23:59:59").getTime() < new Date().getTime() ||item.MainTask.contractorInfo.F_EnabledMark == false)
					})
					this.imageUrl = this.indexData.image 
					                ? this.baseUrl + this.indexData.image 
					                : '/static/img/202405230811051495.png';
					console.log(this.indexData.taskList)
				})
			},
			
			// 新增方法
			// 处理作业点击
			handleTaskTap(task, event) {
				// 检查附近是否有其他作业
				const nearbyTasks = this.findNearbyTasks(task);
				
				if (nearbyTasks.length <= 1) {
					// 只有一个作业，直接导航
					this.navigateToTask(task);
				} else {
					// 多个作业，显示弹窗
					this.showTaskPopup(nearbyTasks);
				}
			},
			
			// 查找附近的作业
			findNearbyTasks(task) {
				const threshold = 8; // 判断重叠的阈值
				return this.indexData.taskList.filter(item => {
					if (item.F_Id === task.F_Id) return true;
					
					// 计算两点之间的距离
					const distance = Math.sqrt(
						Math.pow(item.PostionX - task.PostionX, 2) + 
						Math.pow(item.PostionY - task.PostionY, 2)
					);
					
					return distance < threshold;
				});
			},
			
			// 显示作业弹窗
			showTaskPopup(tasks) {
				this.nearbyTasks = tasks;
				this.showPopup = true;
			},
			
			// 关闭弹窗
			closePopup() {
				this.showPopup = false;
				this.nearbyTasks = [];
			},
			
			// 导航到作业详情
			navigateToTask(task) {
				uni.navigateTo({
					url: `/pages/task/info?id=${task.F_Id}&type=group`
				});
				this.closePopup();
			}
		}
	}
</script>

<style lang="less">
	page{
		// background-color: #00984a;
		background-color: #FFFFFF;
	}
	.container{
		// background-color: #00984a;
		background-color: #FFFFFF;
		width: 100vh; /* 设置宽度为视口的高度 */
		height: 100vw; /* 设置高度为视口的宽度 */
		// transform: rotate(90deg);
		// transform-origin: left bottom;
		// top: 50%;
		// left: 0;
		// margin: 100vw 0 0 0;
		
		transform: rotate(270deg);
		transform-origin: right bottom;
		// bottom: 50%;
		right: 0;
		// margin: -100vw 0 0 -112vw;
		margin: -92vw 0 0 -116vw;
		padding: 10rpx 50rpx 20rpx 70rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		gap: 10rpx;
		.top{
			width: 93vh;
			position: relative;
			display: flex;
			flex-direction: row;
			color: #ccccc;
			justify-content: space-between;
			// padding-left: 50rpx;
			// padding-right: 50rpx;
			margin-top: -63rpx;margin-left: -14rpx;
			// color: #ffffff;
			.logo{
				width: 60rpx;
				height: 60rpx;
				position: absolute;
				left: 0;
				margin-left: 18rpx;
				margin-top: 10rpx;
			}
			.tool{
				position: absolute;
				top: 0;
				right: 0;
				margin-top: 39rpx;
				display: flex;
				flex-direction: row;
				justify-content: center;
				width: 49vh;
				gap: 60rpx;
				.item{
					display: flex;
					justify-content: center;
					align-items: center;
					gap: 10rpx;
					font-weight: 500;
					font-size: 16rpx;
					image{
						width: 45rpx;
						height: 45rpx;
					}
				}
			}
			.left{
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 34rpx;
				font-weight: 500;
				gap: 20rpx;
				image{
					width: 60rpx;
					height:60rpx;
				}
			}
			.right{
				font-size: 28rpx;
			}
		}
		.middle{
			display: flex;
			flex-direction: row;
			width: 90vh;
			gap: 20rpx;
			.left{
				width: 70vh;
				background: linear-gradient(111.57deg, #c5f2da 0%, #00984a 99.2%);
				padding: 10rpx;
				image{
					width: 68vh;
					height: 100%;
					// height: 76vw;
				}
				.task-list{
					display: flex;
					align-items: center;
					text-align: center;
					position: absolute;
					padding: 10rpx; /* 增大点击区域 */
					//border: 2rpx solid rgba(255, 255, 255, 1);
					.tubiao{
						text-align: center;
						color: #FFFFFF;
						line-height: 40rpx;
						width: 40rpx;
						height: 40rpx;
					}
				}

				.narmal {
					border: 2rpx solid rgba(255, 255, 255, 1);
					background: linear-gradient( 194.48deg, rgba(244, 255, 255, 0.2) 12.64%, rgba(215, 255, 236, 0.6) 88.67% ), linear-gradient(0deg, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.6));
					box-shadow: 0rpx 4rpx 20rpx 4rpx rgba(123, 185, 170, 0.6);
				}

				.denger {
					border: 2rpx solid rgba(220, 20, 60, 1);
					background: linear-gradient(194.48deg, rgba(255, 200, 200, 0.4) 12.64%, rgba(255, 150, 150, 0.8) 88.67%);
					box-shadow: 0rpx 4rpx 10rpx 4rpx rgba(220, 20, 60, 0.8);
					// box-shadow: 0rpx 4rpx 20rpx 4rpx rgba(220, 20, 60, 0.6);
				}
				
				/* 添加弹窗样式 */
				.task-popup {
					position: fixed;
					width: 500rpx;
					max-height: 600rpx;
					background: #fff;
					border-radius: 12rpx;
					box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.2);
					z-index: 100;
					left: 50%;
					top: 50%;
					transform: translate(-50%, -50%);
					
					.popup-header {
						display: flex;
						justify-content: space-between;
						padding: 20rpx;
						border-bottom: 1rpx solid #eee;
						font-size: 28rpx;
						background-color: #f8f8f8;
						border-top-left-radius: 12rpx;
						border-top-right-radius: 12rpx;
						font-weight: bold;
						
						.close-btn {
							font-size: 36rpx;
							line-height: 28rpx;
							color: #999;
							padding: 0 10rpx;
						}
					}
					
					.popup-content {
						max-height: 500rpx;
						
						.popup-task-item {
							padding: 16rpx 20rpx;
							border-bottom: 1rpx solid #f5f5f5;
							display: flex;
							justify-content: space-between;
							align-items: center;
							
							&.danger {
								border-left: 6rpx solid #ff4d4f;
								padding-left: 14rpx;
							}
							
							.task-info {
								display: flex;
								flex-direction: column;
								width: 100%;
								
								.task-icons {
									display: flex;
									gap: 8rpx;
									align-items: center;
									margin-bottom: 6rpx;
									flex-wrap: wrap;
									
									.tubiao-small {
										text-align: center;
										color: #FFFFFF;
										line-height: 36rpx;
										width: 36rpx;
										height: 36rpx;
										font-size: 24rpx;
										margin-right: 8rpx;
									}
									
									.icon-small {
										width: 32rpx;
										height: 32rpx;
									}
								}
								
								.task-type {
									font-size: 24rpx;
									color: #666;
								}
							}
						}
					}
				}
			}
			.right{
				width: 30vh;
				.data-list{
					flex: 8;
					display: flex;
					flex-wrap: wrap;
					gap:10rpx;
					.item{
						border: 5rpx solid #ffffff;
						display: flex;
						flex-direction: column;
						// background-color: rgba(26, 201, 255, 0.2980392156862745);
						// background: #88e22e;
						background: linear-gradient(194.48deg, rgba(200, 230, 230, 0.5) 12.64%, rgba(150, 230, 190, 0.9) 88.67%);
						// background: linear-gradient(194.48deg, rgba(244, 255, 255, 0.2) 12.64%, rgba(215, 255, 236, 0.6) 88.67%);
						opacity: 0.7;
						width: 46%;
						height: 24.4vw;
						padding: 10rpx;
						.title{
							font-size: 18rpx;
							color: #000000;
							font-weight: 400;							// color: #ffffff;
							border-top: 3rpx solid #00984a;
						}
						.shuju{
							display: flex;
							flex-direction: row;
							color: #000000;
							// color: #ffffff;
							align-items: center;
							gap: 10rpx;
							height: 20vw;
							.yuan{
								width: 40rpx;
								height: 40rpx;
							}
							.wenzi{
								font-size: 18rpx;
								display: flex;
								flex-direction: row;
								justify-content: center;
								gap: 4rpx;
								align-items: center;
								.num{
									font-size: 38rpx;
									font-weight: 400;
								}
							}
						}
					}
				}
			}
		}
		.bottom{
			display: flex;
			// color: #ffffff;
			color: #222347;
			.item-list{
				display: flex;
				gap: 10rpx;
				.item{
					align-items: center;
					display: flex;
					gap: 10rpx;
					.title{
						font-size: 18rpx;
					}
					
					image{
						width: 40rpx;
						height: 40rpx;
					}
				}
			}
		}
	}
</style>