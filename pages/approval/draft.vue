<template>
	<view class="container">
		<view class="warning-bar">
			<image src="/static/img/warning.svg" style="width: 48rpx;height: 48rpx;"></image>
			<text>草稿箱最多保存7天，超期将自动删除！</text>
		</view>
		<view class="draft-list">
			<view class="draft-item card" v-for="(draft, index) in drafts" :key="index">
				<view class="draft-title">{{ draft.flowName }}</view>
				<view class="draft-info">
					<text class="last-edited">最后编辑 {{ draft.lastModified }}</text>
					<view class="actions">
						<button class="cu-btn round sm modify-btn" @click="editDraft(draft)">编辑</button>
						<button class="cu-btn round sm delete-btn" @click="deleteDraft(draft.id)">删除</button>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				drafts: []
			}
		},
		onLoad() {
			this.fetchUserDrafts();
		},
		methods: {
			fetchUserDrafts() {
				this.$minApi.getUserDrafts().then(res => {
					if (res.state === 'success') {
						console.log(res.data);
						this.drafts = res.data.map(item => {
							
							return {
								id: item.F_Id,
								flowName: item.F_CustomName,
								lastModified: item.F_CreatorTime,
								schemeId: item.F_SchemeId,
								draftId: item.F_Id
							}
						});
					}
				})
			},
			editDraft(draft) {
				uni.navigateTo({
					url: '/pages/approval/Apply?draftId=' + draft.draftId + '&schemeId=' + draft.schemeId
				});
			},
			deleteDraft(draftId) {
				console.log('删除草稿:', draftId);
				this.$minApi.delDraft({
					id: draftId
				}).then(res => {
					if (res.code === 200) {
						this.fetchUserDrafts();
					}
				})
			}
		}
	}
</script>

<style lang="scss">
	.container {
		background-color: #f5f5f5;
		min-height: 100vh;
	}

	.warning-bar {
		background-color: #fff5f5;
		color: #e54d42;
		padding: 20rpx;
		text-align: center;
		font-size: 24rpx;
		display: flex;
		align-items: center;
		gap: 10rpx;
	}

	.draft-list {
		padding: 20rpx;
	}

	.draft-item {
		background-color: #ffffff;
		border-radius: 20rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
	}

	.draft-title {
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 20rpx;
		border-bottom: 1rpx solid #EBEFF2;
	}

	.draft-info {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.last-edited {
		font-size: 24rpx;
		color: #999;
	}

	.actions {
		display: flex;
		gap: 20rpx;
	}
	.modify-btn {
		background-color: #00984a;
		color: #00984a;
		font-size: 30rpx;
	}
	.delete-btn {
		background-color: #ff0000;
		color: #ff0000;
		font-size: 30rpx;
	}
</style>