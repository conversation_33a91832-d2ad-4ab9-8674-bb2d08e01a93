<template>
	<view class="transfer-container">
		<!-- 页面标题 -->
		<view class="header">
			<text class="title">转审处理</text>
		</view>

		<!-- 转审对象选择 -->
		<view class="form-item">
			<view class="item-label">
				<text class="required">*</text>
				<text class="label-text">转审对象</text>
			</view>
			<view class="item-content" @click="openUserSelector">
				<view v-if="selectedUser" class="selected-user">
					<image class="user-avatar" :src="selectedUser.avatar || '/static/img/profile.svg'" />
					<view class="user-info">
						<text class="user-name">{{ selectedUser.name }}</text>
						<text v-if="selectedUser.department" class="user-dept">{{ selectedUser.department }}</text>
					</view>
				</view>
				<view v-else class="placeholder">
					<text>请选择转审对象</text>
				</view>
				<text class="arrow">></text>
			</view>
		</view>

		<!-- 转审备注输入 -->
		<view class="form-item">
			<view class="item-label">
				<text class="label-text">转审备注</text>
			</view>
			<view class="item-content">
				<textarea
					v-model="transferRemark"
					class="remark-input"
					placeholder="请输入转审备注（选填）"
					maxlength="500"
					:show-count="false"
					auto-height
				/>
			</view>
		</view>

		<!-- 底部操作按钮 -->
		<view class="bottom-actions">
			<button class="action-btn cancel-btn" @click="handleCancel">取消</button>
			<button class="action-btn confirm-btn" :disabled="!canSubmit" @click="handleConfirm">确定转审</button>
		</view>

		<!-- 用户选择弹窗 -->
		<uni-popup ref="userSelectorPopup" type="bottom" :safe-area="false">
			<view class="user-selector-popup">
				<view class="popup-header">
					<text class="popup-title">选择转审对象</text>
					<text class="popup-close" @click="closeUserSelector">取消</text>
				</view>

				<scroll-view class="user-list" scroll-y>
					<view v-if="loadingUsers" class="loading-container">
						<text class="loading-text">加载中...</text>
					</view>

					<view v-else-if="availableUsers.length === 0" class="empty-container">
						<text class="empty-text">暂无可选人员</text>
					</view>

					<view v-else>
						<view v-for="(user, index) in availableUsers"
							:key="index"
							class="user-item"
							@click="selectUser(user)">
							<image class="user-item-avatar"
								:src="user.avatar || '/static/img/profile.svg'" />
							<view class="user-item-info">
								<text class="user-item-name">{{ user.name }}</text>
								<text v-if="user.department" class="user-item-dept">{{ user.department }}</text>
							</view>
							<view v-if="isUserSelected(user)" class="user-item-check">
								<text class="check-icon">✓</text>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
import api from '@/api/api.js'
import { mapState } from 'vuex'

export default {
	components: {},
	computed: {
		...mapState(['user']),
		canSubmit() {
			return this.selectedUser !== null
		}
	},
	data() {
		return {
			instanceId: null,
			selectedUser: null,
			transferRemark: '',
			availableUsers: [],
			loadingUsers: false
		}
	},
	onLoad(options) {
		if (options.id) {
			this.instanceId = options.id
			this.loadAllUsers()
		} else {
			uni.showToast({
				title: '缺少流程ID',
				icon: 'none'
			})
			setTimeout(() => {
				uni.navigateBack()
			}, 1500)
		}
	},
	methods: {
		// 加载所有用户
		async loadAllUsers() {
			this.loadingUsers = true
			try {
				const response = await api.apis.listUser()
				console.log('获取用户列表:', response)

				if (response.state === "success" && response.data) {
					// 处理用户数据
					let userList = []
					if (Array.isArray(response.data)) {
						userList = response.data
					} else if (response.data.data && Array.isArray(response.data.data)) {
						userList = response.data.data
					}

					// 映射用户数据
					this.availableUsers = userList.map(user => ({
						id: user.F_Id || user.id,
						name: user.F_RealName || user.name || '未知用户',
						avatar: user.F_HeadIcon || user.avatar || '/static/img/profile.svg',
						department: user.F_DepartmentName || user.department || ''
					}))

					console.log('处理后的用户列表:', this.availableUsers)
				} else {
					console.error('获取用户列表失败:', response)
					uni.showToast({
						title: response?.message || '获取用户列表失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('加载用户列表失败:', error)
				uni.showToast({
					title: '网络请求失败',
					icon: 'none'
				})
			} finally {
				this.loadingUsers = false
			}
		},

		// 打开用户选择器
		openUserSelector() {
			this.$refs.userSelectorPopup.open()
		},

		// 关闭用户选择器
		closeUserSelector() {
			this.$refs.userSelectorPopup.close()
		},

		// 选择用户
		selectUser(user) {
			this.selectedUser = user
			this.closeUserSelector()
		},

		// 判断用户是否已选中
		isUserSelected(user) {
			return this.selectedUser && this.selectedUser.id === user.id
		},

		// 处理取消
		handleCancel() {
			uni.navigateBack()
		},

		// 处理确认转审
		async handleConfirm() {
			if (!this.canSubmit) {
				uni.showToast({
					title: '请选择转审对象',
					icon: 'none'
				})
				return
			}

			try {
				uni.showLoading({
					title: '转审中...'
				})

				// 调用转审接口
				const transferParams = {
					flowInstanceId: this.instanceId,
					actionType: 'transfer',
					targetUserId: this.selectedUser.id,
					remark: this.transferRemark
				}

				const response = await api.apis.auditFlow(transferParams)

				if (response.state === "success") {
					uni.showToast({
						title: '转审成功',
						icon: 'success'
					})

					// 返回上一页
					setTimeout(() => {
						uni.navigateBack()
					}, 1500)
				} else {
					uni.showToast({
						title: response.message || '转审失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('转审失败:', error)
				uni.showToast({
					title: '转审失败',
					icon: 'none'
				})
			} finally {
				uni.hideLoading()
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.transfer-container {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 140rpx;
}

.header {
	background-color: #fff;
	padding: 30rpx;
	border-bottom: 1rpx solid #eee;

	.title {
		font-size: 32rpx;
		font-weight: 500;
		color: #333;
	}
}

.form-item {
	background-color: #fff;
	margin-top: 20rpx;
	padding: 30rpx;

	.item-label {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;

		.required {
			color: #ff4d4f;
			margin-right: 8rpx;
		}

		.label-text {
			font-size: 28rpx;
			color: #333;
			font-weight: 500;
		}
	}

	.item-content {
		display: flex;
		align-items: center;
		min-height: 80rpx;

		.selected-user {
			display: flex;
			align-items: center;
			flex: 1;

			.user-avatar {
				width: 80rpx;
				height: 80rpx;
				border-radius: 50%;
				margin-right: 20rpx;
				background-color: #f0f0f0;
			}

			.user-info {
				flex: 1;

				.user-name {
					font-size: 30rpx;
					color: #333;
					display: block;
					margin-bottom: 8rpx;
				}

				.user-dept {
					font-size: 26rpx;
					color: #666;
				}
			}
		}

		.placeholder {
			flex: 1;
			font-size: 28rpx;
			color: #999;
		}

		.arrow {
			font-size: 28rpx;
			color: #ccc;
		}
	}

	.remark-input {
		width: 100%;
		min-height: 120rpx;
		padding: 20rpx;
		border: 1rpx solid #e8e8e8;
		border-radius: 8rpx;
		background-color: #fafafa;
		font-size: 28rpx;
		line-height: 1.5;
	}
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	display: flex;
	justify-content: center;
	gap: 20rpx;
	padding: 20rpx;
	background-color: #fff;
	border-top: 1rpx solid #eee;

	.action-btn {
		flex: 1;
		height: 80rpx;
		border-radius: 8rpx;
		font-size: 32rpx;

		&.cancel-btn {
			background-color: #f5f5f5;
			color: #666;
		}

		&.confirm-btn {
			background-color: #007aff;
			color: #fff;

			&[disabled] {
				background-color: #ccc;
				color: #999;
			}
		}
	}
}

// 用户选择弹窗
.user-selector-popup {
	background: #fff;
	border-radius: 24rpx 24rpx 0 0;
	height: 70vh;
	display: flex;
	flex-direction: column;

	.popup-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;

		.popup-title {
			font-size: 32rpx;
			font-weight: 500;
			color: #333;
		}

		.popup-close {
			font-size: 28rpx;
			color: #666;
			padding: 10rpx 20rpx;
		}
	}

	.user-list {
		flex: 1;
		padding: 0 30rpx;

		.loading-container,
		.empty-container {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 300rpx;

			.loading-text,
			.empty-text {
				font-size: 28rpx;
				color: #999;
			}
		}

		.user-item {
			display: flex;
			align-items: center;
			padding: 24rpx 0;
			border-bottom: 1rpx solid #f8f8f8;

			&:active {
				background: #f8f8f8;
			}

			.user-item-avatar {
				width: 80rpx;
				height: 80rpx;
				border-radius: 50%;
				margin-right: 24rpx;
				background: #f0f0f0;
			}

			.user-item-info {
				flex: 1;
				display: flex;
				flex-direction: column;

				.user-item-name {
					font-size: 30rpx;
					color: #333;
					margin-bottom: 8rpx;
				}

				.user-item-dept {
					font-size: 26rpx;
					color: #999;
				}
			}

			.user-item-check {
				width: 48rpx;
				height: 48rpx;
				border-radius: 50%;
				background: #1890ff;
				display: flex;
				align-items: center;
				justify-content: center;

				.check-icon {
					color: #fff;
					font-size: 28rpx;
				}
			}
		}
	}
}
</style>