<template>
	<view class="container">
		<view class="form-section">
			<view class="title">表单信息</view>
			<view class="content">
				<!-- 动态表单 -->
				<block v-for="(field, index) in formContent" :key="index">
					<view :class="['item', (field.tag === 'radio' && field.options && field.options.length > 2) || field.tag === 'checkbox' ? 'item-vertical' : '']"
						v-if="isVisible(field)">
						<!-- 只有非动态分组/table类型才显示label -->
						<label v-if="field.tag !== 'dynamicGroup' && field.tag !== 'table'" class="label">
							{{ field.label }}<text v-if="isRequired(field)" class="required">*</text>
						</label>
						
						<!-- Input -->
						<input
							v-if="field.tag === 'input'"
							class="text"
							:placeholder="'请输入' + field.label"
							v-model="formData[field.id]"
						/>
						<!-- Textarea -->
						<textarea
							v-if="field.tag === 'textarea'"
							class="text"
							:placeholder="'请输入' + field.label"
							v-model="formData[field.id]"
							auto-height
						/>
						<!-- Select -->
						<picker
							v-if="field.tag === 'select'"
							mode="selector"
							:range="field.options || []"
							range-key="text"
							:value="getPickerIndex(field)"
							@change="handlePickerChange($event, field)"
						>
							<view class="uni-input">
								{{ getPickerText(field) || ('请选择' + field.label) }}
							</view>
						</picker>
						<!-- Radio -->
						<view v-if="field.tag === 'radio'" class="custom-control-container">
							<!-- Render as Switch if only two options -->
							<switch v-if="field.options.length === 2"
								:checked="formData[field.id] === field.options.value"
								@change="handleSwitchChange($event, field)"
								color="#00984a"
							/>
							<!-- Render as Buttons if more than two options -->
							<view v-else class="option-button-container-vertical">
								<view class="option-button-group">
									<button
										v-for="option in field.options"
										:key="option.value"
										:class="['option-button', { 'active': formData[field.id] === option.value }]"
										@click="handleRadioAsButton(field, option)">
										{{ option.text }}
									</button>
								</view>
							</view>
						</view>
						<!-- Checkbox -->
						<view v-if="field.tag === 'checkbox'" class="option-button-container-vertical">
							<view class="option-button-group">
								<button
									v-for="option in field.options"
									:key="option.value"
									:class="['option-button', { 'active': (formData[field.id] || []).includes(option.value) }]"
									@click="handleCheckboxAsButton(field, option)">
									{{ option.text }}
								</button>
							</view>
						</view>
						<!-- Date -->
						<picker
							v-if="field.tag === 'date'"
							mode="date"
							:value="formData[field.id]"
							@change="handleDateChange($event, field)"
						>
							<view class="uni-input">
								{{ formData[field.id] || '请选择日期' }}
							</view>
						</picker>
						<!-- Upload -->
						<view v-if="field.tag === 'upload'" class="file-list">
							<view class="file-upload" @click="uploadFile(field)">
								<text class="upload-icon">+</text>
								<text class="upload-text">上传文件</text>
							</view>
							<view v-for="(file, fileIndex) in formData[field.id]" :key="fileIndex" class="file-item">
								<text class="file-name-text">{{ file.name }}</text>
								<text class="delete-btn" @click.stop="removeFile(field, fileIndex)">×</text>
							</view>
						</view>
						<!-- Dynamic Group (Card Style) - dynamicGroup 专用 -->
						<view v-if="field.tag === 'dynamicGroup'" class="dynamic-group-card">
							<view class="dynamic-group-title">{{ field.label }}</view>
							<!-- 空组占位 -->
							<template v-if="!formData[field.id] || !Array.isArray(formData[field.id]) || formData[field.id].length === 0 || !formContent.length">
								<view class="dynamic-group-wrapper">
									<view class="dynamic-group-content">
										<block v-for="(innerField, innerIndex) in field.fields" :key="innerIndex">
											<view :class="['dynamic-group-item', innerField.tag === 'textarea' ? 'vertical' : '']">
												<label class="dynamic-label">
													{{ innerField.label }}<text v-if="innerField.required" class="required">*</text>
												</label>
												<view class="dynamic-picker">请先添加{{ field.label }}</view>
											</view>
										</block>
									</view>
								</view>
							</template>
							<!-- 有组渲染 -->
							<template v-else>
								<view v-for="(groupItem, groupIndex) in (formData[field.id] || [])" :key="`${field.id}-${groupIndex}`" class="dynamic-group-wrapper">
									<view class="dynamic-group-header" v-if="formData[field.id].length > 1">
										<text class="group-index-text">{{ field.label }} {{ groupIndex + 1 }}</text>
										<text v-if="groupIndex > 0" class="delete-group-btn" @click="removeGroupItem(field, groupIndex)">删除</text>
									</view>
									<view class="dynamic-group-content">
										<block v-for="(innerField, innerIndex) in field.fields" :key="innerIndex">
											<view :class="['dynamic-group-item', innerField.tag === 'textarea' ? 'vertical' : '']"
												v-if="isGroupFieldVisible(innerField, groupItem || {})">
												<label class="dynamic-label">
													{{ innerField.label }}<text v-if="innerField.required" class="required">*</text>
												</label>
												
												<!-- Input -->
												<input
													v-if="innerField.tag === 'input'"
													class="dynamic-input"
													:placeholder="'请输入' + innerField.label"
													:value="getDynamicGroupValue(field, groupIndex, innerField,groupItem)"
													@input="handleDynamicGroupInput($event, field, groupIndex, innerField)"
												/>
												<!-- Textarea -->
												<textarea
													v-if="innerField.tag === 'textarea'"
													class="dynamic-textarea"
													:placeholder="'请输入' + innerField.label"
													:value="getDynamicGroupValue(field, groupIndex, innerField,groupItem)"
													@input="handleDynamicGroupInput($event, field, groupIndex, innerField)"
													auto-height
												/>
												<!-- Select -->
												<picker
													v-if="innerField.tag === 'select'"
													mode="selector"
													:range="innerField.options || []"
													range-key="text"
													:value="getGroupPickerIndex(groupItem, innerField,groupIndex)"
													@change="handleArrayGroupPickerChange($event, field, groupIndex, innerField)"
												>
													<view class="dynamic-picker">
														{{ getGroupPickerText(groupItem, innerField, groupIndex) || ('请选择' + innerField.label) }}
													</view>
												</picker>
												<!-- Radio -->
												<view v-if="innerField.tag === 'radio'" class="dynamic-radio-container">
													<switch v-if="innerField.options.length === 2"
														:checked="groupItem[innerField.id] === innerField.options.value"
														@change="handleArrayGroupSwitchChange($event, field, groupIndex, innerField)"
														color="#00984a"
													/>
													<view v-else class="option-button-group">
														<button
															v-for="option in innerField.options"
															:key="option.value"
															:class="['option-button', { 'active': groupItem[innerField.id] === option.value }]"
															@click="handleArrayGroupRadioAsButton(field, groupIndex, innerField, option)">
															{{ option.text }}
														</button>
													</view>
												</view>
												<!-- Date -->
												<picker
													v-if="innerField.tag === 'date'"
													mode="date"
													:value="groupItem[innerField.id]"
													@change="handleArrayGroupDateChange($event, field, groupIndex, innerField)"
												>
													<view class="dynamic-picker">
														{{ groupItem[innerField.id] || '请选择日期' }}
													</view>
												</picker>
												<!-- Upload -->
												<view v-if="innerField.tag === 'upload'" class="dynamic-file-list">
													<view class="dynamic-file-upload" @click="uploadFileInGroup(field, groupIndex, innerField)">
														<text class="upload-icon">+</text>
														<text class="upload-text">上传文件</text>
													</view>
													<view v-for="(file, fileIndex) in (groupItem[innerField.id] || [])" :key="fileIndex" class="dynamic-file-item">
														<text class="file-name-text">{{ file.name }}</text>
														<text class="delete-btn" @click.stop="removeFileInGroup(field, groupIndex, innerField, fileIndex)">×</text>
													</view>
												</view>
											</view>
										</block>
									</view>
								</view>
							</template>
							<!-- Add button for dynamic groups -->
							<view class="add-group-btn" @click="addGroupItem(field)">
								<text class="add-icon">+</text>
								<text class="add-text">添加{{ field.label }}</text>
							</view>
						</view>

						<!-- Table (Card Style) - table 专用 -->
						<view v-else-if="field.tag === 'table'" class="dynamic-group-card">
							<view class="dynamic-group-title">{{ field.label }}</view>
							<view class="dynamic-group-content">
								<block v-for="(innerField, innerIndex) in field.fields" :key="innerIndex">
									<view :class="['dynamic-group-item', innerField.tag === 'textarea' ? 'vertical' : '']"
										v-if="isGroupFieldVisible(innerField, formData[field.id])">
										<label class="dynamic-label">
											{{ innerField.label }}<text v-if="innerField.required" class="required">*</text>
										</label>
										
										<!-- Input -->
										<input
											v-if="innerField.tag === 'input'"
											class="dynamic-input"
											:placeholder="'请输入' + innerField.label"
											v-model="formData[field.id][innerField.id]"
										/>
										<!-- Textarea -->
										<textarea
											v-if="innerField.tag === 'textarea'"
											class="dynamic-textarea"
											:placeholder="'请输入' + innerField.label"
											v-model="formData[field.id][innerField.id]"
											auto-height
										/>
										<!-- Select -->
										<picker
											v-if="innerField.tag === 'select'"
											mode="selector"
											:range="innerField.options || []"
											range-key="text"
											:value="getGroupPickerIndex(formData[field.id] || {}, innerField)"
											@change="handleGroupPickerChange($event, field, innerField)"
										>
											<view class="dynamic-picker">
												{{ getGroupPickerText(formData[field.id] || {}, innerField) || ('请选择' + innerField.label) }}
											</view>
										</picker>
										<!-- Radio -->
										<view v-if="innerField.tag === 'radio'" class="dynamic-radio-container">
											<switch v-if="innerField.options.length === 2"
												:checked="formData[field.id][innerField.id] === innerField.options.value"
												@change="handleGroupSwitchChange($event, field, innerField)"
												color="#00984a"
											/>
											<view v-else class="option-button-group">
												<button
													v-for="option in innerField.options"
													:key="option.value"
													:class="['option-button', { 'active': formData[field.id][innerField.id] === option.value }]"
													@click="handleGroupRadioAsButton(field, innerField, option)">
													{{ option.text }}
												</button>
											</view>
										</view>
										<!-- Date -->
										<picker
											v-if="innerField.tag === 'date'"
											mode="date"
											:value="formData[field.id][innerField.id]"
											@change="handleGroupDateChange($event, field, innerField)"
										>
											<view class="dynamic-picker">
												{{ formData[field.id][innerField.id] || '请选择日期' }}
											</view>
										</picker>
									</view>
								</block>
							</view>
						</view>
					</view>
				</block>
			</view>
		</view>
		<view class="approval-flow-section">
			<view class="title">审批流程</view>
			<view class="content">
				<approver-selector
					v-if="schemeContent"
					:scheme-content="schemeContent"
					v-model="approverData"
					ref="approverSelector"
				/>
			</view>
		</view>
		<view class="action-bar">
			<button class="save-draft-btn" @click="saveDraft">保存草稿</button>
			<button class="submit-btn" @click="submit">提交</button>
		</view>
	</view>
</template>

<script>
	import ApproverSelector from '@/components/approver-selector/approver-selector.vue';
	import globalConfig from '@/config'
	export default {
		components: {
			ApproverSelector
		},
		data() {
			return {
				baseUrl: "",
				flowCode: null,
				schemeId: null,
				draftId: null,
				formContent: [],
				schemeContent: null,
				formData: {},
				approverData: {}
			};
		},
		onLoad(options) {
			this.baseUrl = globalConfig.baseUrl.replace("/api","");
			// 从 options 获取 flowCode 或 draftId/schemeId
			if (options.flowCode) {
				this.flowCode = options.flowCode;
			}
			if (options.draftId) {
				this.draftId = options.draftId;
			}
			if (options.schemeId) {
				this.schemeId = options.schemeId;
			}
			this.fetchFormAndFlow();
		},
		mounted() {
			// 确保 table 和 dynamicGroup 类型的字段在组件挂载后被正确初始化
			this.$nextTick(() => {
				console.log(`[DEBUG-MOUNTED] 🚀 Component mounted, checking formContent:`, this.formContent.length);
				console.log(`[DEBUG-MOUNTED] 📊 Current formData:`, JSON.stringify(this.formData));
				
				this.formContent.forEach(field => {
					if (field.tag === 'table' && (!this.formData[field.id] || typeof this.formData[field.id] !== 'object')) {
						console.log(`[DEBUG-MOUNTED] 📋 Initializing table field ${field.label}`);
						const tableItem = {};
						if (field.fields && Array.isArray(field.fields)) {
							field.fields.forEach(innerField => {
								tableItem[innerField.id] = innerField.defaultValue || null;
							});
						}
						this.$set(this.formData, field.id, tableItem);
					} else if (field.tag === 'dynamicGroup') {
						console.log(`[DEBUG-MOUNTED] 🔍 Checking dynamicGroup field ${field.label}:`, {
							exists: !!this.formData[field.id],
							isArray: Array.isArray(this.formData[field.id]),
							length: this.formData[field.id] ? this.formData[field.id].length : 0,
							currentValue: this.formData[field.id]
						});
						
						if (!this.formData[field.id] || !Array.isArray(this.formData[field.id]) || this.formData[field.id].length === 0) {
							console.log(`[DEBUG-MOUNTED] 🔧 Initializing dynamicGroup field ${field.label}`);
							const newItem = {};
							if (field.fields && Array.isArray(field.fields)) {
								field.fields.forEach(innerField => {
									newItem[innerField.id] = innerField.defaultValue || null;
								});
							}
							console.log(`[DEBUG-MOUNTED] 📝 Created newItem:`, newItem);
							this.$set(this.formData, field.id, [newItem]);
							console.log(`[DEBUG-MOUNTED] ✅ Set formData[${field.id}]:`, this.formData[field.id]);
						} else {
							console.log(`[DEBUG-MOUNTED] ✅ DynamicGroup ${field.label} already initialized`);
						}
					}
				});
				
				console.log(`[DEBUG-MOUNTED] 🎯 Final formData after mounted:`, JSON.stringify(this.formData));
			});
		},
		methods: {
			async fetchFormAndFlow() {
				uni.showLoading({ title: '加载中...' });
				try {
					let apiResponse;
					if (this.flowCode) {
						apiResponse = await this.$minApi.getFlowForm({ code: this.flowCode });
					} else if (this.schemeId) {
						apiResponse = await this.$minApi.getFlowFormById({ id: this.schemeId });
					} else {
						throw new Error("缺少必要的流程参数");
					}

					let res = apiResponse;
					if (typeof res === 'string') {
						try {
							res = JSON.parse(res);
						} catch (e) {
							console.error("无法解析API响应:", e);
							throw new Error("无效的服务器响应格式");
						}
					}

					if (res && res.F_FrmContent) {
						uni.setNavigationBarTitle({
							title: res.F_SchemeName || '发起审批'
						});
						this.formContent = JSON.parse(res.F_FrmContent || '[]');
						this.schemeContent = JSON.parse(res.F_SchemeContent || '{}');
						
						await this.initializeFormData();

						if (this.draftId) {
							await this.loadDraft();
						}
					} else {
						uni.showToast({ title: (res && res.message) || '表单加载失败', icon: 'none' });
					}
				} catch (error) {
					console.error("fetchFormAndFlow error:", error);
					uni.showToast({ title: '加载失败，请稍后重试', icon: 'none' });
				} finally {
					uni.hideLoading();
				}
			},
			async initializeFormData() {
				const initialData = {};
				
				// First, process and standardize all fields
				this.formContent.forEach(field => {
					// Normalize top-level field remote datasource config (support api* and remote* keys)
					if (!field.remoteUrl && field.apiUrl) {
						field.remoteUrl = field.apiUrl;
					}
					if (!field.remoteMethod && field.apiMethod) {
						field.remoteMethod = field.apiMethod;
					}
					if (!field.remoteOptionText && field.dataMapping && field.dataMapping.textField) {
						field.remoteOptionText = field.dataMapping.textField;
					}
					if (!field.remoteOptionValue && field.dataMapping && field.dataMapping.valueField) {
						field.remoteOptionValue = field.dataMapping.valueField;
					}

					// Standardize dynamicGroup/table fields from 'columns' to 'fields'
					if ((field.tag === 'dynamicGroup' || field.tag === 'table') && field.columns) {
						field.fields = field.columns.map(col => ({
							id: col.field, // Use 'field' as 'id' for v-model
							label: col.title,
							tag: col.type || 'input', // Default to input if type is missing
							options: col.options || [],
							required: col.required || false,
							expression: col.expression || null,
							defaultValue: col.defaultValue || null,
							// Support both old and new API datasource formats
							datasourceType: col.datasourceType || 'local',
							apiUrl: col.apiUrl || '',
							apiMethod: col.apiMethod || 'GET',
							dataMapping: col.dataMapping || null,
							remoteUrl: col.apiUrl || '',
							remoteMethod: col.apiMethod || 'GET',
							remoteOptionText: (col.dataMapping && col.dataMapping.textField) || '',
							remoteOptionValue: (col.dataMapping && col.dataMapping.valueField) || ''
						}));
					}
					
					// Also process fields array if it exists (for dynamicGroup)
					if (field.fields && Array.isArray(field.fields)) {
						field.fields = field.fields.map(innerField => ({
							...innerField,
							// Support both old and new API datasource formats
							datasourceType: innerField.datasourceType || 'local',
							apiUrl: innerField.apiUrl || '',
							apiMethod: innerField.apiMethod || 'GET',
							dataMapping: innerField.dataMapping || null,
							remoteUrl: innerField.apiUrl || '',
							remoteMethod: innerField.apiMethod || 'GET',
							remoteOptionText: (innerField.dataMapping && innerField.dataMapping.textField) || '',
							remoteOptionValue: (innerField.dataMapping && innerField.dataMapping.valueField) || ''
						}));
					}
				});
				
				// Now load remote data for fields with API datasource
				await this.loadRemoteOptions();
				
				// Then initialize form data with default values
				this.formContent.forEach(field => {

					let defaultValue;
					switch (field.tag) {
						case 'checkbox':
						case 'upload':
							defaultValue = [];
							break;
						case 'radio':
							// For radio with 2 options (rendered as switch), set default to second option (usually "否")
							// This ensures dependent fields are hidden by default
							if (field.options && field.options.length === 2) {
								defaultValue = field.defaultValue || field.options.value;
							} else {
								defaultValue = field.defaultValue || null;
							}
							break;
						case 'dynamicGroup':
							// Dynamic groups should be initialized as an array with one item
							console.log(`[DEBUG-INIT] 🔧 Initializing dynamicGroup ${field.label}:`, {
								fieldId: field.id,
								hasFields: !!(field.fields && Array.isArray(field.fields)),
								fieldsCount: field.fields ? field.fields.length : 0,
								fields: field.fields
							});
							
							const newItem = {};
							if (field.fields && Array.isArray(field.fields)) {
								field.fields.forEach(innerField => {
									let innerDefaultValue;
									if (innerField.tag === 'radio' && innerField.options && innerField.options.length === 2) {
										innerDefaultValue = innerField.defaultValue || innerField.options[1].value;
									} else if (innerField.tag === 'upload') {
										innerDefaultValue = [];
									} else {
										innerDefaultValue = innerField.defaultValue || null;
									}
									console.log(`[DEBUG-INIT] 📝 Setting ${innerField.id} = ${innerDefaultValue}`);
									this.$set(newItem, innerField.id, innerDefaultValue);
								});
							}
							defaultValue = [newItem]; // Initialize as array with one item
							console.log(`[DEBUG-INIT] ✅ Initialized dynamicGroup ${field.label}:`, defaultValue);
							break;
						case 'table':
							// Tables remain as single object for backward compatibility
							const tableItem = {};
							if (field.fields && Array.isArray(field.fields)) {
								field.fields.forEach(innerField => {
									if (innerField.tag === 'radio' && innerField.options && innerField.options.length === 2) {
										this.$set(tableItem, innerField.id, innerField.defaultValue || innerField.options.value);
									} else if (innerField.tag === 'upload') {
										this.$set(tableItem, innerField.id, []);
									} else {
										this.$set(tableItem, innerField.id, innerField.defaultValue || null);
									}
								});
							}
							defaultValue = tableItem; // Initialize as a single object
							console.log(`[initializeFormData] Initialized table ${field.label}:`, defaultValue);
							// 确保 table 类型的字段被正确初始化
							this.$set(initialData, field.id, tableItem);
							break;
						default:
							defaultValue = field.defaultValue || null;
					}
					// 只有当 table 类型没有在上面的 case 中设置时才设置
					if (field.tag !== 'table') {
						this.$set(initialData, field.id, defaultValue);
					}
				});
				this.formData = initialData;
				
				// 确保 table 类型的字段被正确设置到 formData
				this.$nextTick(() => {
					this.formContent.forEach(field => {
						if (field.tag === 'table' && (!this.formData[field.id] || Object.keys(this.formData[field.id]).length === 0)) {
							console.log(`[initializeFormData] Re-initializing empty table field ${field.label}`);
							const tableItem = {};
							if (field.fields && Array.isArray(field.fields)) {
								field.fields.forEach(innerField => {
									if (innerField.tag === 'upload') {
										tableItem[innerField.id] = [];
									} else {
										tableItem[innerField.id] = innerField.defaultValue || null;
									}
								});
							}
							this.$set(this.formData, field.id, tableItem);
						}
					});
					this.$forceUpdate();
				});
			},
			async loadRemoteOptions() {
				console.log('Starting to load remote options...');
				// Load options for all fields with remote/api datasource
				const promises = [];
				
				for (const field of this.formContent) {
					// Support both 'remote' and 'api' datasource types
					// Also check for apiUrl in addition to remoteUrl
					const hasRemoteData = (field.datasourceType === 'remote' || field.datasourceType === 'api') &&
					                      (field.remoteUrl || field.apiUrl);
					if (hasRemoteData) {
						console.log(`Found API field: ${field.label}, datasourceType: ${field.datasourceType}, url: ${field.remoteUrl || field.apiUrl}`);
						promises.push(this.loadFieldRemoteOptions(field));
					}
					
					// Check fields within dynamic groups/tables
					if ((field.tag === 'dynamicGroup' || field.tag === 'table')) {
						// Check fields (processed format)
						if (field.fields && Array.isArray(field.fields)) {
							for (const innerField of field.fields) {
								if ((innerField.datasourceType === 'remote' || innerField.datasourceType === 'api') && innerField.remoteUrl) {
									console.log(`Found API field in group: ${innerField.label}, datasourceType: ${innerField.datasourceType}, url: ${innerField.remoteUrl}`);
									promises.push(this.loadFieldRemoteOptions(innerField));
								}
							}
						}
					}
				}
				
				console.log(`Total API calls to make: ${promises.length}`);
				
				if (promises.length > 0) {
					await Promise.all(promises);
				}
			},
			async loadFieldRemoteOptions(field) {
				try {
					const url = field.remoteUrl || field.apiUrl;
					const method = (field.remoteMethod || field.apiMethod || 'GET').toUpperCase();
					
					if (!url) {
						console.log(`No URL for field ${field.label || field.id}`);
						return;
					}
					
					console.log(`Loading remote options for ${field.label || field.id} from ${url} using ${method}`);
					
					// Get user token
					const user = this.$store.getters.user;
					const token = user && user.ApiToken ? user.ApiToken : '';
					
					const fullUrl = this.baseUrl + url;
					
					console.log(`Full URL: ${fullUrl}`);
					
					// Make direct HTTP request
					const response = await new Promise((resolve, reject) => {
						uni.request({
							url: fullUrl,
							method: method,
							header: {
								'WC-Token': token,
								'Content-Type': 'application/json'
							},
							data: {},
							success: (res) => {
								console.log(`Response for ${field.label || field.id}:`, res);
								if (res.statusCode === 200) {
									resolve(res.data);
								} else {
									reject(new Error(`HTTP ${res.statusCode}: ${res.data}`));
								}
							},
							fail: (err) => {
								console.error(`Request failed for ${field.label || field.id}:`, err);
								reject(err);
							}
						});
					});
					
					if (response) {
						// Handle different response formats
						let data = response.data || response;
						
						// Parse the remote option text and value paths
						const textPath = field.remoteOptionText || (field.dataMapping && field.dataMapping.textField) || 'text';
						const valuePath = field.remoteOptionValue || (field.dataMapping && field.dataMapping.valueField) || 'value';
						
						// Convert response data to options format
						const options = this.parseRemoteData(data, textPath, valuePath);
						
						console.log(`Loaded ${options.length} options for ${field.label || field.id}`);
						
						// Update field options
						this.$set(field, 'options', options);
					}
				} catch (error) {
					console.error(`Failed to load remote options for field ${field.label || field.id}:`, error);
					// Set empty options on error
					this.$set(field, 'options', []);
				}
			},
			async loadColumnRemoteOptions(column) {
				try {
					const url = column.remoteUrl || column.apiUrl;
					const method = (column.remoteMethod || column.apiMethod || 'GET').toUpperCase();
					
					if (!url) {
						console.log(`No URL for column ${column.title || column.field}`);
						return;
					}
					
					console.log(`Loading remote options for column ${column.title || column.field} from ${url} using ${method}`);
					
					// Get user token
					const user = this.$store.getters.user;
					const token = user && user.ApiToken ? user.ApiToken : '';
					
					// Build full URL
					const fullUrl = this.baseUrl + url;
					
					console.log(`Full URL: ${fullUrl}`);
					
					// Make direct HTTP request
					const response = await new Promise((resolve, reject) => {
						uni.request({
							url: fullUrl,
							method: method,
							header: {
								'WC-Token': token,
								'Content-Type': 'application/json'
							},
							data: {},
							success: (res) => {
								console.log(`Response for ${column.title || column.field}:`, res);
								if (res.statusCode === 200) {
									resolve(res.data);
								} else {
									reject(new Error(`HTTP ${res.statusCode}: ${res.data}`));
								}
							},
							fail: (err) => {
								console.error(`Request failed for ${column.title || column.field}:`, err);
								reject(err);
							}
						});
					});
					
					if (response) {
						// Handle different response formats
						let data = response.data || response;
						
						// Parse the remote option text and value paths
						const textPath = column.remoteOptionText || (column.dataMapping && column.dataMapping.textField) || 'text';
						const valuePath = column.remoteOptionValue || (column.dataMapping && column.dataMapping.valueField) || 'value';
						
						// Convert response data to options format
						const options = this.parseRemoteData(data, textPath, valuePath);
						
						console.log(`Loaded ${options.length} options for column ${column.title || column.field}`);
						
						// Update column options
						this.$set(column, 'options', options);
					}
				} catch (error) {
					console.error(`Failed to load remote options for column ${column.title || column.field}:`, error);
					// Set empty options on error
					this.$set(column, 'options', []);
				}
			},
			parseRemoteData(data, textPath, valuePath) {
				// Ensure data is an array
				const dataArray = Array.isArray(data) ? data : [data];
				
				return dataArray.map(item => {
					// For simple field names (no dots), access directly
					let text, value;
					
					if (!textPath.includes('.')) {
						text = item[textPath];
					} else {
						text = this.getValueByPath(item, textPath);
					}
					
					if (!valuePath.includes('.')) {
						value = item[valuePath];
					} else {
						value = this.getValueByPath(item, valuePath);
					}
					
					return {
						text: String(text || ''),
						value: String(value || ''),
						checked: false
					};
				});
			},
			getValueByPath(obj, path) {
				// Support nested path like "options.data.dictName"
				const keys = path.split('.');
				let result = obj;
				
				for (const key of keys) {
					if (result && typeof result === 'object') {
						result = result[key];
					} else {
						return null;
					}
				}
				
				return result;
			},
			evaluateExpression(expression, context) {
				if (!expression || typeof expression !== 'string') {
					return true;
				}
				const parts = expression.match(/(.+?)\s*([=!]=)\s*(.+)/);
				if (!parts) {
					console.warn(`Unsupported expression format: ${expression}`);
					return false;
				}
				const [, key, operator, valueStr] = parts;
				const keyInContext = key.trim();
				const expectedValue = valueStr.trim().replace(/^['"]|['"]$/g, '');
				const actualValue = context[keyInContext];

				if (operator === '==') {
					return String(actualValue) === String(expectedValue);
				} else if (operator === '!=') {
					return String(actualValue) !== String(expectedValue);
				}
				return false;
			},
			isVisible(field) {
				if (!field.expression) {
					return true;
				}
				return this.evaluateExpression(field.expression, this.formData);
			},
			isRequired(field) {
				if (typeof field.required === 'boolean') {
					return field.required;
				}
				if (typeof field.required === 'string') {
					return this.evaluateExpression(field.required, this.formData);
				}
				return false;
			},
			getPickerText(field) {
				const selectedValue = this.formData[field.id];
				if (selectedValue === null || selectedValue === undefined || selectedValue === '') {
					return '';
				}
				if (!field.options || field.options.length === 0) {
					// 选项未加载时先展示已保存的值作为占位
					console.log(`[getPickerText] field: ${field.label}, options not loaded yet, showing value: ${selectedValue}`);
					return String(selectedValue);
				}
				// 确保比较时类型一致（都转为字符串）
				const selectedOption = field.options.find(opt =>
					String(opt.value) === String(selectedValue)
				);
				console.log(`[getPickerText] field: ${field.label}, value: ${selectedValue}, options:`, field.options, `found: ${selectedOption ? selectedOption.text : 'not found'}`);
				return selectedOption ? selectedOption.text : '';
			},
			getPickerIndex(field) {
				const selectedValue = this.formData[field.id];
				const hasOptions = Array.isArray(field.options) && field.options.length > 0;
				if (!hasOptions) {
					// 返回 0 以保证 picker 内部状态有效（避免 -1 导致选择后不刷新）
					return 0;
				}
				if (selectedValue === null || selectedValue === undefined || selectedValue === '') {
					// 未选择时也返回 0，只作为滚轮默认定位，不影响展示文本（展示文本由 getPickerText 控制）
					return 0;
				}
				// 查找选中值的索引
				const index = field.options.findIndex(opt =>
					String(opt.value) === String(selectedValue)
				);
				console.log(`[getPickerIndex] field: ${field.label}, value: ${selectedValue}, index: ${index}`);
				return index >= 0 ? index : 0;
			},
			handlePickerChange(e, field) {
				const selectedIndex = parseInt(e.detail.value);
				if (field.options && field.options[selectedIndex]) {
					const selectedOption = field.options[selectedIndex];
					const selectedValue = selectedOption.value;
					console.log(`[handlePickerChange] field: ${field.label}, index: ${selectedIndex}, value: ${selectedValue}, text: ${selectedOption.text}`);
					// 直接设置值，确保响应式更新
					this.formData[field.id] = selectedValue;
					// 使用 Vue.set 确保响应式
					this.$set(this.formData, field.id, selectedValue);
					// 强制更新视图
					this.$nextTick(() => {
						this.$forceUpdate();
					});
				}
			},
			handleSwitchChange(e, field) {
				const isChecked = e.detail.value;
				if (field.options && field.options.length >= 2) {
					// Assumes first option is "true" value (e.g., '是'), second is "false" value (e.g., '否')
					const targetValue = isChecked ? field.options[0].value : field.options[1].value;
					this.$set(this.formData, field.id, targetValue);
				}
			},
			handleRadioAsButton(field, option) {
				this.$set(this.formData, field.id, option.value);
			},
			handleCheckboxAsButton(field, option) {
				const currentValue = this.formData[field.id] || [];
				const index = currentValue.indexOf(option.value);
				if (index > -1) {
					currentValue.splice(index, 1);
				} else {
					currentValue.push(option.value);
				}
				this.$set(this.formData, field.id, currentValue);
			},
			handleDateChange(e, field) {
				this.$set(this.formData, field.id, e.detail.value);
			},
			async uploadFile(field) {
				try {
					const res = await uni.chooseImage({ count: 1 }); // 或 uni.chooseFile
					const filePath = res.tempFilePaths;
					const fileName = res.tempFiles.name;
					
					uni.showLoading({ title: '上传中...' });
					
					const uploadRes = await this.$minApi.uploadFile({
						filePath: filePath,
						name: 'file',
						fileby: 'approval'
					});

					uni.hideLoading();

					if (uploadRes.code === 200) {
						const fileData = {
							name: fileName,
							url: uploadRes.data,
							type: 'image'
						};
						if (!this.formData[field.id]) {
							this.$set(this.formData, field.id, []);
						}
						this.formData[field.id].push(fileData);
					} else {
						uni.showToast({ title: uploadRes.msg || '上传失败', icon: 'none' });
					}
				} catch (error) {
					uni.hideLoading();
					console.error("uploadFile error:", error);
				}
			},
			removeFile(field, fileIndex) {
				this.formData[field.id].splice(fileIndex, 1);
			},
			async uploadFileInGroup(groupField, groupIndex, innerField) {
				try {
					const res = await uni.chooseImage({ count: 1 }); // 或 uni.chooseFile
					const filePath = res.tempFilePaths[0];
					const fileName = res.tempFiles[0].name || `file_${Date.now()}`;
					
					uni.showLoading({ title: '上传中...' });
					
					const uploadRes = await this.$minApi.uploadFile({
						filePath: filePath,
						name: 'file',
						fileby: 'approval'
					});

					uni.hideLoading();

					if (uploadRes.code === 200) {
						const fileData = {
							name: fileName,
							url: uploadRes.data,
							type: 'image'
						};
						
						// 确保数组和对象存在
						if (!this.formData[groupField.id]) {
							this.$set(this.formData, groupField.id, []);
						}
						if (!this.formData[groupField.id][groupIndex]) {
							this.$set(this.formData[groupField.id], groupIndex, {});
						}
						if (!this.formData[groupField.id][groupIndex][innerField.id]) {
							this.$set(this.formData[groupField.id][groupIndex], innerField.id, []);
						}
						
						// 添加文件到动态分组的字段中
						this.formData[groupField.id][groupIndex][innerField.id].push(fileData);
						
						console.log(`[uploadFileInGroup] 文件上传成功:`, {
							groupField: groupField.label,
							groupIndex,
							innerField: innerField.label,
							fileName,
							currentFiles: this.formData[groupField.id][groupIndex][innerField.id]
						});
					} else {
						uni.showToast({ title: uploadRes.msg || '上传失败', icon: 'none' });
					}
				} catch (error) {
					uni.hideLoading();
					console.error("uploadFileInGroup error:", error);
					uni.showToast({ title: '上传失败', icon: 'none' });
				}
			},
			removeFileInGroup(groupField, groupIndex, innerField, fileIndex) {
				if (this.formData[groupField.id] &&
					this.formData[groupField.id][groupIndex] &&
					this.formData[groupField.id][groupIndex][innerField.id]) {
					this.formData[groupField.id][groupIndex][innerField.id].splice(fileIndex, 1);
					
					console.log(`[removeFileInGroup] 文件删除成功:`, {
						groupField: groupField.label,
						groupIndex,
						innerField: innerField.label,
						fileIndex,
						remainingFiles: this.formData[groupField.id][groupIndex][innerField.id]
					});
				}
			},
			async loadDraft() {
				uni.showLoading({ title: '加载草稿...' });
				try {
					const res = await this.$minApi.getDraft({ draftId: this.draftId });
					if (res.state === 'success' && res.data) {
						if (res.data.F_FrmData) {
							const draftFormData = JSON.parse(res.data.F_FrmData);
							this.applyDraftFormData(draftFormData);
						}
						if (res.data.F_RuntimeApprovalData) {
							this.approverData = JSON.parse(res.data.F_RuntimeApprovalData);
						}
					} else {
						uni.showToast({ title: '草稿加载失败', icon: 'none' });
					}
				} catch (error) {
					console.error("loadDraft error:", error);
					uni.showToast({ title: '草稿加载异常', icon: 'none' });
				} finally {
					uni.hideLoading();
				}
			},
			applyDraftFormData(draftData) {
				this.formContent.forEach(field => {
					if (draftData.hasOwnProperty(field.id)) {
						let value = draftData[field.id];
						switch (field.tag) {
							case 'checkbox':
								value = typeof value === 'string' ? value.split(',') : [];
								break;
							case 'upload':
								value = typeof value === 'string' ? this.parseUploadData(value) : [];
								break;
							case 'dynamicGroup':
								// 归一化为对象数组，并补齐缺失项，确保组内对象永远有效
								if (value == null || value === '') {
									value = [{}];
								} else if (typeof value === 'string') {
									try {
										value = JSON.parse(value);
									} catch (e) {
										console.error(`Failed to parse dynamicGroup data for ${field.id}:`, e);
										value = [{}];
									}
								}
								// 确保是数组
								if (!Array.isArray(value)) {
									value = [value || {}];
								}
								// 确保每一项是对象
								value = value.map(v => (v && typeof v === 'object') ? v : {});
								// 至少保留一个组
								if (value.length === 0) {
									value = [{}];
								}
								// 补齐每个组项的字段key，避免取值时报 groupItem[innerField.id] 为 undefined
								if (field.fields && Array.isArray(field.fields)) {
									value = value.map(item => {
										const normalized = { ...item };
										field.fields.forEach(innerField => {
											if (!(innerField.id in normalized)) {
												normalized[innerField.id] = null;
											}
										});
										return normalized;
									});
								}
								console.log(`[applyDraftFormData] Normalized dynamicGroup ${field.id}:`, value);
								break;
							case 'table':
								// Tables should be single objects
								if (typeof value === 'string') {
									try {
										value = JSON.parse(value);
										// If it's an array, take the first item
										if (Array.isArray(value) && value.length > 0) {
											value = value[0];
										}
									} catch (e) {
										console.error(`Failed to parse table data for ${field.id}:`, e);
										value = {};
									}
								}
								console.log(`[applyDraftFormData] Applied table ${field.id}:`, value);
								break;
						}
						this.$set(this.formData, field.id, value);
					}
				});
			},
			parseUploadData(dataString) {
				if (!dataString) return [];
				return dataString.split(',').map(url => {
					const name = url.substring(url.lastIndexOf('/') + 1);
					const isImage = /\.(jpg|jpeg|png|gif)$/i.test(name);
					return { name, url, type: isImage ? 'image' : 'file' };
				});
			},
			// --- Dynamic Group Methods ---
			isGroupFieldVisible(innerField, groupItem) {
				if (!innerField.expression) {
					return true;
				}
				// The context for evaluation should include both the main form and the group's data
				const context = { ...this.formData, ...groupItem };
				return this.evaluateExpression(innerField.expression, context);
			},
			_parseGroupPickerValue(groupItem, innerField) {
				if (!groupItem || typeof groupItem !== 'object') {
					return { selectedValue: '', selectedText: '', raw: null };
				}
				const raw = groupItem[innerField.id];
				let selectedValue = '';
				let selectedText = '';
			
				if (raw != null && typeof raw === 'object') {
					// 支持历史草稿把整个对象存入的情况，尽量按映射键提取
					const valueKeys = [innerField.remoteOptionValue, 'value', 'id', 'F_Id', 'Code', 'code', innerField.id].filter(Boolean);
					const textKeys  = [innerField.remoteOptionText, 'text', 'name', 'label', 'F_FullName', 'Title', 'title'].filter(Boolean);
					for (const k of valueKeys) {
						if (raw && raw[k] != null && raw[k] !== '') { selectedValue = String(raw[k]); break; }
					}
					for (const k of textKeys) {
						if (raw && raw[k] != null && raw[k] !== '') { selectedText = String(raw[k]); break; }
					}
				} else {
					selectedValue = raw != null ? String(raw) : '';
				}
				return { selectedValue, selectedText, raw };
			},
			getGroupPickerText(groupItem, innerField,groupIndex) {
				console.log('getGroupPickerText:',groupItem,innerField,groupIndex);
				let gt = this.formData[innerField.id];
				console.log('gt:',gt,innerField);
				const { selectedValue, selectedText, raw } = this._parseGroupPickerValue(groupItem, innerField);
			
				console.log(`[DEBUG-PICKER-TEXT] 🔍 Getting picker text:`, {
					field: innerField.label,
					groupItem,
					selectedValue,
					selectedText,
					raw,
					hasOptions: !!(innerField.options && innerField.options.length > 0),
					optionsCount: innerField.options ? innerField.options.length : 0
				});
			
				if (!selectedValue && !selectedText) {
					console.log(`[DEBUG-PICKER-TEXT] ❌ No value or text, returning empty`);
					return '';
				}
			
				if (!innerField.options || innerField.options.length === 0) {
					// 选项未加载，用已有文本或值占位
					console.log(`[DEBUG-PICKER-TEXT] ⚠️ Options not loaded, using fallback:`, selectedText || selectedValue);
					return selectedText || selectedValue;
				}
			
				// 优先按 value 匹配，失败则按 text 兜底（兼容字典/历史草稿按文本存值）
				let selectedOption = null;
				if (selectedValue) {
					selectedOption = innerField.options.find(opt => String(opt.value) === String(selectedValue));
				}
				if (!selectedOption && selectedText) {
					selectedOption = innerField.options.find(opt => String(opt.text) === String(selectedText));
				}
			
				const result = selectedOption ? selectedOption.text : (selectedText || selectedValue);
				console.log(`[DEBUG-PICKER-TEXT] 🎯 Final result:`, {
					selectedOption,
					result
				});
				return result;
			},
			getGroupPickerIndex(groupItem, innerField) {
				console.log(`[DEBUG-PICKER-INDEX] 🎯 Get picker index for group item:`, groupItem);
				const { selectedValue, selectedText, raw } = this._parseGroupPickerValue(groupItem, innerField);
			
				// console.log(`[getGroupPickerIndex] field: ${innerField.label}, parsed value: ${selectedValue}, text: ${selectedText}, raw:`, raw);
			
				if ((!selectedValue && !selectedText) || !innerField.options || innerField.options.length === 0) {
					return 0;
				}
				let index = -1; // Correctly initialize to -1
				if (selectedValue) {
					index = innerField.options.findIndex(opt => String(opt.value) === String(selectedValue));
				}
				if (index < 0 && selectedText) {
					index = innerField.options.findIndex(opt => String(opt.text) === String(selectedText));
				}
			
				return index >= 0 ? index : 0;
			},
			handleGroupPickerChange(e, groupField, innerField) {
				console.log("formData:"	,this.formData[groupField.id]);
				const selectedIndex = parseInt(e.detail.value);
				if (innerField.options && innerField.options[selectedIndex]) {
					const selectedValue = innerField.options[selectedIndex].value;
					console.log(`[handleGroupPickerChange] group: ${groupField.label}, field: ${innerField.label}, index: ${selectedIndex}, value: ${selectedValue}`);
					// 确保 formData[groupField.id] 存在
					if (!this.formData[groupField.id]) {
						console.log(`[handleGroupPickerChange] Creating new object for ${groupField.label}`);
						this.$set(this.formData, groupField.id, {});
					}
					this.$set(this.formData[groupField.id], innerField.id, selectedValue);
					// 强制更新视图
					this.$nextTick(() => {
						this.$forceUpdate();
						console.log(`[handleGroupPickerChange] Updated formData[${groupField.id}]:`, this.formData[groupField.id]);
					});
				}
			},
			handleGroupSwitchChange(e, groupField, innerField) {
				const isChecked = e.detail.value;
				if (innerField.options && innerField.options.length >= 2) {
					const targetValue = isChecked ? innerField.options[0].value : innerField.options[1].value;
					this.$set(this.formData[groupField.id], innerField.id, targetValue);
				}
			},
			handleGroupRadioAsButton(groupField, innerField, option) {
				this.$set(this.formData[groupField.id], innerField.id, option.value);
			},
			handleGroupDateChange(e, groupField, innerField) {
				this.$set(this.formData[groupField.id], innerField.id, e.detail.value);
			},
			// Methods for array-based dynamic groups
			handleArrayGroupPickerChange(e, groupField, groupIndex, innerField) {
				const selectedIndex = parseInt(e.detail.value);
				console.log(`[DEBUG-PICKER] 🔍 Picker change triggered:`, {
					groupField: groupField.label,
					groupIndex,
					innerField: innerField.label,
					selectedIndex,
					currentFormData: JSON.stringify(this.formData[groupField.id])
				});
				
				if (innerField.options && innerField.options[selectedIndex]) {
					// 统一存储为字符串，避免类型不一致导致回显匹配失败
					const selectedValue = String(innerField.options[selectedIndex].value);
					const selectedText = innerField.options[selectedIndex].text;
					
					console.log(`[DEBUG-PICKER] 📝 Selected option:`, {
						selectedValue,
						selectedText,
						optionObject: innerField.options[selectedIndex]
					});
					
					// 确保数组和对象存在
					if (!this.formData[groupField.id]) {
						this.$set(this.formData, groupField.id, []);
					}
					if (!this.formData[groupField.id][groupIndex]) {
						this.$set(this.formData[groupField.id], groupIndex, {});
					}
					
					const oldGroupItems = this.formData[groupField.id];
					const newGroupItems = [...oldGroupItems];
					const oldGroupItem = newGroupItems[groupIndex] || {};
					const newGroupItem = { ...oldGroupItem, [innerField.id]: selectedValue,[innerField.id+"_ShowText"]: selectedText  };
					newGroupItems[groupIndex] = newGroupItem;
					
					console.log(`[DEBUG-PICKER] 🔄 Data update:`, {
						oldGroupItem,
						newGroupItem,
						oldFormDataRef: oldGroupItems,
						newFormDataRef: newGroupItems
					});
					
					this.$set(this.formData, groupField.id, newGroupItems);

					// 延迟更新以确保数据同步
					this.$nextTick(() => {
						console.log(`[DEBUG-PICKER] 🎯 After $nextTick, formData:`, JSON.stringify(this.formData[groupField.id]));
						// 触发重新渲染
						this.$forceUpdate();
					});
				} else {
					console.log(`[DEBUG-PICKER] ❌ No valid option found for index ${selectedIndex}`);
				}
			},
			
			// 新增：获取动态分组字段值的方法
			getDynamicGroupValue(groupField, groupIndex, innerField,groupItem) {
				console.log(`[DEBUG-GROUP-PICKER] 🎯 Fetching value for groupField:`, groupField, 'groupIndex:', groupIndex, 'innerField:', innerField, 'groupItem:', groupItem);
				if (!this.formData[groupField.id] || !this.formData[groupField.id][groupIndex]) {
					return '';
				}
				const value = this.formData[groupField.id][groupIndex][innerField.id];
				console.log(`[DEBUG-GET-VALUE] 🔍 Getting value:`, {
					groupField: groupField.label,
					groupIndex,
					innerField: innerField.label,
					value
				});
				return value || '';
			},
			
			// 修改：处理动态分组输入事件的方法
			handleDynamicGroupInput(e, groupField, groupIndex, innerField) {
				const inputValue = e.detail.value;
				console.log(`[DEBUG-INPUT] 📝 Input change detected:`, {
					groupField: groupField.label,
					groupIndex,
					innerField: innerField.label,
					inputValue,
					currentGroupItem: this.formData[groupField.id] ? this.formData[groupField.id][groupIndex] : null
				});
				
				// 确保数组和对象存在
				if (!this.formData[groupField.id]) {
					this.$set(this.formData, groupField.id, []);
				}
				if (!this.formData[groupField.id][groupIndex]) {
					this.$set(this.formData[groupField.id], groupIndex, {});
				}
				
				// 手动更新数据以确保响应式
				const newGroupItems = [...this.formData[groupField.id]];
				const newGroupItem = { ...newGroupItems[groupIndex], [innerField.id]: inputValue };
				newGroupItems[groupIndex] = newGroupItem;
				this.$set(this.formData, groupField.id, newGroupItems);
				
				console.log(`[DEBUG-INPUT] 🔄 Updated formData:`, JSON.stringify(this.formData[groupField.id]));
			},
			handleArrayGroupSwitchChange(e, groupField, groupIndex, innerField) {
				const isChecked = e.detail.value;
				if (innerField.options && innerField.options.length >= 2) {
					const targetValue = isChecked ? innerField.options[0].value : innerField.options[1].value;
					const newGroupItems = [...this.formData[groupField.id]];
					const newGroupItem = { ...newGroupItems[groupIndex], [innerField.id]: targetValue };
					newGroupItems[groupIndex] = newGroupItem;
					this.$set(this.formData, groupField.id, newGroupItems);
				}
			},
			handleArrayGroupRadioAsButton(groupField, groupIndex, innerField, option) {
				const newGroupItems = [...this.formData[groupField.id]];
				const newGroupItem = { ...newGroupItems[groupIndex], [innerField.id]: option.value };
				newGroupItems[groupIndex] = newGroupItem;
				this.$set(this.formData, groupField.id, newGroupItems);
			},
			handleArrayGroupDateChange(e, groupField, groupIndex, innerField) {
				const newGroupItems = [...this.formData[groupField.id]];
				const newGroupItem = { ...newGroupItems[groupIndex], [innerField.id]: e.detail.value };
				newGroupItems[groupIndex] = newGroupItem;
				this.$set(this.formData, groupField.id, newGroupItems);
			},
			// Add and remove group items
			addGroupItem(field) {
				const newItem = {};
				if (field.fields && Array.isArray(field.fields)) {
					field.fields.forEach(innerField => {
						if (innerField.tag === 'radio' && innerField.options && innerField.options.length === 2) {
							this.$set(newItem, innerField.id, innerField.defaultValue || innerField.options[1].value);
						} else if (innerField.tag === 'upload') {
							this.$set(newItem, innerField.id, []);
						} else {
							this.$set(newItem, innerField.id, innerField.defaultValue || null);
						}
					});
				}
				const currentItems = Array.isArray(this.formData[field.id]) ? this.formData[field.id] : [];
				this.$set(this.formData, field.id, [...currentItems, newItem]);
			},
			removeGroupItem(field, index) {
				// 确保不删除第一个组，且至少保留一个组
				const currentItems = this.formData[field.id];
				if (Array.isArray(currentItems) && currentItems.length > 1 && index > 0) {
					const newItems = currentItems.filter((_, i) => i !== index);
					this.$set(this.formData, field.id, newItems);
				}
			},
			// --- Save/Submit ---
			saveDraft() {
				this.handleSave(true);
			},
			submit() {
				this.handleSave(false);
			},
			async handleSave(isDraft) {
				if (!isDraft) {
					if (!this.validateAll()) {
						return;
					}
				}

				const submitData = this.prepareSubmitData();
				const flowData = this.processFlowData(this.approverData);

				const payload = {
					code: this.flowCode,
					formData: JSON.stringify(submitData),
					schemeId: this.schemeId,
					draftId: this.draftId
				};

				uni.showLoading({ title: isDraft ? '保存中...' : '提交中...' });

				try {
					let res;
					if (isDraft) {
						payload.approverData = JSON.stringify(flowData);
						res = await this.$minApi.saveDraft(payload);
					} else {
						payload.flowData = JSON.stringify(flowData);
						res = await this.$minApi.submitFlow(payload);
					}

					if (res.state === 'success') {
						uni.showToast({
							title: isDraft ? '保存成功' : '提交成功',
							icon: 'success'
						});
						if (isDraft) {
							this.draftId = res.data.draftId; // Update draftId after saving
						} else {
							setTimeout(() => uni.navigateBack(), 1500);
						}
					} else {
						uni.showToast({ title: res.message || '操作失败', icon: 'none' });
					}
				} catch (error) {
					console.error("handleSave error:", error);
					uni.showToast({ title: '操作异常', icon: 'none' });
				} finally {
					uni.hideLoading();
				}
			},
			validateAll() {
				for (const field of this.formContent) {
					if (this.isVisible(field) && this.isRequired(field)) {
						const value = this.formData[field.id];
						if (value === null || value === '' || (Array.isArray(value) && value.length === 0)) {
							uni.showToast({ title: `请填写或选择[${field.label}]`, icon: 'none' });
							return false;
						}
						// Validate dynamicGroup/table
						if (field.tag === 'dynamicGroup' && Array.isArray(value)) {
							// Validate each group item
							for (let i = 0; i < value.length; i++) {
								const groupItem = value[i];
								for (const innerField of field.fields) {
									if (this.isGroupFieldVisible(innerField, groupItem) && innerField.required) {
										const innerValue = groupItem[innerField.id];
										if (innerValue === null || innerValue === '' || (Array.isArray(innerValue) && innerValue.length === 0)) {
											uni.showToast({ title: `请填写[${field.label} ${i + 1}]中的[${innerField.label}]`, icon: 'none' });
											return false;
										}
									}
								}
							}
						} else if (field.tag === 'table' && typeof value === 'object' && value !== null) {
							// Validate single table item
							const groupItem = value;
							for (const innerField of field.fields) {
								if (this.isGroupFieldVisible(innerField, groupItem) && innerField.required) {
									const innerValue = groupItem[innerField.id];
									if (innerValue === null || innerValue === '' || (Array.isArray(innerValue) && innerValue.length === 0)) {
										uni.showToast({ title: `请填写[${field.label}]中的[${innerField.label}]`, icon: 'none' });
										return false;
									}
								}
							}
						}
					}
				}
				if (this.$refs.approverSelector && !this.$refs.approverSelector.validate()) {
					return false;
				}
				return true;
			},
			prepareSubmitData() {
				const data = JSON.parse(JSON.stringify(this.formData));
				this.formContent.forEach(field => {
					if (!this.isVisible(field)) {
						delete data[field.id];
						return;
					}
					const value = data[field.id];
					if (Array.isArray(value)) {
						if (field.tag === 'checkbox') {
							data[field.id] = value.join(',');
						} else if (field.tag === 'upload') {
							data[field.id] = value.map(f => f.url).join(',');
						}
					} else if (typeof value === 'object' && value !== null) {
						if (field.tag === 'dynamicGroup' || field.tag === 'table') {
							data[field.id] = JSON.stringify(value);
						}
					} else if (Array.isArray(value)) {
						// Handle array-based dynamic groups
						if (field.tag === 'dynamicGroup') {
							data[field.id] = JSON.stringify(value);
						}
					}
				});
				return data;
			},
			processFlowData(flowData) {
				const newFlowData = JSON.parse(JSON.stringify(flowData));
				const assessmentNode = this.schemeContent.nodes.find(n => n.name === '评估人');
				const validationNode = this.schemeContent.nodes.find(n => n.name === '校验');

				if (assessmentNode && validationNode && newFlowData.nodes) {
					const assessmentData = newFlowData.nodes.find(n => n.id === assessmentNode.id);
					if (assessmentData) {
						const validationData = JSON.parse(JSON.stringify(assessmentData));
						validationData.id = validationNode.id;
						newFlowData.nodes.push(validationData);
					}
				}
				return newFlowData;
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		display: flex;
		flex-direction: column;
		gap: 20rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
		padding-bottom: 160rpx;
	}

	.form-section, .approval-flow-section {
		background-color: white;
		border-radius: 16rpx;
		.title {
			color: #7F7F7F;
			font-weight: 400;
			font-size: 28rpx;
			background-color: rgba(242, 242, 242, 1);
			padding: 20rpx;
			border-top-left-radius: 16rpx;
			border-top-right-radius: 16rpx;
		}
		.content {
			padding: 20rpx;
			display: flex;
			flex-direction: column;
			gap: 20rpx;
		}
	}
	
	.item {
		display: flex;
		flex-direction: row;
		align-items: center;
		line-height: 66rpx;
		justify-content: space-between;
		border-bottom: 1rpx solid rgba(242, 242, 242, 1);
		padding: 10rpx 0;
		
		// 垂直布局样式（用于多个按钮的情况）
		&.item-vertical {
			flex-direction: column;
			align-items: flex-start;
			
			.label {
				margin-bottom: 10rpx;
			}
		}
		
		.label {
			color: #666666;
			font-size: 28rpx;
			white-space: nowrap;
			margin-right: 20rpx;
		}
		
		.required {
			color: #ff0000;
			margin-left: 4rpx;
		}
		
		.text, .uni-textarea, .uni-input {
			flex: 1;
			color: #666666;
			font-size: 28rpx;
			text-align: right;
		}
		
		.uni-input {
			line-height: 30rpx;
			padding-right: 0;
		}
		
		.custom-control-container {
			flex: 1;
			display: flex;
			justify-content: flex-end;
		}
		
		.option-button-container-vertical {
			width: 100%;
			
			.option-button-group {
				display: flex;
				flex-wrap: wrap;
				gap: 10rpx;
				justify-content: flex-start;
			}
		}
		
		.option-button-group {
			display: flex;
			flex-wrap: wrap;
			gap: 10rpx;
			justify-content: flex-end;
			flex: 1;
		}
		.option-button {
			background-color: #F6F7FC;
			color: #23262A;
			border: none;
			padding: 6rpx 20rpx;
			height: 50rpx;
			line-height: 38rpx;
			font-size: 28rpx;
			border-radius: 10rpx;
			margin: 0;
			transition: all 0.2s ease;
			&.active {
				background-color: #00984a;
				color: #FFFFFF;
			}
		}
	}
	
	.file-list {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
		padding-top: 10rpx;
		
		.file-upload {
			width: 160rpx;
			height: 160rpx;
			border: 2rpx dashed #ddd;
			border-radius: 8rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			background-color: #f8f8f8;
			
			.upload-icon {
				font-size: 48rpx;
				color: #999;
			}
			
			.upload-text {
				font-size: 24rpx;
				color: #999;
			}
		}
		
		.file-item {
			position: relative;
			width: 160rpx;
			height: 160rpx;
			background-color: #f5f5f5;
			border-radius: 8rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 10rpx;
			
			.file-name-text {
				font-size: 24rpx;
				color: #666666;
				text-align: center;
				word-break: break-all;
			}
			
			.delete-btn {
				position: absolute;
				top: -15rpx;
				right: -15rpx;
				width: 30rpx;
				height: 30rpx;
				background-color: rgba(0, 0, 0, 0.5);
				color: #fff;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 24rpx;
			}
		}
	}

	.dynamic-group-card {
		width: 100%;
		background-color: #ffffff;
		border-radius: 16rpx;
		padding: 0;
		margin-top: 20rpx;
		border: 1rpx solid #f0f0f0;
		overflow: hidden;
		box-sizing: border-box;

		.dynamic-group-title {
			color: #333;
			font-weight: bold;
			font-size: 30rpx;
			padding: 20rpx;
			background-color: #fafafa;
			border-bottom: 1rpx solid #f0f0f0;
		}
		
		.dynamic-group-wrapper {
			border-bottom: 2rpx solid #f0f0f0;
			
			&:last-of-type {
				border-bottom: none;
			}
		}
		
		.dynamic-group-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 15rpx 20rpx;
			background-color: #f8f8f8;
			
			.group-index-text {
				font-size: 26rpx;
				color: #666;
				font-weight: 500;
			}
			
			.delete-group-btn {
				color: #ff4444;
				font-size: 26rpx;
				padding: 5rpx 15rpx;
			}
		}

		.dynamic-group-content {
			padding: 0 20rpx;
			
			.dynamic-group-item {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 20rpx 0;
				border-bottom: 1rpx solid #f0f0f0;
				
				&:last-child {
					border-bottom: none;
				}
				
				&.vertical {
					flex-direction: column;
					align-items: flex-start;
					
					.dynamic-label {
						margin-bottom: 10rpx;
					}
					
					.dynamic-textarea {
						width: 100%;
					}
				}
			}
			
			.dynamic-label {
				color: #666666;
				font-size: 28rpx;
				white-space: nowrap;
				margin-right: 20rpx;
				min-width: 120rpx;
			}
			
			.dynamic-input,
			.dynamic-picker {
				flex: 1;
				padding: 10rpx;
				font-size: 28rpx;
				color: #333;
				text-align: right;
				
				&::placeholder {
					color: #999;
				}
			}
			
			.dynamic-textarea {
				flex: 1;
				padding: 10rpx;
				font-size: 28rpx;
				color: #333;
				min-height: 80rpx;
				
				&::placeholder {
					color: #999;
				}
			}
			
			.dynamic-picker {
				text-align: right;
			}
			
			.dynamic-radio-container {
				flex: 1;
				display: flex;
				justify-content: flex-end;
				
				.option-button-group {
					display: flex;
					flex-wrap: wrap;
					gap: 10rpx;
					justify-content: flex-end;
				}
			}
			
			.dynamic-file-list {
				flex: 1;
				display: flex;
				flex-wrap: wrap;
				gap: 15rpx;
				justify-content: flex-end;
				
				.dynamic-file-upload {
					width: 120rpx;
					height: 120rpx;
					border: 2rpx dashed #ddd;
					border-radius: 8rpx;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					background-color: #f8f8f8;
					
					.upload-icon {
						font-size: 36rpx;
						color: #999;
					}
					
					.upload-text {
						font-size: 20rpx;
						color: #999;
						margin-top: 5rpx;
					}
				}
				
				.dynamic-file-item {
					position: relative;
					width: 120rpx;
					height: 120rpx;
					background-color: #f5f5f5;
					border-radius: 8rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					padding: 8rpx;
					
					.file-name-text {
						font-size: 20rpx;
						color: #666666;
						text-align: center;
						word-break: break-all;
						line-height: 1.2;
					}
					
					.delete-btn {
						position: absolute;
						top: -10rpx;
						right: -10rpx;
						width: 24rpx;
						height: 24rpx;
						background-color: rgba(0, 0, 0, 0.5);
						color: #fff;
						border-radius: 50%;
						display: flex;
						align-items: center;
						justify-content: center;
						font-size: 18rpx;
					}
				}
			}
		}
		
		.add-group-btn {
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 20rpx;
			margin: 20rpx;
			background-color: #f8f8f8;
			border: 2rpx dashed #00984a;
			border-radius: 10rpx;
			
			.add-icon {
				font-size: 32rpx;
				color: #00984a;
				margin-right: 10rpx;
			}
			
			.add-text {
				font-size: 28rpx;
				color: #00984a;
			}
		}
	}

	.action-bar {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		display: flex;
		justify-content: space-around;
		align-items: center;
		background-color: #fff;
		padding: 20rpx;
		padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
		padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
		border-top: 1rpx solid #eee;
		box-shadow: 0 -5rpx 10rpx rgba(0,0,0,0.05);
	}

	.save-draft-btn,
	.submit-btn {
		flex: 1;
		margin: 0 10rpx;
		border-radius: 40rpx;
		font-size: 30rpx;
	}

	.save-draft-btn {
		background-color: #f0f0f0;
		color: #333;
	}

	.submit-btn {
		background-color: #00984a;
		color: #fff;
	}
</style>