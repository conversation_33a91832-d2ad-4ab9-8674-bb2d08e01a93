<template>
	<view class="container">
		<view class="form-wrapper">
			<textarea v-if="isNormalApproval" class="approval-textarea" placeholder="请输入审批意见" v-model="formData.approvalComment"></textarea>
			
			<!-- 安全风险表单 -->
			<view v-if="isSafetyRisk" class="safety-risk-form">
				<view class="form-item">
					<label class="label required">安全风险:</label>
					<textarea class="textarea" placeholder="请输入" v-model="formData.safetyRisk"></textarea>
				</view>
				<view class="form-item">
					<label class="label required">技术要求:</label>
					<textarea class="textarea" placeholder="请输入" v-model="formData.technicalRequirements"></textarea>
				</view>
				<view class="form-item checkbox-item">
					<checkbox :checked="formData.notInvolved" @click="formData.notInvolved = !formData.notInvolved">不涉及</checkbox>
					<view class="warning-text">若勾选此按钮,施工完成验证时将不参与验证环节</view>
				</view>
			</view>

			<!-- 化学品安全评估表单 -->
			<view v-if="isChemicalSafety">
				<!-- 安全评估人 -->
				<view v-if="currentRoleForm === '安全评估人'" class="chemical-safety-form">
					<view class="form-item">
						<label class="label required">物理状态:</label>
						<view class="radio-group">
							<text v-for="item in ['液体', '气体', '粉末', '液化气体', '固体']" :key="item" @click="formData.physicalState = item" :class="{'active': formData.physicalState === item}">{{ item }}</text>
						</view>
					</view>
					<view class="form-item" v-if="formData.physicalState === '气体'">
						<label class="label required">压力:</label>
						<input class="input" type="number" placeholder="请输入压力" v-model="formData.pressure" />
					</view>
					<view class="form-item">
						<label class="label required">闪点:</label>
						<input class="input" type="number" placeholder="请输入闪点" v-model="formData.flashPoint" />
					</view>
					<view class="form-item">
						<label class="label required">自燃温度:</label>
						<input class="input" type="number" placeholder="请输入自燃温度" v-model="formData.autoignitionTemperature" />
					</view>
					<view class="form-item">
						<label class="label required">易燃风险:</label>
						<view class="sub-item">
							<text>潜在的爆炸:</text>
							<switch :checked="formData.potentialExplosion" @change="formData.potentialExplosion = $event.detail.value" />
						</view>
						<view class="sub-item" v-if="formData.potentialExplosion">
							<text>危险气体:</text>
							<input class="input" placeholder="请输入危险气体" v-model="formData.hazardousGas" />
						</view>
					</view>
					<view class="form-item">
						<label class="label required">灭火剂:</label>
						<view class="checkbox-group">
							<text v-for="item in ['水', 'CO2', '干粉', '其他']" :key="item" @click="toggleCheckbox('extinguishingAgent', item)" :class="{'active': (formData.extinguishingAgent || []).includes(item)}">{{ item }}</text>
						</view>
					</view>
					<view class="form-item">
						<label class="label required">操作:</label>
						<view class="checkbox-group">
							<text v-for="item in ['使用专用PPE', '严禁火源', '防水', '防止光照', '保持容器关闭', '其他']" :key="item" @click="toggleCheckbox('operation', item)" :class="{'active': (formData.operation || []).includes(item)}">{{ item }}</text>
						</view>
					</view>
					<view class="form-item">
						<label class="label required">贮存条件:</label>
						<view class="checkbox-group">
							<text v-for="item in ['通风', '隔离', '避免日光照射', '冷藏', '黑暗条件', '与潜在自燃物隔离贮存', '桶接地', '需要机械通风', '其他']" :key="item" @click="toggleCheckbox('storageConditions', item)" :class="{'active': (formData.storageConditions || []).includes(item)}">{{ item }}</text>
						</view>
					</view>
					<view class="form-item ppe-item">
						<label class="label required">所需PPE:</label>
						<view class="ppe-group">
							<view class="ppe-row"><checkbox @click="togglePpe('hand')">手</checkbox><input class="input" v-model="formData.ppe_hand_text" /></view>
							<view class="ppe-row"><checkbox @click="togglePpe('eye')">眼睛</checkbox><input class="input" v-model="formData.ppe_eye_text" /></view>
							<view class="ppe-row"><checkbox @click="togglePpe('respiratory')">呼吸</checkbox><input class="input" v-model="formData.ppe_respiratory_text" /></view>
							<view class="ppe-row"><checkbox @click="togglePpe('other')">其他</checkbox><input class="input" v-model="formData.ppe_other_text" /></view>
						</view>
					</view>
					<view class="form-item">
						<label class="label required">危险化学品:</label>
						<switch :checked="formData.hazardousChemical" @change="formData.hazardousChemical = $event.detail.value" />
					</view>
					<view class="form-item">
						<label class="label">备注说明:</label>
						<textarea class="textarea" placeholder="请输入备注说明" v-model="formData.remarks"></textarea>
					</view>
					<view class="form-item">
						<label class="label required">化学品类别:</label>
						<input class="input" placeholder="请输入化学品类别" v-model="formData.chemicalCategory" />
					</view>
					<view class="form-item">
						<label class="label">评估意见:</label>
						<textarea class="textarea" placeholder="请输入评估意见" v-model="formData.assessmentOpinion"></textarea>
					</view>
				</view>

				<!-- 职业健康评估人 -->
				<view v-if="currentRoleForm === '职业健康评估人'" class="chemical-safety-form">
					<view class="form-item">
						<label class="label required">主要组分/含量:</label>
						<input class="input" placeholder="请输入" v-model="formData.mainComponent" />
					</view>
					<view class="form-item">
						<label class="label required">职业病危害因素:</label>
						<switch :checked="formData.occupationalHazard" @change="formData.occupationalHazard = $event.detail.value" />
					</view>
					<view class="form-item" v-if="formData.occupationalHazard">
						<label class="label required">名称:</label>
						<input class="input" placeholder="请输入名称" v-model="formData.hazardName" />
					</view>
					<view class="form-item">
						<label class="label required">职业接触限值:</label>
						<input class="input" placeholder="请输入" v-model="formData.exposureLimit" />
					</view>
					<view class="form-item">
						<label class="label required">健康风险:</label>
						<view class="checkbox-group">
							<text v-for="item in ['吸入', '摄入', '眼睛接触', '皮肤介入', '慢性疾病', '致癌性']" :key="item" @click="toggleCheckbox('healthRisk', item)" :class="{'active': (formData.healthRisk || []).includes(item)}">{{ item }}</text>
						</view>
					</view>
					<view class="form-item">
						<label class="label">评估意见:</label>
						<textarea class="textarea" placeholder="请输入评估意见" v-model="formData.assessmentOpinion"></textarea>
					</view>
				</view>

				<!-- 环境评估人 -->
				<view v-if="currentRoleForm === '环境评估人'" class="chemical-safety-form">
					<view class="form-item">
						<label class="label required">有害物质:</label>
						<switch :checked="formData.hazardousSubstance" @change="formData.hazardousSubstance = $event.detail.value" />
					</view>
					<view class="form-item" v-if="formData.hazardousSubstance">
						<label class="label">类型:</label>
						<input class="input" placeholder="类型（材料名称、成分百分含量）" v-model="formData.hazardousSubstanceType" />
					</view>
					<view class="form-item">
						<label class="label">泄漏/溢出:</label>
						<view class="sub-item">
							<text>有必要准备吸附材料吗？</text>
							<switch :checked="formData.spillAdsorbent" @change="formData.spillAdsorbent = $event.detail.value" />
						</view>
						<view class="sub-item" v-if="formData.spillAdsorbent">
							<text>类型:</text>
							<input class="input" placeholder="请输入吸附材料类型" v-model="formData.spillType" />
						</view>
					</view>
					<view class="form-item">
						<label class="label">废物:</label>
						<view class="radio-group">
							<text @click="formData.wasteType = '有害的'" :class="{'active': formData.wasteType === '有害的'}">有害的</text>
							<text @click="formData.wasteType = '无害的'" :class="{'active': formData.wasteType === '无害的'}">无害的</text>
						</view>
					</view>
					<view v-if="formData.wasteType === '有害的'">
						<view class="form-item">
							<label class="label">名称:</label>
							<input class="input" placeholder="请输入废物名称" v-model="formData.wasteName" />
						</view>
						<view class="form-item">
							<label class="label">要求处置方式:</label>
							<input class="input" placeholder="请输入处置方式" v-model="formData.wasteDisposal" />
						</view>
					</view>
					<view class="form-item">
						<label class="label">废水处理:</label>
						<view class="sub-item">
							<text>会影响废水处理工艺吗？</text>
							<switch :checked="formData.affectWastewater" @change="formData.affectWastewater = $event.detail.value" />
						</view>
						<view class="sub-item" v-if="formData.affectWastewater">
							<text>备注:</text>
							<input class="input" placeholder="请输入备注说明" v-model="formData.wastewaterRemark" />
						</view>
					</view>
					<view class="form-item">
						<label class="label">地下水:</label>
						<view class="sub-item">
							<text>能够导致地下水污染吗？</text>
							<switch :checked="formData.polluteGroundwater" @change="formData.polluteGroundwater = $event.detail.value" />
						</view>
						<view class="sub-item" v-if="formData.polluteGroundwater">
							<text>备注:</text>
							<input class="input" placeholder="请输入备注说明" v-model="formData.groundwaterRemark" />
						</view>
					</view>
					<view class="form-item">
						<label class="label">贮存:</label>
						<view class="sub-item">
							<text>需要特殊的贮存条件吗？</text>
							<switch :checked="formData.specialStorage" @change="formData.specialStorage = $event.detail.value" />
						</view>
						<view class="sub-item" v-if="formData.specialStorage">
							<text>备注:</text>
							<input class="input" placeholder="请输入备注说明" v-model="formData.storageRemark" />
						</view>
					</view>
					<view class="form-item">
						<label class="label">评估意见:</label>
						<textarea class="textarea" placeholder="请输入评估意见" v-model="formData.assessmentOpinion"></textarea>
					</view>
				</view>
			</view>
		</view>

		<view class="action-bar">
			<button class="cancel-btn" @click="handleCancel">取消</button>
			<button class="confirm-btn" @click="handleConfirm">确定</button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			id: null,
			type: '',
			auditType: '',
			title: '',
			formData: {},
			currentRoleForm: ''
		};
	},
	computed: {
		isNormalApproval() {
			return this.auditType === 'reject' || this.type === 'approval';
		},
		isSafetyRisk() {
			return this.type === 'safety' && this.auditType === 'pass';
		},
		isChemicalSafety() {
			return this.type === 'chemical_safety';
		}
	},
	onLoad(options) {
		this.id = options.id;
		this.type = options.type;
		this.auditType = options.auditType;
		this.title = decodeURIComponent(options.title) || '审批';
		uni.setNavigationBarTitle({ title: this.title });

		if (this.isChemicalSafety) {
			this.fetchCurrentUserApprovalType();
		}
	},
	methods: {
		async fetchCurrentUserApprovalType() {
			try {
				const res = await this.$minApi.getCurrentUserApprovalType(this.id);
				if (res.state === 'success' && res.data) {
					this.currentRoleForm = res.data;
				} else {
					this.currentRoleForm = '安全评估人'; // 默认
				}
			} catch (error) {
				this.currentRoleForm = '安全评估人'; // 异常时默认
			}
		},
		toggleCheckbox(field, value) {
			if (!this.formData[field]) {
				this.$set(this.formData, field, []);
			}
			const index = this.formData[field].indexOf(value);
			if (index > -1) {
				this.formData[field].splice(index, 1);
			} else {
				this.formData[field].push(value);
			}
		},
		togglePpe(type) {
			// 简单处理，实际可能需要更复杂的逻辑
		},
		handleCancel() {
			uni.navigateBack();
		},
		handleConfirm() {
			if (this.validateForm()) {
				this.submitApproval();
			}
		},
		validateForm() {
			// ... 根据不同表单进行验证
			return true;
		},
		async submitApproval() {
			uni.showLoading({ title: '提交中...' });
			try {
				const params = {
					F_FlowInstanceId: this.id,
					F_VerificationFinally: this.auditType === 'reject' ? '2' : '1',
					F_VerificationOpinion: this.formatOpinion(),
					F_FrmData: this.isChemicalSafety ? JSON.stringify(this.formData) : ''
				};
				const res = await this.$minApi.auditFlow(params);
				if (res.state === 'success') {
					uni.showToast({ title: '提交成功' });
					setTimeout(() => uni.navigateBack(), 1500);
				} else {
					uni.showToast({ title: res.message || '提交失败', icon: 'none' });
				}
			} catch (error) {
				uni.showToast({ title: '提交失败', icon: 'none' });
			} finally {
				uni.hideLoading();
			}
		},
		formatOpinion() {
			if (this.isNormalApproval) {
				return this.formData.approvalComment || '';
			}
			// ... 根据不同表单格式化意见
			return JSON.stringify(this.formData);
		}
	}
};
</script>

<style lang="scss" scoped>
.container {
	padding: 20rpx;
	background-color: #f5f5f5;
}
.form-wrapper {
	background-color: #fff;
	padding: 30rpx;
	border-radius: 16rpx;
}
.approval-textarea {
	width: 100%;
	height: 200rpx;
	border: 1rpx solid #eee;
	border-radius: 8rpx;
	padding: 20rpx;
	box-sizing: border-box;
}
.form-item {
	margin-bottom: 20rpx;
	.label {
		font-weight: bold;
		margin-bottom: 10rpx;
		display: block;
		&.required::before {
			content: '*';
			color: red;
			margin-right: 5rpx;
		}
	}
	.input, .textarea {
		width: 100%;
		border: 1rpx solid #eee;
		border-radius: 8rpx;
		padding: 20rpx;
		box-sizing: border-box;
	}
	.radio-group, .checkbox-group {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
		text {
			padding: 10rpx 20rpx;
			border: 1rpx solid #eee;
			border-radius: 8rpx;
			&.active {
				background-color: #00984a;
				color: #fff;
				border-color: #00984a;
			}
		}
	}
}
.action-bar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	display: flex;
	padding: 20rpx;
	background-color: #fff;
	.cancel-btn, .confirm-btn {
		flex: 1;
		margin: 0 10rpx;
	}
	.confirm-btn {
		background-color: #00984a;
		color: #fff;
	}
}
</style>