<template>
	<view class="container">
		<!-- 提交人信息 -->
		<uni-card :is-shadow="false" :border="false">
			<view class="user-info">
				<image class="avatar" :src="flowData.F_CreatorUserAvatar || '/static/img/profile.svg'"></image>
				<view class="user-desc">
					<view class="username">提交人：{{ flowData.F_CreatorUserName }}</view>
					<view class="create-time">提交时间：{{ flowData.F_CreatorTime }}</view>
				</view>
			</view>
		</uni-card>

		<!-- 基本信息 -->
		<uni-card title="基本信息" :is-shadow="false" :border="false">
			<view class="form-content">
				<block v-for="(field, index) in formContent" :key="index">
					<view class="form-item" v-if="isVisible(field)">
						<label v-if="field.tag !== 'dynamicGroup' && field.tag !== 'table'" class="label">{{ field.label }}</label>
						<view class="value-area" v-if="field.tag !== 'dynamicGroup' && field.tag !== 'table'">
							<text v-if="isSimpleField(field.tag)" class="readonly-text">{{ getDisplayValue(field)}}</text>
							<view v-if="field.tag === 'upload'" class="file-list">
								<view v-for="(file, fileIndex) in formData[field.id]" :key="fileIndex" class="file-item">
									<!-- <text class="file-name-text">{{ file.name }}</text> -->
									<image v-if="isImageFile(file.url)" :src="baseUrl+file.url" mode="aspectFill" @click="previewImage(field.id,file)"></image>
									<view v-else class="file-name" @click="openFile(baseUrl+file.url)">
										<text>{{getFileName(file.url)}}</text>
									</view>
								</view>
							</view>
							<view v-if="field.tag === 'select'">
								<text>{{ getPickerText(field) }}</text>
							</view>
						</view>

						<!-- Table 显示 -->
						<view v-if="field.tag === 'table'" class="table-display-card">
							<view class="table-title">{{ field.label }}</view>
							<view class="table-content">
								<block v-for="(innerField, innerIndex) in field.fields" :key="innerIndex">
									<view class="table-item" v-if="isVisible(innerField)">
										<label class="table-label">{{ innerField.label }}</label>
										<view class="table-value">
											<text class="readonly-text">{{ getTableFieldDisplayValue(innerField, formData[field.id]) }}</text>
										</view>
									</view>
								</block>
							</view>
						</view>

						<view v-if="field.tag === 'dynamicGroup'"  class="value-area" style="margin-left: -20rpx;">
							<dynamic-form-group
								v-if="field.tag === 'dynamicGroup'"
								:field="field"
								:form-data="formData"
								:baseUrl="baseUrl"
								v-model="formData[field.id]"
								:is-readonly="true"
							/>
						</view>
					</view>
				</block>
			</view>
		</uni-card>

		<!-- 审批流程 -->
		<uni-card title="审批流程" :is-shadow="false" :border="false">
			<approval-tabs :nodes="flowData.nodes || []" :approval-data="approvalData" :active-id="flowData.F_ActiveId"></approval-tabs>
		</uni-card>

		<!-- 底部操作按钮 -->
		<view class="bottom-actions" v-if="!isPreview">
			<button v-if="showCancelBtn" class="action-btn" type="default" @click="handleAction('cancel')">取消审批</button>
			<button v-if="showTransferBtn" class="action-btn" type="default" @click="handleAction('transfer')">转审</button>
			<button v-if="showRejectBtn" class="action-btn" type="warn" @click="handleAction('reject')">拒绝</button>
			<button v-if="showAgreeBtn" class="action-btn" type="primary" @click="handleAction('pass')">同意</button>
			<button v-if="showRestartBtn" class="action-btn" type="primary" @click="handleAction('restart')">重新发起</button>
		</view>
	</view>
</template>

<script>
import DynamicFormGroup from '@/components/dynamic-form-group/dynamic-form-group.vue';
import ApprovalTabs from 'components/approval-tabs/approval-tabs.vue';
import uniCard from '@/components/uni-card/uni-card.vue';
import { formatDate, formatDateDay } from '@/utils/datetime.js';
import { mapState, mapActions, mapGetters } from 'vuex';
import globalConfig from '@/config';

export default {
	components: {
		DynamicFormGroup,
		ApprovalTabs,
		uniCard
	},
	computed: {
		...mapGetters(['themeBgColor', 'darkMode', 'user']),
		userInfo() {
			return { ...this.user } || {}
		},
	},
	data() {
		return {
			baseUrl: "",
			instanceId: null,
			flowData: {},
			formContent: [],
			formData: {},
			approvalData: null,
			showActions: false,
			isPreview: false,        // 预览模式标识
			// 按钮显示控制
			showCancelBtn: false,    // 取消审批按钮
			showTransferBtn: false,  // 转办按钮
			showRejectBtn: false,    // 拒绝按钮
			showAgreeBtn: false,     // 同意按钮
			showRestartBtn: false,   // 重新发起按钮
		};
	},
	onLoad(options) {
		console.log("options",options);
		this.baseUrl = globalConfig.baseUrl.replace("/api","");
		if (options.id) {
			this.instanceId = options.id;
			// 检查是否为预览模式
			if (options.type === 'preview') {
				this.isPreview = true;
				this.showActions = false; // 预览模式下隐藏所有操作按钮
			}
			this.fetchFlowDetails();
		} else {
			uni.showToast({ title: '缺少流程ID', icon: 'none' });
		}
	},
	methods: {
		async fetchFlowDetails() {
			uni.showLoading({ title: '加载中...' });
			try {
				const res = await this.$minApi.getFlowDetails(this.instanceId);
				if (res.state === "success") {
					this.flowData = {
						...this.flowData,
						F_CreatorUserName: res.data.F_CreatorUserName,
						F_CreatorUserId: res.data.F_CreatorUserId, // 发起人ID
						F_CreatorTime: formatDate(res.data.F_CreatorTime),
						F_CreatorUserAvatar: res.data.F_CreatorUserAvatar,
						F_ActiveId: res.data.F_ActivityId,
						F_FlowCode: res.data.F_Code,
						F_ActiveName: res.data.F_ActivityName,
						F_IsFinish: res.data.F_IsFinish, // 完成状态
						F_MakerList: res.data.F_MakerList // 审批人列表
					};
					
					if (res.data.F_RuntimeApprovalData) {
						this.approvalData = JSON.parse(res.data.F_RuntimeApprovalData).data;
					}

					this.formContent = JSON.parse(res.data.F_FrmContent || '[]');
					this.formData = JSON.parse(res.data.F_FrmData || '{}');
					if (res.data.F_SchemeContent) {
						const flowTemplate = JSON.parse(res.data.F_SchemeContent);
						this.flowData.nodes = flowTemplate.nodes || [];
					}
					
					this.initializeFormData();
					this.checkUserActions();
				}
			} catch (error) {
				console.error("fetchFlowDetails error:", error);
				uni.showToast({ title: '加载失败', icon: 'none' });
			} finally {
				uni.hideLoading();
			}
		},
		initializeFormData() {
			const initialData = { ...this.formData };

			// First, process and standardize all fields
			this.formContent.forEach(field => {
				// Normalize top-level field remote datasource config (support api* and remote* keys)
				if (!field.remoteUrl && field.apiUrl) {
					field.remoteUrl = field.apiUrl;
				}
				if (!field.remoteMethod && field.apiMethod) {
					field.remoteMethod = field.apiMethod;
				}
				if (!field.remoteOptionText && field.dataMapping && field.dataMapping.textField) {
					field.remoteOptionText = field.dataMapping.textField;
				}
				if (!field.remoteOptionValue && field.dataMapping && field.dataMapping.valueField) {
					field.remoteOptionValue = field.dataMapping.valueField;
				}

				if (field.tag === 'table' && field.columns) {
					field.fields = field.columns.map(col => ({
						id: col.field, // Use 'field' as 'id' for v-model
						label: col.title,
						tag: col.type || 'input', // Default to input if type is missing
						options: col.options || [],
						required: col.required || false,
						expression: col.expression || null,
						defaultValue: col.defaultValue || null,
						// Support both old and new API datasource formats
						datasourceType: col.datasourceType || 'local',
						apiUrl: col.apiUrl || '',
						apiMethod: col.apiMethod || 'GET',
						dataMapping: col.dataMapping || null,
						remoteUrl: col.apiUrl || '',
						remoteMethod: col.apiMethod || 'GET',
						remoteOptionText: (col.dataMapping && col.dataMapping.textField) || '',
						remoteOptionValue: (col.dataMapping && col.dataMapping.valueField) || ''
					}));
				}

				// Also process fields array if it exists (for dynamicGroup)
				if (field.fields && Array.isArray(field.fields)) {
					field.fields = field.fields.map(innerField => ({
						...innerField,
						// Support both old and new API datasource formats
						datasourceType: innerField.datasourceType || 'local',
						apiUrl: innerField.apiUrl || '',
						apiMethod: innerField.apiMethod || 'GET',
						dataMapping: innerField.dataMapping || null,
						remoteUrl: innerField.apiUrl || '',
						remoteMethod: innerField.apiMethod || 'GET',
						remoteOptionText: (innerField.dataMapping && innerField.dataMapping.textField) || '',
						remoteOptionValue: (innerField.dataMapping && innerField.dataMapping.valueField) || ''
					}));
				}
			});

			// Now load remote data for fields with API datasource
			this.loadRemoteOptions();

			// Then initialize form data with default values
			this.formContent.forEach(field => {
				if (initialData.hasOwnProperty(field.id)) {
					let value = initialData[field.id];
					switch (field.tag) {
						case 'checkbox':
							initialData[field.id] = typeof value === 'string' ? value.split(',') : [];
							break;
						case 'upload':
							initialData[field.id] = this.parseUploadData(value);
							break;
						case 'dynamicGroup':
							try {
								initialData[field.id] = JSON.parse(value);
							} catch (e) {
								initialData[field.id] = [];
							}
							break;
						case 'table':
							try {
								// Table 数据通常是 JSON 字符串，需要解析
								if (typeof value === 'string') {
									const parsed = JSON.parse(value);
									// 如果解析后是数组，取第一个元素（table 是单个对象）
									initialData[field.id] = Array.isArray(parsed) && parsed.length > 0 ? parsed[0] : parsed;
								} else {
									initialData[field.id] = value;
								}
							} catch (e) {
								console.error(`Failed to parse table data for ${field.id}:`, e);
								initialData[field.id] = {};
							}
							break;
					}
				}
			});
			this.formData = initialData;
		},

		async loadRemoteOptions() {
			console.log('Starting to load remote options...');
			// Load options for all fields with remote/api datasource
			const promises = [];
			
			for (const field of this.formContent) {
				// Support both 'remote' and 'api' datasource types
				// Also check for apiUrl in addition to remoteUrl
				const hasRemoteData = (field.datasourceType === 'remote' || field.datasourceType === 'api') &&
										(field.remoteUrl || field.apiUrl);
				if (hasRemoteData) {
					console.log(`Found API field: ${field.label}, datasourceType: ${field.datasourceType}, url: ${field.remoteUrl || field.apiUrl}`);
					promises.push(this.loadFieldRemoteOptions(field));
				}
				
				// Check fields within dynamic groups/tables
				if ((field.tag === 'dynamicGroup' || field.tag === 'table')) {
					// Check fields (processed format)
					if (field.fields && Array.isArray(field.fields)) {
						for (const innerField of field.fields) {
							if ((innerField.datasourceType === 'remote' || innerField.datasourceType === 'api') && innerField.remoteUrl) {
								console.log(`Found API field in group: ${innerField.label}, datasourceType: ${innerField.datasourceType}, url: ${innerField.remoteUrl}`);
								promises.push(this.loadFieldRemoteOptions(innerField));
							}
						}
					}
					// Check columns (original format for table)
					if (field.tag === 'table' && field.columns && Array.isArray(field.columns)) {
						for (const column of field.columns) {
							if ((column.datasourceType === 'remote' || column.datasourceType === 'api') && (column.remoteUrl || column.apiUrl)) {
								console.log(`Found API column: ${column.title}, datasourceType: ${column.datasourceType}, url: ${column.remoteUrl || column.apiUrl}`);
								promises.push(this.loadColumnRemoteOptions(column));
							}
						}
					}
				}
			}
			
			console.log(`Total API calls to make: ${promises.length}`);
			
			if (promises.length > 0) {
				await Promise.all(promises);
			}
		},
		async loadFieldRemoteOptions(field) {
			try {
				const url = field.remoteUrl || field.apiUrl;
				const method = (field.remoteMethod || field.apiMethod || 'GET').toUpperCase();
				
				if (!url) {
					console.log(`No URL for field ${field.label || field.id}`);
					return;
				}
				
				console.log(`Loading remote options for ${field.label || field.id} from ${url} using ${method}`);
				
				// Get user token
				const user = this.$store.getters.user;
				const token = user && user.ApiToken ? user.ApiToken : '';
				
				const fullUrl = this.baseUrl + url;
				
				console.log(`Full URL: ${fullUrl}`);
				
				// Make direct HTTP request
				const response = await new Promise((resolve, reject) => {
					uni.request({
						url: fullUrl,
						method: method,
						header: {
							'WC-Token': token,
							'Content-Type': 'application/json'
						},
						data: {},
						success: (res) => {
							console.log(`Response for ${field.label || field.id}:`, res);
							if (res.statusCode === 200) {
								resolve(res.data);
							} else {
								reject(new Error(`HTTP ${res.statusCode}: ${res.data}`));
							}
						},
						fail: (err) => {
							console.error(`Request failed for ${field.label || field.id}:`, err);
							reject(err);
						}
					});
				});
				
				if (response) {
					// Handle different response formats
					let data = response.data || response;
					
					// Parse the remote option text and value paths
					const textPath = field.remoteOptionText || (field.dataMapping && field.dataMapping.textField) || 'text';
					const valuePath = field.remoteOptionValue || (field.dataMapping && field.dataMapping.valueField) || 'value';
					
					// Convert response data to options format
					const options = this.parseRemoteData(data, textPath, valuePath);
					
					console.log(`Loaded ${options.length} options for ${field.label || field.id}`);
					
					// Update field options
					this.$set(field, 'options', options);
				}
			} catch (error) {
				console.error(`Failed to load remote options for field ${field.label || field.id}:`, error);
				// Set empty options on error
				this.$set(field, 'options', []);
			}
		},

		async loadColumnRemoteOptions(column) {
			try {
				const url = column.remoteUrl || column.apiUrl;
				const method = (column.remoteMethod || column.apiMethod || 'GET').toUpperCase();

				if (!url) {
					console.log(`No URL for column ${column.title || column.field}`);
					return;
				}

				console.log(`Loading remote options for column ${column.title || column.field} from ${url} using ${method}`);

				// Get user token
				const user = this.$store.getters.user;
				const token = user && user.ApiToken ? user.ApiToken : '';

				// Build full URL
				const fullUrl = this.baseUrl + url;

				console.log(`Full URL: ${fullUrl}`);

				// Make direct HTTP request
				const response = await new Promise((resolve, reject) => {
					uni.request({
						url: fullUrl,
						method: method,
						header: {
							'WC-Token': token,
							'Content-Type': 'application/json'
						},
						data: {},
						success: (res) => {
							console.log(`Response for ${column.title || column.field}:`, res);
							if (res.statusCode === 200) {
								resolve(res.data);
							} else {
								reject(new Error(`HTTP ${res.statusCode}: ${res.data}`));
							}
						},
						fail: (err) => {
							console.error(`Request failed for ${column.title || column.field}:`, err);
							reject(err);
						}
					});
				});

				if (response) {
					// Handle different response formats
					let data = response.data || response;

					// Parse the remote option text and value paths
					const textPath = column.remoteOptionText || (column.dataMapping && column.dataMapping.textField) || 'text';
					const valuePath = column.remoteOptionValue || (column.dataMapping && column.dataMapping.valueField) || 'value';

					// Convert response data to options format
					const options = this.parseRemoteData(data, textPath, valuePath);

					console.log(`Loaded ${options.length} options for column ${column.title || column.field}`);

					// Update column options
					this.$set(column, 'options', options);
				}
			} catch (error) {
				console.error(`Failed to load remote options for column ${column.title || column.field}:`, error);
				// Set empty options on error
				this.$set(column, 'options', []);
			}
		},

		parseRemoteData(data, textPath, valuePath) {
			// Ensure data is an array
			const dataArray = Array.isArray(data) ? data : [data];

			return dataArray.map(item => {
				// For simple field names (no dots), access directly
				let text, value;

				if (!textPath.includes('.')) {
					text = item[textPath];
				} else {
					text = this.getValueByPath(item, textPath);
				}

				if (!valuePath.includes('.')) {
					value = item[valuePath];
				} else {
					value = this.getValueByPath(item, valuePath);
				}

				return {
					text: String(text || ''),
					value: String(value || ''),
					checked: false
				};
			});
		},

		getValueByPath(obj, path) {
			// Support nested path like "options.data.dictName"
			const keys = path.split('.');
			let result = obj;

			for (const key of keys) {
				if (result && typeof result === 'object') {
					result = result[key];
				} else {
					return null;
				}
			}

			return result;
		},

		parseUploadData(dataString) {
			if (!dataString) return [];
			return dataString.split(',').map(url => {
				const name = url.substring(url.lastIndexOf('/') + 1);
				const isImage = /\.(jpg|jpeg|png|gif)$/i.test(name);
				return { name, url, type: isImage ? 'image' : 'file' };
			});
		},
		isVisible(field) {
			return this.formData.hasOwnProperty(field.id);
		},
		getPickerText(field) {
			const selectedValue = this.formData[field.id];
			if (selectedValue === null || selectedValue === undefined || selectedValue === '') {
				return '';
			}
			if (!field.options || field.options.length === 0) {
				// 选项未加载时先展示已保存的值作为占位
				console.log(`[getPickerText] field: ${field.label}, options not loaded yet, showing value: ${selectedValue}`);
				return String(selectedValue);
			}
			// 确保比较时类型一致（都转为字符串）
			const selectedOption = field.options.find(opt =>
				String(opt.value) === String(selectedValue)
			);
			console.log(`[getPickerText] field: ${field.label}, value: ${selectedValue}, options:`, field.options, `found: ${selectedOption ? selectedOption.text : 'not found'}`);
			return selectedOption ? selectedOption.text : '';
		},		
		isSimpleField(tag) {
			return !['upload', 'dynamicGroup', 'table','select'].includes(tag);
		},
		getDisplayValue(field) {
			const value = this.formData[field.id];
			if (value === null || value === undefined) return '';

			switch (field.tag) {
				case 'select':
				case 'radio':
					if (field.options) {
						const option = field.options.find(opt => String(opt.value) === String(value));
						return option ? option.text : value;
					}
					return value;
				case 'checkbox':
					if (field.options && Array.isArray(value)) {
						return value.map(val => {
							const option = field.options.find(opt => String(opt.value) === String(val));
							return option ? option.text : val;
						}).join(', ');
					}
					return Array.isArray(value) ? value.join(', ') : value;
				case 'date':
					return formatDateDay(value);
				default:
					return value;
			}
		},
		getTableFieldDisplayValue(innerField, tableData) {
			if (!tableData || typeof tableData !== 'object') return '';

			const value = tableData[innerField.id];
			if (value === null || value === undefined) return '';

			switch (innerField.tag) {
				case 'select':
				case 'radio':
					if (innerField.options) {
						const option = innerField.options.find(opt => String(opt.value) === String(value));
						return option ? option.text : value;
					}
					return value;
				case 'checkbox':
					if (innerField.options && Array.isArray(value)) {
						return value.map(val => {
							const option = innerField.options.find(opt => String(opt.value) === String(val));
							return option ? option.text : val;
						}).join(', ');
					}
					return Array.isArray(value) ? value.join(', ') : value;
				case 'date':
					return formatDateDay(value);
				default:
					return value;
			}
		},
		isImageFile(file) {
			return file.toLowerCase().endsWith('.jpg') || 
					file.toLowerCase().endsWith('.jpeg') || 
					file.toLowerCase().endsWith('.png') || 
					file.toLowerCase().endsWith('.gif');
		},
		getFileName(file) {
			return file.split('/').pop().split('.').slice(0, -1).join('.');
		},
		openFile(file) {
			uni.downloadFile({
				url: file,
				success: (res) => {
					if (res.statusCode === 200) {
						uni.openDocument({
							filePath: res.tempFilePath,
							fileType: 'file',
							success: () => {
								console.log('文件打开成功')
							},
							fail: (err) => {
								console.error('打开文件失败', err)
							}
						})
					}
				},
				fail: (err) => {
					console.error('下载文件失败', err)
				}
			})
		},
		previewImage(fids,file) {
			let files = this.formData[fids];
			// 获取对应类型的文件列表
			let imageList = [];
			files.forEach(f => {
				if(this.isImageFile(f.url)) {
					imageList.push(f.url);
				}
			});
			
			// 找到当前图片在列表中的索引
			const currentIndex = imageList.indexOf(file.url);
			
			// 预览图片
			uni.previewImage({
				urls: imageList,
				current: currentIndex
			});
		},

		// 获取审批页面类型
		getAuditPageType() {
			const flowCode = this.flowData.F_FlowCode;
			const currentNodeName = this.flowData.F_ActiveName;

			console.log('审批类型判断:', {
				flowData:this.flowData,
				flowCode,
				currentNodeName
			});

			// 当流程编码以'CM'开头且当前节点名为'评估人'时，返回'chemical_safety'
			if (flowCode.startsWith('CM') && currentNodeName === '评估人') {
				return 'chemical_safety';
			}

			// 当流程编码以'MOC'开头且当前节点名为'评估人'时，返回'safety'
			if (flowCode.startsWith('MOC') && currentNodeName === '评估人') {
				return 'safety';
			}

			// 其他情况返回'approval'
			return 'approval';
		},

		checkUserActions() {
			// 重置所有按钮显示状态
			this.showCancelBtn = false;
			this.showTransferBtn = false;
			this.showRejectBtn = false;
			this.showAgreeBtn = false;
			this.showRestartBtn = false;
			this.showActions = false;

			// 获取当前用户信息
			const currentUserId = this.userInfo.F_Id;
			const finishStatus = this.flowData.F_IsFinish; // 支持F_IsFinish或F_Status
			const creatorUserId = this.flowData.F_CreatorUserId; // 发起人ID
			const makerList = this.flowData.F_MakerList; // 审批人列表

			// 检查当前用户是否是发起人
			const isCreator = currentUserId && creatorUserId && String(currentUserId) === String(creatorUserId);
			// 检查当前用户是否在审批人列表中
			let isApprover = false;
			if (makerList && makerList !== "1") {
				const makerListArray = makerList.split(',');
				isApprover = makerListArray.includes(String(currentUserId));
			}

			// 1. 取消审批按钮：当前登录人是发起人且finish不等于3、1、2时显示
			if (isCreator && finishStatus !== 3 && finishStatus !== 1 && finishStatus !== 2) {
				this.showCancelBtn = true;
			}

			// 2. 转办、拒绝、同意按钮：当前登录人ID在markerlist中且finish为0时显示
			if (isApprover && finishStatus === 0) {
				this.showTransferBtn = true;
				this.showRejectBtn = true;
				this.showAgreeBtn = true;
			}

			// 3. 重新发起按钮：finish==3且当前登录人是发起人时显示
			if (isCreator && finishStatus === 3) {
				this.showRestartBtn = true;
			}

			// // 设置总的showActions状态
			// this.showActions = this.showCancelBtn || this.showTransferBtn || this.showRejectBtn || this.showAgreeBtn || this.showRestartBtn;
		},
		handleAction(actionType) {
			if (actionType === 'cancel') {
				// 处理取消审批逻辑
				this.handleCancelApproval();
			} else if (actionType === 'restart') {
				// 处理重新发起逻辑
				this.handleRestartApproval();
			} else if (actionType === 'transfer') {
				// 转审跳转到专用页面
				uni.navigateTo({
					url: `/pages/approval/transfer?id=${this.instanceId}`
				});
			} else {
				// 获取审批页面类型
				const auditType = this.getAuditPageType();

				// 根据操作类型设置标题
				let title = '';
				switch (actionType) {
					case 'reject':
						title = '拒绝审批';
						break;
					case 'pass':
						title = '同意审批';
						break;
					default:
						title = '审批';
				}
				console.log(`/pages/approval/audit?id=${this.instanceId}&type=${auditType}&auditType=${actionType}&title=${encodeURIComponent(title)}`)
				// 拒绝、同意跳转到audit页面，传递正确的参数
				uni.navigateTo({
					url: `/pages/approval/audit?id=${this.instanceId}&type=${auditType}&auditType=${actionType}&title=${encodeURIComponent(title)}`
				});
			}
		},
		handleCancelApproval() {
			uni.showModal({
				title: '确认取消',
				content: '确定要取消这个审批流程吗？',
				success: (res) => {
					if (res.confirm) {
						// 调用取消审批接口
						this.cancelApproval();
					}
				}
			});
		},
		handleRestartApproval() {
			uni.showModal({
				title: '确认重新发起',
				content: '确定要重新发起这个审批流程吗？',
				success: (res) => {
					if (res.confirm) {
						// 调用重新发起接口
						this.restartApproval();
					}
				}
			});
		},
		async cancelApproval() {
			try {
				uni.showLoading({ title: '取消中...' });
				const res = await this.$minApi.cancelApproval({ id: this.instanceId });
				if (res.state === "success") {
					uni.showToast({ title: '取消成功', icon: 'success' });
					// 刷新页面或返回上一页
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				} else {
					uni.showToast({ title: res.message || '取消失败', icon: 'none' });
				}
			} catch (error) {
				console.error("cancelApproval error:", error);
				uni.showToast({ title: '取消失败', icon: 'none' });
			} finally {
				uni.hideLoading();
			}
		},
		async restartApproval() {
			try {
				uni.showLoading({ title: '重新发起中...' });
				const res = await this.$minApi.restartApproval({ id: this.instanceId });
				if (res.state === "success") {
					uni.showToast({ title: '重新发起成功', icon: 'success' });
					// 刷新页面或返回上一页
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				} else {
					uni.showToast({ title: res.message || '重新发起失败', icon: 'none' });
				}
			} catch (error) {
				console.error("restartApproval error:", error);
				uni.showToast({ title: '重新发起失败', icon: 'none' });
			} finally {
				uni.hideLoading();
			}
		}
	}
}
</script>

<style lang="scss">
	.container {
		background-color: #f5f5f5;
		min-height: 100vh;
		padding-bottom: 140rpx;
	}

	.user-info {
		display: flex;
		align-items: center;
	}

	.avatar {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		margin-right: 20rpx;
	}

	.user-desc {
		font-size: 28rpx;
		color: #666;
	}
	
	.username {
		font-size: 32rpx;
		color: #333;
		margin-bottom: 8rpx;
	}
	
	.form-content {
		.form-item {
			display: flex;
			padding: 20rpx 0;
			border-bottom: 1rpx solid #f0f0f0;
			font-size: 28rpx;
			justify-content: space-between;
			&:last-child {
				border-bottom: none;
			}
			
			.label {
				// width: 220rpx;
				color: #666;
				flex-shrink: 0;
			}
			
			.value-area {
				color: #333;
			}
			
			.readonly-text {
				word-break: break-all;
			}
		}
	}
	
	.table-display-card {
		margin: 20rpx 0;
		background-color: #f8f9fa;
		border-radius: 12rpx;
		padding: 20rpx;

		.table-title {
			font-size: 32rpx;
			font-weight: 600;
			color: #333;
			margin-bottom: 20rpx;
			padding-bottom: 10rpx;
			border-bottom: 2rpx solid #e9ecef;
		}

		.table-content {
			.table-item {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 15rpx 0;
				border-bottom: 1rpx solid #e9ecef;

				&:last-child {
					border-bottom: none;
				}

				.table-label {
					font-size: 28rpx;
					color: #666;
					flex-shrink: 0;
					width: 200rpx;
				}

				.table-value {
					flex: 1;
					text-align: right;

					.readonly-text {
						font-size: 28rpx;
						color: #333;
						word-break: break-all;
					}
				}
			}
		}
	}

	.file-list {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
		padding-top: 10rpx;
		.file-item {
			position: relative;
			width: 160rpx;
			height: 160rpx;
			background-color: #f5f5f5;
			border-radius: 8rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 10rpx;
			.file-name-text {
				font-size: 24rpx;
				color: #666666;
				text-align: center;
				word-break: break-all;
			}
			image{
				width: 100%;
				height: 100%;
				border-radius: 8rpx;
				object-fit: cover;
			}
		}
	}


	.bottom-actions {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		display: flex;
		justify-content: center;
		gap: 10rpx;
		padding: 20rpx;
		background-color: #fff;
		border-top: 1rpx solid #eee;
		box-shadow: 0 -5rpx 10rpx rgba(0,0,0,0.05);
		height: 140rpx;
	}

	.action-btn {
		flex: 1;
    	height: 70rpx;
		line-height: 70rpx;
		border-radius: 100rpx;
		font-size: 28rpx;
	}
</style>