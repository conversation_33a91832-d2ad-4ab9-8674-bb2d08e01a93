<template>
	<view class="container">
		<!-- 筛选区域 -->
		<view class="filter-section">
			<!-- 搜索框 -->
			<view class="search-wrapper">
				<view class="search-box">
					<uni-icons type="search" size="20" color="#fff"></uni-icons>
					<input class="search-input" placeholder="请输入关键词搜索" placeholder-style="color:#fff" />
				</view>
			</view>

			<!-- 筛选条件 -->
			<view class="filter-controls">
				<view class="filter-row">
					<view class="filter-btn" @click="openStatusFilter">
						<text>{{ selectedStatusText || '状态' }}</text>
						<image src="/static/img/home/<USER>" mode="widthFix"></image>
					</view>
					<picker mode="selector" :range="departmentList" range-key="F_FullName" @change="onDepartmentChange">
						<view class="filter-btn">
							<text>{{ searchParams.departmentName || '部门' }}</text>
							<image src="/static/img/home/<USER>" mode="widthFix"></image>
						</view>
					</picker>
				</view>
				<view class="date-range-picker">
				        <picker mode="date" @change="onDateChange($event, 'beginTime')" :value="searchParams.beginTime">
				            <view class="date-picker-input">
				                <uni-icons type="calendar" size="16" color="#999"></uni-icons>
				                <text>{{ searchParams.beginTime || '开始日期' }}</text>
				            </view>
				        </picker>
				        <picker mode="date" @change="onDateChange($event, 'endTime')" :value="searchParams.endTime">
				            <view class="date-picker-input">
				                <uni-icons type="calendar" size="16" color="#999"></uni-icons>
				                <text>{{ searchParams.endTime || '结束日期' }}</text>
				            </view>
				        </picker>
				    </view>
			</view>
		</view>

		<!-- 任务列表 -->
		<view class="task-list">
			<view v-for="item in list" :key="item.F_Id" class="list-item-card" @click="viewDetails(item.F_Id)">
				<!-- 卡片头部 -->
				<view class="card-header">
					<view class="header-left">
						<image class="status-dot" src="/static/img/dian.svg"></image>
						<text class="code">审批编号: {{ item.F_Code }}</text>
					</view>
					<view class="header-right">
						<text class="status" :class="[getStatusClass(item.F_FlowStatus)]">{{ item.F_FlowStatus }}</text>
					</view>
				</view>

				<!-- 卡片中部 -->
				<view class="card-body">
					<view class="body-title">
						<text class="title-text">{{ item.F_SchemeName }}</text>
					</view>
					<view class="body-content">
						<view class="content-item">
							<text class="label">发起人:</text>
							<text class="value">{{ item.F_CreatorUserName }}</text>
						</view>
						<view class="content-item">
							<text class="label">发起时间:</text>
							<text class="value">{{ formatDateTime(item.F_CreatorTime) }}</text>
						</view>
					</view>
				</view>

				<!-- 卡片底部 -->
				<view class="card-footer">
					<view class="footer-left">
						<image class="avatar" src="/static/img/profile.svg"></image>
						<text class="username">{{ item.F_CreatorUserName }}</text>
					</view>
					<view class="footer-right">
						<text class="time">{{ formatDateTime(item.F_CreatorTime) }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 状态筛选弹窗 -->
		<uni-popup ref="statusPopup" type="bottom">
			<view class="status-filter-popup">
				<view class="popup-header">
					<text class="popup-title">状态筛选</text>
					<view class="popup-actions">
						<button class="reset-btn" @click="resetStatusFilter">重置</button>
						<button class="confirm-btn" @click="confirmStatusFilter">确认</button>
					</view>
				</view>
				<view class="popup-content">
					<scroll-view scroll-y="true" style="max-height: 60vh;">
						<view v-for="(group, groupIndex) in statusFilters" :key="groupIndex" class="filter-group">
							<view class="group-title">{{ group.title }}</view>
							<view class="group-items">
								<view
									v-for="(item, itemIndex) in group.items"
									:key="itemIndex"
									class="filter-item"
									:class="{ 'active': item.selected }"
									@click="toggleStatus(groupIndex, itemIndex)"
								>
									{{ item.name }}
								</view>
							</view>
						</view>
					</scroll-view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>

	export default {
		data() {
			return {
				total: 0, // 任务总数，后续从API获取
				list: [], // 任务列表
				page: 1,
				pageSize: 10,
				loading: false,
				hasMore: true,
				departmentList: [],
				searchParams: {
					departmentId: null,
					departmentName: '',
					startDate: '',
					endDate: ''
				},
				selectedStatusText: '', // 用于显示选中的状态文本
				statusFilters: [
					{
						title: '变更审批',
						items: [
							{ name: '待评估', selected: false },
							{ name: '待审批', selected: false },
							{ name: '待验证', selected: false },
							// { name: '已验证', selected: false },
							// { name: '已取消', selected: false },
							// { name: '已拒绝', selected: false },
							// { name: '已驳回', selected: false }
						]
					},
					{
						title: '新化学品审批',
						items: [
							{ name: '内部批准', selected: false },
							{ name: 'EHS评估', selected: false },
							{ name: 'EHS批准', selected: false },
							// { name: '已通过', selected: false },
							// { name: '已取消', selected: false },
							// { name: '已拒绝', selected: false },
							// { name: '已驳回', selected: false }
						]
					},
					{
						title: '外部持入化学品审批',
						items: [
							{ name: '内部审批', selected: false },
							{ name: 'EHS审批', selected: false },
							// { name: '已通过', selected: false },
							// { name: '已取消', selected: false },
							// { name: '已拒绝', selected: false },
							// { name: '已驳回', selected: false }
						]
					},
					{
						title: '双休及节假日工作审批',
						items: [
							{ name: '内部审批', selected: false },
							{ name: 'EHS审批', selected: false },
							// { name: '已通过', selected: false },
							// { name: '已取消', selected: false },
							// { name: '已拒绝', selected: false },
							// { name: '已驳回', selected: false }
						]
					}
				],
				selectedStatus: {}
			};
		},
		onLoad() {
			this.loadInitialData();
			this.getList(true);
		},
		onShow() {
			this.loadInitialData();
			this.getList(true);
		},
		onPullDownRefresh() {
			// 下拉刷新
			this.page = 1;
			this.getList(true);
		},
		onReachBottom() {
			// 上拉加载
			if (this.hasMore && !this.loading) {
				this.page++;
				this.getList();
			}
		},
		methods: {
			async loadInitialData() {
				try {
					const deptRes = await this.$minApi.getOrgList();
					if (Array.isArray(deptRes)) {
						this.departmentList = deptRes;
					} else {
						uni.showToast({ title: '部门列表加载失败', icon: 'none' });
					}
				} catch (error) {
					console.error("Department data loading failed:", error);
					uni.showToast({ title: '部门数据加载异常', icon: 'none' });
				}
			},
			getList(refresh = false) {
				if (this.loading) return;
				this.loading = true;

				const params = {
					page: this.page,
					rows: this.pageSize,
					status: JSON.stringify(this.selectedStatus),
					departmentId: this.searchParams.departmentId,
					startDate: this.searchParams.startDate,
					endDate: this.searchParams.endDate
				};

				this.$minApi.GetFlowInstanceList(params).then(res => {
					if (refresh) {
						this.list = [];
					}
					const newList = res.data.rows || [];
					this.list = this.list.concat(newList);
					this.total = res.total || 0;
					this.hasMore = newList.length === this.pageSize;
					
					this.loading = false;
					uni.stopPullDownRefresh();
				}).catch(() => {
					this.loading = false;
					uni.stopPullDownRefresh();
				});
			},
			handleSearch() {
				this.page = 1;
				this.getList(true);
			},
			openStatusFilter() {
				this.$refs.statusPopup.open();
			},
			toggleStatus(groupIndex, itemIndex) {
				this.statusFilters[groupIndex].items[itemIndex].selected = !this.statusFilters[groupIndex].items[itemIndex].selected;
			},
			resetStatusFilter() {
				// 重置状态筛选
				this.statusFilters.forEach(group => {
					group.items.forEach(item => {
						item.selected = false;
					});
				});
			},
			confirmStatusFilter() {
				// 确认状态筛选
				let selectedCount = 0;
				this.selectedStatus = this.statusFilters.reduce((acc, group) => {
					group.items.forEach(item => {
						if (item.selected) {
							selectedCount++;
							if (!acc[group.title]) {
								acc[group.title] = [];
							}
							acc[group.title].push(item.name);
						}
					});
					return acc;
				}, {});
				
				// 更新显示的状态文本
				if (selectedCount > 0) {
					this.selectedStatusText = `状态(${selectedCount})`;
				} else {
					this.selectedStatusText = '';
				}
				
				this.page = 1;
				this.getList(true);
				this.$refs.statusPopup.close();
			},
			openDeptFilter() {
				// 打开部门筛选
			},
			onDepartmentChange(e) {
				const selectedIndex = e.detail.value;
				const selectedDept = this.departmentList[selectedIndex];
				if (selectedDept) {
					this.searchParams.departmentId = selectedDept.F_Id;
					this.searchParams.departmentName = selectedDept.F_FullName;
					this.handleSearch();
				}
			},
			viewDetails(id) {
				uni.navigateTo({
					url: `/pages/approval/detail?id=${id}`
				});
			},
			// 格式化状态样式
			getStatusClass(status) {
				switch (status) {
					case '待处理':
					case '待审批':
						return 'status-approving';
					case '已驳回':
					case '已拒绝':
					case '已取消':
						return 'status-rejected';
					case '已通过':
					case '已完成':
						return 'status-verified';
					default:
						return 'status-pending';
				}
			},
			// 格式化时间
			formatDateTime(dateTime) {
				if (!dateTime) return '';
				return dateTime.split(' ');
			},
		}
	};
</script>

<style lang="scss" scoped>
	.container {
		width: 100%;
		min-height: 100vh;
		background-color: #F6F7FC;
	}

	.filter-section {
		width: 100%;
	}

	.search-wrapper {
		background-color: #00984a; /* 主题绿色 */
		padding: 20rpx;
		border-bottom-left-radius: 30rpx;
		border-bottom-right-radius: 30rpx;
	}

	.search-box {
		display: flex;
		align-items: center;
		height: 72rpx;
		padding: 0 30rpx;
		background-color: rgba(255, 255, 255, 0.3);
		border-radius: 36rpx;
	}

	.search-input {
		flex: 1;
		margin-left: 16rpx;
		font-size: 28rpx;
		color: #fff;
	}

	.filter-controls {
		padding: 0rpx 20rpx;
		border-radius: 16rpx;
	}
	.date-range-picker {
		display: flex;
		justify-content: space-between;
		gap: 20rpx; /* 增加间距 */
		margin-bottom: 20rpx;
	}

	.date-picker-input {
		display: flex;
		align-items: center;
		width: 42vw;
		padding: 16rpx 24rpx;
		background-color: #FFFFFF;
		border-radius: 20rpx;
		font-size: 26rpx;
		color: #8D949C;
		box-sizing: border-box;
	}

	.date-picker-input text {
		margin-left: 16rpx;
	}

	.filter-row {
		display: flex;
		justify-content: center;
		align-items: center;
		gap: 16rpx;
	}

	.filter-row:first-child {
		margin-bottom: 16rpx;
	}

	.filter-btn {
		// flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		height: 68rpx;
		padding: 0 20rpx;
		gap: 10rpx;
		border-radius: 8rpx;
		font-size: 28rpx;
		color: #4C5156;
		transition: all 0.2s;
		image{
			width: 19.44rpx;
			height: 18rpx;
		}
	}

	.filter-btn:active {
		background-color: #f2f3f5;
	}

	.filter-btn text {
		flex: 1;
		text-align: left;
	}

	.date-picker {
		flex: 1;
		height: 68rpx;
		background-color: #f7f8fa;
		border-radius: 8rpx;
		border: 1rpx solid #ebedf0;
	}
	
	// 日期选择器内部样式调整
	.date-picker ::v-deep .uni-date {
		height: 68rpx;
		padding: 0 20rpx;
	}
	
	.date-picker ::v-deep .uni-date-x {
		background-color: transparent;
		border: none;
		height: 68rpx;
		padding: 0;
	}
	
	.date-picker ::v-deep .uni-date-x--border {
		border: none;
	}
	
	.date-picker ::v-deep .uni-date-editor--x {
		height: 68rpx;
		display: flex;
		align-items: center;
	}
	
	.date-picker ::v-deep .uni-date__icon-clear {
		display: none;
	}
	
	.date-picker ::v-deep input {
		font-size: 28rpx;
		color: #333;
		height: 68rpx;
		line-height: 68rpx;
	}
	
	.date-picker ::v-deep .placeholder {
		color: #999;
		font-size: 28rpx;
	}

	.task-list {
		padding: 20rpx;
	}

	.list-item-card {
		background-color: white;
		border-radius: 16rpx;
		margin: 0 0 20rpx;
		padding: 30rpx;
		box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
	}
	.card-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding-bottom: 20rpx;
		border-bottom: 2rpx solid #f5f5f5;
	}
	.header-left {
		display: flex;
		align-items: center;
	}
	.status-dot {
		width: 16rpx;
		height: 16rpx;
		margin-right: 16rpx;
	}
	.code {
		font-size: 28rpx;
		color: #333;
	}
	.status {
		font-size: 28rpx;
		font-weight: bold;
	}
	.status-pending { color: #2979ff; }
	.status-approving { color: #ff9800; }
	.status-rejected { color: #f44336; }
	.status-verified { color: #4caf50; }
	.card-body {
		padding: 30rpx 0;
	}
	.body-title {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}
	.title-text {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}
	.content-item {
		display: flex;
		font-size: 28rpx;
		color: #666;
		line-height: 1.8;
	}
	.label {
		width: 160rpx;
		color: #999;
	}
	.value {
		flex: 1;
	}
	.card-footer {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding-top: 20rpx;
		border-top: 2rpx solid #f5f5f5;
	}
	.footer-left {
		display: flex;
		align-items: center;
	}
	.avatar {
		width: 60rpx;
		height: 60rpx;
		border-radius: 50%;
		margin-right: 16rpx;
	}
	.username {
		font-size: 28rpx;
		color: #333;
	}
	.time {
		font-size: 26rpx;
		color: #999;
	}

	.status-filter-popup {
		background-color: #ffffff;
		border-top-left-radius: 20rpx;
		border-top-right-radius: 20rpx;
	}

	.popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx 0rpx;
		border-bottom: 1rpx solid #eee;
	}

	.popup-title {
		font-size: 40rpx;
		font-weight: bold;
		color: #23262A;
	}

	.popup-actions {
		display: flex;
	}

	.reset-btn,
	.confirm-btn {
		height: 60rpx;
		line-height: 60rpx;
		padding: 0 30rpx;
		font-size: 28rpx;
		border-radius: 30rpx;
	}

	.reset-btn {
		background-color: #f0f0f0;
		color: #333;
		margin-right: 20rpx;
	}

	.confirm-btn {
		background-color: #00984a;
		color: #ffffff;
	}

	.popup-content {
		padding: 30rpx 0rpx;
	}

	.filter-group {
		margin-bottom: 20rpx;
	}

	.group-title {
		font-size: 30rpx;
		color: #23262A;
		margin-bottom: 20rpx;
	}

	.group-items {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
	}

	.filter-item {
		padding: 16rpx 30rpx;
		background-color: #f5f5f5;
		border-radius: 8rpx;
		font-size: 28rpx;
		color: #333;
	}

	.filter-item.active {
		background-color: #00984a;
		color: #ffffff;
	}
</style>