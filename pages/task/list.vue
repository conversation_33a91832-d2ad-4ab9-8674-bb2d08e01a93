<template>
	<view class="container">
		<uni-nav-bar :fixed="true" color="#333333" :background-color="themeBgColor" :border="false">
			<view class="input-view">
				<uni-icons type="search" size="22" color="#666666" />
				<input v-model="searchVal" confirm-type="search" class="input" type="text" placeholder="搜索" @confirm="search">
				<uni-icons :color="'#999999'" v-if="searchVal!==''" class="icon-clear" type="clear" size="22" @click="clear" />
			</view>
		</uni-nav-bar>
		<view class="filters">
			<view class="item">
				<label>状态</label>
				<picker mode="selector" @change="onStatusChange" :value="StatusIndex" range-key="text" :range="StatusList">
					<view class="uni-input">{{StatusList[StatusIndex].text}}</view>
				</picker>
			</view>
			<view class="item">
				<label>日期</label>
				<picker mode="date" @change="onStartDateChange" :value="StartDate" :end="EndDate">
					<view class="uni-input">{{ StartDate }}</view>
				</picker>
				<view style="margin-left: 10rpx;margin-right: 10rpx;"> - </view>
				<picker mode="date" @change="onEndDateChange" :start="StartDate" :value="EndDate">
					<view class="uni-input">{{ EndDate }}</view>
				</picker>
			</view>
		</view>
		<view class="card-list">
			<view class="title">任务列表（{{total}}个）</view>
			<view class="card" v-for="item in ContractorList" :key="item.F_Id">
				<navigator :url="`/pages/task/info?id=${item.F_Id}&type=simple`" open-type="navigate" class="navbar" v-if="$checkBtnAuth('zytzck')">
					<view class="header">
						<view class="title">任务编号：{{ item.F_Num}}</view>
						<view class="status">
							<text :class="{ 'processing': item.F_Status == '0', 'complate': item.F_Status == '1' }">{{ item.F_Status=='0' ? '进行中' : '已完成' }}</text>
						</view>
					</view>
					<view class="content">
						<view class="title">{{ item.commpanyName }}</view>
						<view>作业内容：{{ item.F_Content}}</view>
						<view>现场负责人：{{ item.F_Leader }}</view>
						<view>施工人员：{{ item.staffNames }}</view>
						<view class="bottom">
							<view class="user"><image src="/static/img/profile.svg"></image>{{ item.F_CreatorUserName ? item.F_CreatorUserName : '--' }}提交</view>
							<view class="clock"><image src="/static/img/clock.svg"></image>{{ item.F_CompleteTime ? item.F_CompleteTime : '--' }}</view>
						</view>
					</view>
				</navigator>
				<view class="navbar" v-else>
					<view class="header">
						<view class="title">任务编号：{{ item.F_Num}}</view>
						<view class="status">
							<text :class="{ 'processing': item.F_Status == '0', 'complate': item.F_Status == '1' }">{{ item.F_Status=='0' ? '进行中' : '已完成' }}</text>
						</view>
					</view>
					<view class="content">
						<view class="title">{{ item.commpanyName }}</view>
						<view>作业内容：{{ item.F_Content}}</view>
						<view>现场负责人：{{ item.F_Leader }}</view>
						<view>施工人员：{{ item.staffNames }}</view>
						<view class="bottom">
							<view class="user"><image src="/static/img/profile.svg"></image>{{ item.F_CreatorUserName ? item.F_CreatorUserName : '--' }}提交</view>
							<view class="clock"><image src="/static/img/clock.svg"></image>{{ item.F_CompleteTime ? item.F_CompleteTime : '--' }}</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { mapGetters } from 'vuex'
	import uniIcons from '@/components/uni-icons/uni-icons.vue'
	import uniNavBar from '@/components/uni-nav-bar/uni-nav-bar.vue'
	import globalConfig from '@/config'
	export default {
		components: {
			uniIcons,
			uniNavBar
		},
		computed: {
			...mapGetters(['themeBgColor', 'darkMode']),
		},
		data() {
			const now = new Date()
			const year = now.getFullYear()
			const month = String(now.getMonth() + 1).padStart(2, '0')
			const day = String(now.getDate()).padStart(2, '0')
			const today = `${year}-${month}-${day}`
			
			return {
				baseUrl:'',
				searchVal: '',
				// 窗口高度
				winHeight: "",
				value: 1,
				StatusList: [
					{ value: '', text: "全部" },
					{ value: '0', text: "进行中" },
					{ value: '1', text: "已完成" },
				],
				StatusIndex: 0,
				ContractorList: [],
				StartDate: '',
				EndDate: '',
				// 分页参数
				queryParams: {
					page: 1,
					rows: 10,
					field: 'F_CreatorTime',
					order: 'desc',
					keyword: '',
					status: '',
					beginTime: '',
					endTime: ''
				},
				// 加载状态
				loading: false,
				// 是否还有更多数据
				isEnd: false,
				// 总数
				total: 0
			}
		},
		onShareAppMessage() {
		    return {
		      title: 'EHS-掌上观纹', // 自定义分享标题
		      path: '/pages/start/index', // 分享页面路径（默认当前页）
		      imageUrl: this.baseUrl+'/images/nideke.jpg', // 分享图片（可选）
		      success(res) {
		        console.log('分享成功', res);
		      },
		      fail(err) {
		        console.error('分享失败', err);
		      }
		    };
		},
		onReady() {
			uni.setNavigationBarTitle({
			    title: this.$t('承包商作业台账')
			})
			this.setNavBarColor()
		},
		onShow() {
			this.setNavBarColor()
			this.loadData()
		},
		onLoad() {
			this.baseUrl = globalConfig.baseUrl.replace("/api","");
			//  高度自适应
			uni.getSystemInfo({
				success: res => {
					this.winHeight = res.windowHeight
				}
			})
			// 初始化查询参数
			this.queryParams.beginTime = this.StartDate
			this.queryParams.endTime = this.EndDate
			this.loadData()
		},
		// 触底加载更多
		onReachBottom() {
			if (this.loading || this.isEnd) return
			this.queryParams.page++
			this.loadData()
		},
		methods: {
			// 加载数据
			async loadData() {
				if (this.loading) return
				this.loading = true
				
				try {
					const res = await this.$minApi.getTaskList(this.queryParams)
					
					if (res.state === 0) {
						const { data, count } = res
						this.total = count
						
						// 如果是第一页，直接赋值
						if (this.queryParams.page === 1) {
							this.ContractorList = data
						} else {
							// 否则追加数据
							this.ContractorList = [...this.ContractorList, ...data]
						}
						
						// 判断是否还有更多数据
						this.isEnd = this.ContractorList.length >= count
					} else {
						uni.showToast({
							title: res.msg || '加载失败1',
							icon: 'none'
						})
					}
				} catch (e) {
					uni.showToast({
						title: '加载失败2',
						icon: 'none'
					})
				} finally {
					this.loading = false
				}
			},
			onEndDateChange(e) {
				this.EndDate = e.detail.value
				this.queryParams.endTime = e.detail.value
				this.resetAndSearch()
			},
			onStartDateChange(e) {
				this.StartDate = e.detail.value
				this.queryParams.beginTime = e.detail.value
				this.resetAndSearch()
			},
			onStatusChange(e) {
				// 更新当前选择的索引
				this.StatusIndex = e.detail.value
				this.queryParams.status = this.StatusList[e.detail.value].value
				this.resetAndSearch()
			},
			setNavBarColor() {
				// navBar-bg-color
				uni.setNavigationBarColor({
					frontColor: '#000000',
					backgroundColor: this.themeBgColor,
					animation: {
						duration: 400,
						timingFunc: 'easeIn'
					}
				})
			},
			search() {
				this.queryParams.keyword = this.searchVal
				this.resetAndSearch()
			},
			clear() {
				this.searchVal = ''
				this.queryParams.keyword = ''
				this.resetAndSearch()
			},
			// 重置分页并搜索
			resetAndSearch() {
				this.queryParams.page = 1
				this.isEnd = false
				this.loadData()
			}
		}
	}
</script>

<style lang="less">

	.container{
		height: 100vh;
		background-color: white;
		.filters{
			display: flex;
			flex-direction: row;
			align-items: center;
			background-color: white;
			padding: 20rpx;
			width: 100%;
			flex-wrap: wrap;
			gap: 10rpx;
			.item{
				display: flex;
				flex-direction: row;
				align-items: center;
				flex: 1;
				label{
					color: #4c4c4c;
					// width: 20%;
					padding: 10rpx;
					align-items: center;
					text-align: center;
					line-height: 20rpx;
				}
				.uni-input{
					color: #7c7c7c;
					border: 1px solid #00984a;
					align-items: center;
					text-align: left;
					width: 36vw;
					border-radius: 10rpx !important;
					line-height: 20rpx;
				}
			}
		}
		.card-list{
			display: flex;
			flex-direction: column;
			align-items: left;
			justify-content: start;
			width: 100vw;
			padding: 30rpx;
			gap: 20rpx;
			.title{
				font-size: 32rpx;
				font-weight: 400;
				color: #333333;
			}
			.card{
				color: #999999;
				border: 1rpx solid rgba(242, 242, 242, 1);
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				width: 100%;
				background-color: white;
				border-radius: 10rpx;
				.navbar{
					width: 100%;
				}
				.header{
					width: 100%;
					border-bottom: 1rpx solid rgba(242, 242, 242, 1);
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: space-between;
					padding: 20rpx;
					box-sizing: border-box;
					.title{
						color: #333333;
						font-size: 28rpx;
						font-weight: 300;
					}
					.status{
						
					}
					.processing{
						color: #F77214;
					}
					.complate{
						color: #999999;
					}
				}
				.content{
					padding: 20rpx;
					width: 100%;
					display: flex;
					flex-direction: column;
					align-items: left;
					// font-weight: 300;
					.title{
						color: #333333;
						font-size: 32rpx;
						font-weight: 400;
						margin-bottom: 20rpx;
					}
					.bottom{
						display: flex;
						align-items: center;
						justify-content: space-between;
						gap:10rpx;
						margin-top: 30rpx;
						line-height: 40rpx;
						.user{
							display: flex;
							align-items: center;
							gap: 10rpx;
							line-height: 40rpx;
							image{
								width: 50rpx;
								height: 50rpx;
							}
						}
						.clock{
							display: flex;
							align-items: center;
							line-height: 40rpx;
							gap: 5rpx;
							image{
								width: 30rpx;
								height: 30rpx;
							}
						}
					}
				}
			}
		}
	}
</style>