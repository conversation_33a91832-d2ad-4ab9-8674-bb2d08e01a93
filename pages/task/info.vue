<template>
	<view class="container">
		<view class="nav-bar" v-if="!isSimpleMode">
			<view class="tab-list">
				<!-- 承包商作业 tab，默认显示 -->
				<view class="tab-item" v-if="MainTask.F_Type == 101" :class="{'active': CurrentTask.F_Risk === 200}" @click="switchTab(200)">
					<text>承包商作业</text>
				</view>
				<!-- 动火作业 tab -->
				<view class="tab-item" v-if="MainTask.HasFire" :class="{'active': CurrentTask.F_Risk === 201}" @click="switchTab(201)">
					<text>动火作业</text>
				</view>
				<!-- 登高作业 tab -->
				<view class="tab-item" v-if="MainTask.HasHight" :class="{'active': CurrentTask.F_Risk === 202}" @click="switchTab(202)">
					<text>登高作业</text>
				</view>
				<!-- 吊装作业 tab -->
				<view class="tab-item" v-if="MainTask.HasDiaoZhuang" :class="{'active': CurrentTask.F_Risk === 203}" @click="switchTab(203)">
					<text>吊装作业</text>
				</view>
				<!-- 有限空间作业 tab -->
				<view class="tab-item" v-if="MainTask.HasKongjian" :class="{'active': CurrentTask.F_Risk === 204}" @click="switchTab(204)">
					<text>有限空间作业</text>
				</view>
			</view>
		</view>
		<view class="header">
			<view class="title">{{ ContractorInfo.F_CompanyName }}</view>
			<view class="detail">
				<view class="num">任务编号：{{ CurrentTask.F_Num }}</view>
				<view class="status" :class="{'status-green': CurrentTask.F_Status == '1', 'status-red': CurrentTask.F_Status == '0'}">
					{{ CurrentTask.F_Status == '1' ? '已完成' : '进行中' }}
				</view>
			</view>
		</view>
		<view class="info">
			<view class="title">任务信息</view>
			<view class="content">
				<view class="item">
					<label class="label">作业类型</label>
					<view class="text">{{ CurrentTask.F_Type == 101 ? '承包商作业':'内部作业' }}</view>
				</view>
				<view class="item">
					<label class="label">作业申请时间</label>
					<view class="text">{{ CurrentTask.F_ApplyTime }}</view>
				</view>
				<view class="item">
					<label class="label">预计完成时间</label>
					<view class="text">{{ CurrentTask.F_ExpectTime }}</view>
				</view>
				<view class="item">
					<label class="label">作业内容</label>
					<view class="text">{{ CurrentTask.F_Content }}</view>
				</view>
				<view class="item">
					<label class="label">现场负责人</label>
					<view class="text">{{ CurrentTask.F_Leader }}</view>
				</view>
				<view class="item">
					<label class="label">施工人员</label>
					<view class="staff-list">
						<view class="staff-item" v-for="item in CurrentTask.staffList" :key="item.F_Id">
							<image :src="item.F_Photo? baseUrl + item.F_Photo : '/static/img/profile.svg'" mode="aspectFill"></image>
							<label class="label">{{ item.F_Name }}</label>
						</view>
					</view>
				</view>
				<view class="item">
					<label class="label">施工申请表</label>
					<view class="image-list" v-if="CurrentTask.F_ApplyTable">
						<view class="image-item" v-for="(url, index) in getImageList(CurrentTask.F_ApplyTable)" :key="index">
							<image 
								:src="baseUrl + url" 
								mode="aspectFit"
								class="apply-image"
								@click="previewImage(url)"
							/>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="info">
			<view class="title">公司信息</view>
			<view class="content">
				<view class="item">
					<label class="label">公司名称</label>
					<view class="text">{{ ContractorInfo.F_CompanyName }}</view>
				</view>
				<view class="item">
					<label class="label">公司地址</label>
					<view class="text">{{ ContractorInfo.F_Address }}</view>
				</view>
				<view class="item">
					<label class="label">承包商种类</label>
					<view class="text">{{ ContractorInfo.F_TypeName == 'DailyType' ? '日常性承包商' : '间断性承包商' }}</view>
				</view>
				<view class="item" v-if="ContractorInfo.F_License">
					<label class="label">营业执照</label>
					<image :src="baseUrl + ContractorInfo.F_License" mode="aspectFit" class="license-image" @click="previewImage(ContractorInfo.F_License)"></image>
				</view>
			</view>
		</view>
		<view class="botomm-btn" v-if="CurrentTask.F_Status==0">
			<navigator :url="`/pages/task/edit?id=${id}`" open-type="navigate" v-if="$checkBtnAuth('zytzbj') && $checkBtnAuth('cb_task') && CurrentTask.F_Risk == 200">
				<view class="btn">编辑任务</view>
			</navigator>
			<navigator :url="`/pages/task/edit?id=${id}`" open-type="navigate" v-else-if="$checkBtnAuth('zytzbj') && $checkBtnAuth('dh_task') && CurrentTask.F_Risk == 201">
				<view class="btn">编辑任务</view>
			</navigator>
			<navigator :url="`/pages/task/edit?id=${id}`" open-type="navigate" v-else-if="$checkBtnAuth('zytzbj') && $checkBtnAuth('dg_task') && CurrentTask.F_Risk == 202">
				<view class="btn">编辑任务</view>
			</navigator>
			<navigator :url="`/pages/task/edit?id=${id}`" open-type="navigate" v-else-if="$checkBtnAuth('zytzbj') && $checkBtnAuth('dz_task') && CurrentTask.F_Risk == 203">
				<view class="btn">编辑任务</view>
			</navigator>
			<navigator :url="`/pages/task/edit?id=${id}`" open-type="navigate" v-else-if="$checkBtnAuth('zytzbj') && $checkBtnAuth('kj_task') && CurrentTask.F_Risk == 204">
				<view class="btn">编辑任务</view>
			</navigator>
			<view class="btn" @click.native="complateTask" v-if="$checkBtnAuth('zyjs') && $checkBtnAuth('cb_task') && CurrentTask.F_Risk == 200">结束任务</view>
			<view class="btn" @click.native="complateTask" v-else-if="$checkBtnAuth('zyjs') && $checkBtnAuth('dh_task') && CurrentTask.F_Risk == 201">结束任务</view>
			<view class="btn" @click.native="complateTask" v-else-if="$checkBtnAuth('zyjs') && $checkBtnAuth('dg_task') && CurrentTask.F_Risk == 202">结束任务</view>
			<view class="btn" @click.native="complateTask" v-else-if="$checkBtnAuth('zyjs') && $checkBtnAuth('dz_task') && CurrentTask.F_Risk == 203">结束任务</view>
			<view class="btn" @click.native="complateTask" v-else-if="$checkBtnAuth('zyjs') && $checkBtnAuth('kj_task') && CurrentTask.F_Risk == 204">结束任务</view>
		</view>
	</view>
</template>

<script>
	import { mapGetters } from 'vuex'
	import wButton from '@/components/watch-login/watch-button.vue'
	import globalConfig from '@/config'
	
	export default{
		components: {
			wButton,
		},
		computed: {
			...mapGetters(['themeBgColor', 'darkMode', 'user']),
			currentComponent() {
				const componentMap = {
					2: 'contractor-work',
					3: 'fire-work',
					4: 'height-work',
					5: 'lifting-work',
					6: 'confined-space-work'
				}
				return componentMap[this.currentTab] || null
			}
		},
		data(){
			return{
				id: '', // 任务ID
				type: '', // 任务类型：simple或group
				isSimpleMode: false, // 是否简单模式
				baseUrl: '',
				currentTab: 0, // 当前选中的tab
				MainTask: {},
				CurrentTask: {},
				ContractorInfo: {}
			}
		},
		onReady() {
			uni.setNavigationBarTitle({
			    title: this.$t('任务详情')
			})
			this.setNavBarColor()
		},
		onShow() {
			this.setNavBarColor()
		},
		onLoad(options) {
			if (options.id) {
				this.id = options.id
				this.type = options.type || 'group'
				this.isSimpleMode = this.type === 'simple'
				this.baseUrl = globalConfig.baseUrl.replace('/api', '')
				this.loadTaskInfo()
			}
		},
		onShareAppMessage() {
		    return {
		      title: 'EHS-掌上观纹', // 自定义分享标题
		      path: '/pages/start/index', // 分享页面路径（默认当前页）
		      imageUrl: this.baseUrl+'/images/nideke.jpg', // 分享图片（可选）
		      success(res) {
		        console.log('分享成功', res);
		      },
		      fail(err) {
		        console.error('分享失败', err);
		      }
		    };
		},
		methods:{
			checkPermission(){
				if(this.user.F_RoleId === "08dc7a3d-e5a8-44cf-87ea-15347b9fa336"){
					return false;
				}else{
					return true;
				}
			},
			complateTask(){
				uni.showLoading({
					title: '处理中...'
				})
				this.$minApi.completeTask(this.CurrentTask.F_Id).then(res => {
					uni.showLoading({
						title: '加载中...'
					})
					if(res.state == 'success'){
						uni.showToast({
							title: '操作成功',
							icon: 'none'
						})
						this.loadTaskInfo();
					}else{
						uni.showToast({
							title: '操作失败',
							icon: 'none'
						})
					}
				});
			},
			setNavBarColor() {
				uni.setNavigationBarColor({
				    frontColor: '#000000',
				    backgroundColor: this.themeBgColor,
				    animation: {
				        duration: 400,
				        timingFunc: 'easeIn'
				    }
				})
			},
			loadTaskInfo() {
				uni.showLoading({
					title: '加载中...'
				})
				
				// 根据type选择不同的API
				const apiMethod = this.isSimpleMode ? 'getSimpleTaskInfo' : 'getGroupTaskInfo'
				
				this.$minApi[apiMethod](this.id).then(res => {
					if(res) {
						if(this.isSimpleMode){
							this.CurrentTask = res;
							this.MainTask = res;
						}else{
							this.MainTask = {
								...res
							}
							this.CurrentTask = res.MainTask
						}
						this.ContractorInfo = this.CurrentTask.contractorInfo
						this.MainTask.staffList = this.CurrentTask.staffList
					}
				}).catch((eee) => {
					uni.showToast({
						title: '加载失败',
						icon: 'none'
					})
				}).finally(() => {
					uni.hideLoading()
				})
			},
			// 获取图片列表
			getImageList(imageStr) {
				if(!imageStr) return []
				return imageStr.split(',')
			},
			// 预览图片
			previewImage(url) {
				const urls = [this.baseUrl + url]
				uni.previewImage({
					urls: urls,
					current: urls[0]
				})
			},
			// 切换tab
			switchTab(risk) {
				if (this.CurrentTask.F_Risk === risk) return
				if(risk == 200){
					this.CurrentTask = this.MainTask.MainTask;
					this.ContractorInfo = this.MainTask.MainTask.contractorInfo;
					// this.staffList = this.MainTask.MainTask.staffList;
				}else if(risk == 201){
					this.CurrentTask = this.MainTask.FireTask;
					this.ContractorInfo = this.MainTask.FireTask.contractorInfo;
				}else if(risk == 202){
					this.CurrentTask = this.MainTask.HightTask;
					this.ContractorInfo = this.MainTask.HightTask.contractorInfo;
				}else if(risk == 203){
					this.CurrentTask = this.MainTask.HasDiaoZhuang;
					this.ContractorInfo = this.MainTask.HasDiaoZhuang.contractorInfo;
				}else if(risk == 204){
					this.CurrentTask = this.MainTask.KongJianTask;
					this.ContractorInfo = this.MainTask.KongJianTask.contractorInfo;
				}
				// uni.showLoading({
				// 	title: '加载中...'
				// })
				
				// // 调用获取特定风险类型任务的接口
				// this.$minApi.getGroupTaskInfo(this.id, risk).then(res => {
				// 	const { MainTask, staffList } = res
				// 	// 更新任务信息，保持原有的 Has* 标志不变
				// 	this.CurrentTask = res;
				// }).catch(() => {
				// 	uni.showToast({
				// 		title: '切换失败',
				// 		icon: 'none'
				// 	})
				// }).finally(() => {
				// 	uni.hideLoading()
				// })
			}
		}
	}
</script>

<style lang="less" scoped>
	.container{
		display: flex;
		flex-direction: column;
		gap: 20rpx;
		min-height: 100vh;
		padding-bottom: 320rpx;
		.header{
			background-color: white;
			padding: 20rpx;
			display: flex;
			flex-direction: column;
			width: 100vw;
			.title{
				color: #333333;
				font-weight: 650;
				font-size: 36rpx;
				width: 100%;
			}
			.detail{
				display: flex;
				justify-content: space-between;
				.num{
					color: #999999;
					font-size: 24rpx;
				}
				.status {
					width: 140rpx;
					height: 30rpx;
					color: #ffffff;
					font-size: 24rpx;
					align-items: center;
					text-align: center;
					line-height: 34rpx;
					clip-path: polygon(20% 0%, 100% 0%, 80% 100%, 0% 100%);
				}
				.status-green {
				  background-color: green;
				}
				.status-red {
				  background-color: rgb(245, 154, 35);
				}
			}
		}
		.nav-bar {
			background-color: white;
			padding: 20rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			.tab-list {
				display: flex;
				gap: 20rpx;
				.tab-item {
					padding: 10rpx 20rpx;
					background-color: #f0f0f0;
					border-radius: 10rpx;
					text-align: center;
					&.active {
						background-color: #0052D4;
						color: #FFFFFF;
					}
					text {
						font-size: 28rpx;
					}
				}
			}
		}
		.info {
			background-color: white;
			padding: 20rpx;
			.title{
				color: #333333;
				font-weight: 400;
				font-size: 28rpx;
				margin-bottom: 20rpx;
			}
			.content{
				display: flex;
				flex-direction: column;
				gap: 20rpx;
				.item{
					display: flex;
					flex-direction: column;
					.label{
						color: #999999;
						font-size: 24rpx;
					}
					.text{
						color: #666666;
						font-size: 28rpx;
					}
				}
			}
		}
		.staff-list{
			display: flex;
			flex-wrap: wrap;
			gap: 20rpx;
			.staff-item{
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				width: 160rpx;
				image{
					width: 120rpx;
					height: 120rpx;
					border-radius: 60rpx;
				}
				.label{
					color: #666666;
					font-size: 28rpx;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
					width: 100%;
					text-align: center;
					margin-top: 10rpx;
				}
			}
		}
		.image-list {
			display: flex;
			flex-wrap: wrap;
			gap: 20rpx;
			margin-top: 10rpx;
			.image-item {
				position: relative;
				.apply-image {
					width: 200rpx;
					height: 160rpx;
					border-radius: 8rpx;
				}
			}
		}
		.license-image {
			width: 200rpx;
			height: 160rpx;
			border-radius: 8rpx;
			margin-top: 10rpx;
		}
		.botomm-btn {
			padding-bottom: 100rpx;
			height: 230rpx;
			position: fixed; /* 固定在屏幕底部 */
			bottom: 0;
			left: 0;
			width: 100%; /* 占满整个屏幕宽度 */
			background-color: #fff; /* 背景颜色，防止透明 */
			padding: 20rpx 0; /* 上下留白 */
			text-align: center; /* 让按钮居中 */
			align-items: center;
			display: flex;
			justify-content: center;
			box-shadow: 0 -10rpx 20rpx rgba(0, 0, 0, 0.1); /* 添加阴影，提升层次感 */
			z-index: 999; /* 确保层级较高 */
			gap: 30rpx;
			
			.btn{
				font-size: 30rpx;
				// margin-bottom: 140rpx;
				width: 230rpx;
				height: 100rpx;
				line-height: 100rpx;
				text-align: center;
				// margin-left: auto;
				// margin-right: auto;
				// margin-top: 96rpx;
				background: linear-gradient(to right, #00984a, #00984a, #00984a);
				color: #FFFFFF;
				border: none;
				border-radius: 2.5rem;
				box-shadow: 0 0 60rpx 0 rgba(0, 0, 0, .2);
				transition: all 0.4s cubic-bezier(.57, .19, .51, .95);
			}
		}
	}
</style>