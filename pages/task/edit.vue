<template>
	<view class="container">
		<view class="info">
			<view class="title">任务信息</view>
			<view class="content">
				<view class="item">
					<label class="label">申请时间</label>
					<!-- <input v-model="MainTask.F_ApplyTime" class="text" name="F_ApplyTime" maxlength="11" placeholder="请输入申请时间" /> -->
					<picker mode="date" @change="onApplyTimeChange" :value="MainTask.F_ApplyTime">
						<view class="uni-input">{{ MainTask.F_ApplyTime==null?'请输入申请时间': MainTask.F_ApplyTime }}</view>
					</picker>
				</view>
				<view class="item">
					<label class="label">预计完成时间</label>
					<!-- <input v-model="MainTask.F_ExpectTime" class="text" name="F_ExpectTime" maxlength="11" placeholder="请输入预计完成时间" /> -->
					<picker mode="date" @change="onExpectTimeChange" :value="MainTask.F_ExpectTime">
						<view class="uni-input">{{ MainTask.F_ExpectTime==null?'请输入预计完成时间': MainTask.F_ExpectTime }}</view>
					</picker>
				</view>
				<view class="item">
					<label class="label">作业内容</label>
					<input class="text" v-model="MainTask.F_Content" name="F_Content" maxlength="100" placeholder="请输入作业内容"/>
				</view>
				<view class="item">
					<label class="label">序号</label>
					<text class="text" :readonly="true" v-model="MainTask.F_Num" name="F_Num"/>{{MainTask.F_Num}}</text>
					<!-- <input class="text" :readonly="true" v-model="MainTask.F_Num" name="F_Num" maxlength="6" placeholder="请输入序号"/> -->
				</view>
				<view class="item">
					<label class="label">公司名称</label>
					<picker mode="selector" @change="onContractorChange" :value="ContractorIndex" range-key="text" :range="ContractorList">
						<view class="uni-input">{{MainTask.commpanyName?MainTask.commpanyName:"请选择公司"}}</view>
					</picker>
				</view>
				<view class="item">
					<label class="label">现场负责人</label>
					<input class="text" v-model="MainTask.F_Leader" name="F_Leader" maxlength="6" placeholder="请输入现场负责人"/>
				</view>
				<view class="item">
					<label class="label">作业人员</label>
					<view class="text" @click="showStaffSelect">{{MainTask.staffNames || '请选择作业人员'}}</view>
				</view>
				<view class="item">
					<label class="label">施工申请表</label>
					<view class="text">{{MainTask.F_ApplyTable ? '已上传' + getImageList(MainTask.F_ApplyTable).length + '张' : '暂无'}}</view>
				</view>
				<view class="image-container" v-if="MainTask.F_ApplyTable || true">
					<view class="image-list" v-if="MainTask.F_ApplyTable">
						<view class="image-item" v-for="(url, index) in getImageList(MainTask.F_ApplyTable)" :key="index">
							<image 
								:src="baseUrl + url" 
								mode="aspectFit"
								class="apply-image"
								@click="previewImage(url)"
							/>
							<view class="delete-btn" @click.stop="deleteImage(index)">×</view>
						</view>
					</view>
					<view class="add-image" @click="chooseImage">
						<view class="add-icon">+</view>
						<view class="add-text">添加施工申请表</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 底部员工选择弹窗 -->
		<uni-popup ref="staffPopup" type="bottom">
			<view class="staff-popup">
				<view class="popup-header">
					<view class="title">选择作业人员</view>
					<view class="close" @click="hideStaffSelect">×</view>
				</view>
				<view class="staff-list">
					<checkbox-group @change="onStaffChange">
						<label class="staff-item" v-for="item in StaffList" :key="item.F_Id">
							<checkbox :value="item.F_Id" :checked="isStaffSelected(item.F_Id)" />
							<text class="staff-name">{{item.F_Name}}</text>
						</label>
					</checkbox-group>
				</view>
				<view class="selected-staff" v-if="MainTask.staffNames">
					<text class="label">已选择：</text>
					<text class="names">{{MainTask.staffNames}}</text>
				</view>
				<view class="popup-footer">
					<button class="confirm-btn" @click="confirmStaffSelect">确定</button>
				</view>
			</view>
		</uni-popup>
		
		<view class="botomm-btn">
			<wButton text="完成" :rotate="isRotate" @click.native="editor"></wButton>
		</view>
	</view>
</template>

<script>
	import { mapGetters } from 'vuex'
	import wButton from '@/components/watch-login/watch-button.vue'
	import wInput from '@/components/watch-login/watch-input.vue'
	import globalConfig from '@/config'
	import uniPopup from '@/components/uni-popup/uni-popup.vue'
	
	export default{
		components: {
			wButton,
			wInput,
			uniPopup
		},
		computed: {
			...mapGetters(['themeBgColor', 'darkMode']),
		},
		data(){
			return{
				id: '', // 任务ID
				isRotate: false,
				loading: false,
				baseUrl: '',
				EnabledMarkList: [
					{ value: true, text: "有效" },
					{ value: false, text: "无效" },
				],
				EnabledMarkIndex:0,
				MainTask: {
					staffList: [], // 已选择的员工列表
					staffNames: '', // 已选择的员工名称
					F_Staff: '' // 已选择的员工ID列表
				},
				ContractorList: [],
				ContractorIndex: 0,
				StaffList: [],
				selectedStaffIds: [], // 已选择的员工ID数组
			}
		},
		onReady() {
			uni.setNavigationBarTitle({
			    title: this.$t('编辑承包商台账信息')
			})
			this.setNavBarColor()
		},
		onShow() {
			this.setNavBarColor()
		},
		onLoad(options) {
			this.id = options.id
			this.baseUrl = globalConfig.baseUrl.replace('/api', '')
			this.loadContractorList() // 先加载承包商列表
			if(this.id) {
				this.loadTaskInfo()
			}
		},
		onShareAppMessage() {
		    return {
		      title: 'EHS-掌上观纹', // 自定义分享标题
		      path: '/pages/start/index', // 分享页面路径（默认当前页）
		      imageUrl: this.baseUrl+'/images/nideke.jpg', // 分享图片（可选）
		      success(res) {
		        console.log('分享成功', res);
		      },
		      fail(err) {
		        console.error('分享失败', err);
		      }
		    };
		},
		methods:{
			onApplyTimeChange(e){
				this.MainTask.F_ApplyTime = e.detail.value
			},
			onExpectTimeChange(e){
				this.MainTask.F_ExpectTime = e.detail.value
			},
			// 加载承包商列表
			async loadContractorList() {
				try {
					const res = await this.$minApi.listContractor({page:1,rows:1000})
					if(res.data) {
						this.ContractorList = res.data.map(item => ({
							value: item.F_Id,
							text: item.F_CompanyName
						}))
					}
				} catch(e) {
					console.error('加载承包商列表失败', e)
					uni.showToast({
						title: '加载承包商列表失败',
						icon: 'none'
					})
				}
			},
			// 加载任务详情
			async loadTaskInfo() {
				try {
					const res = await this.$minApi.getTaskInfo(this.id)
					if(res) {
						this.MainTask = {
							...this.MainTask,
							...res
						}
						console.log('MainTask',this.MainTask)
						this.selectedStaffIds = this.MainTask.F_Staff.split(',')
						
						// 加载该承包商的员工列表
						await this.loadContractorStaff(this.MainTask.F_Server)
					}
				} catch(e) {
					console.error('加载任务信息失败', e)
					uni.showToast({
						title: '加载任务信息失败',
						icon: 'none'
					})
				}
			},
			// 加载承包商员工列表
			async loadContractorStaff(contractorId) {
				if(!contractorId) return
				try {
					const res = await this.$minApi.listStaff({
						serverId:contractorId
					})
					
					if(res) {
						this.StaffList = res.map(item => ({
							F_Id: item.F_Id,
							F_Name: item.F_Name,
							F_Photo: item.F_Photo ? this.baseUrl + item.F_Photo : '/static/img/profile.svg'
						}))
					}
				} catch(e) {
					console.error('加载员工列表失败', e)
					uni.showToast({
						title: '加载员工列表失败',
						icon: 'none'
					})
				}
			},
			// 选择承包商
			onContractorChange(e) {
				this.ContractorIndex = e.detail.value
				const contractor = this.ContractorList[this.ContractorIndex]
				this.MainTask.F_Server = contractor.value
				this.MainTask.commpanyName = contractor.text
				// 清空之前的员工列表
				this.MainTask.staffList = []
				this.MainTask.staffNames = ''
				this.MainTask.F_Staff = ''
				this.selectedStaffIds = []
				// 加载新选中承包商的员工列表
				this.loadContractorStaff(contractor.value)
			},
			// 编辑任务
			async editor() {
				if(!this.MainTask.F_Content) {
					uni.showToast({
						title: '请输入作业内容',
						icon: 'none'
					})
					return
				}
				if(!this.MainTask.F_Server) {
					uni.showToast({
						title: '请选择承包商',
						icon: 'none'
					})
					return
				}
				if(!this.MainTask.F_Leader) {
					uni.showToast({
						title: '请输入现场负责人',
						icon: 'none'
					})
					return
				}
				if(!this.MainTask.F_Staff) {
					uni.showToast({
						title: '请选择作业人员',
						icon: 'none'
					})
					return
				}
				
				try {
					this.isRotate = true
					const res = await this.$minApi.saveTask({
						...this.MainTask,
						F_Id: this.id
					})
					
					if(res.state === 'success') {
						uni.showToast({
							title: '保存成功',
							icon: 'success'
						})
						setTimeout(() => {
							uni.navigateBack()
						}, 1500)
					} else {
						throw new Error(res.msg || '保存失败')
					}
				} catch(e) {
					console.error('保存失败:', e)
					uni.showToast({
						title: e.msg || '保存失败',
						icon: 'none'
					})
				} finally {
					this.isRotate = false
				}
			},
			setNavBarColor() {
				uni.setNavigationBarColor({
				    frontColor: '#000000',
				    backgroundColor: this.themeBgColor,
				    animation: {
				        duration: 400,
				        timingFunc: 'easeIn'
				    }
				})
			},
			// 选择图片
			chooseImage() {
				uni.chooseImage({
					count: 9, // 最多可选择的图片张数
					success: async (res) => {
						try {
							// 显示上传loading
							uni.showLoading({
								title: '上传中...'
							})
							
							// 上传图片
							for(let tempFile of res.tempFilePaths) {
								const uploadRes = await this.$minApi.uploadFile({
									filePath: tempFile,
									name: 'file',
									fileby: 'task', // 指定文件用途
									filetype: 1 // 1表示图片
								})
								
								if(uploadRes.code === 200 && uploadRes.data) {
									// 追加新的图片路径
									const newUrl = uploadRes.data
									if(this.MainTask.F_ApplyTable) {
										this.MainTask.F_ApplyTable += ',' + newUrl
									} else {
										this.MainTask.F_ApplyTable = newUrl
									}
								} else {
									throw new Error(uploadRes.msg || '上传失败')
								}
							}
							
							uni.hideLoading()
							uni.showToast({
								title: '上传成功',
								icon: 'success'
							})
						} catch(e) {
							console.error('上传失败:', e)
							uni.hideLoading()
							uni.showToast({
								title: e.msg || '上传失败',
								icon: 'none'
							})
						}
					}
				})
			},
			// 删除图片
			deleteImage(index) {
				uni.showModal({
					title: '提示',
					content: '确定删除该图片？',
					success: (res) => {
						if (res.confirm) {
							const imageList = this.getImageList(this.MainTask.F_ApplyTable)
							imageList.splice(index, 1)
							this.MainTask.F_ApplyTable = imageList.join(',')
						}
					}
				})
			},
			// 获取图片列表
			getImageList(imageStr) {
				if(!imageStr) return []
				return imageStr.split(',')
			},
			// 预览图片
			previewImage(url) {
				const urls = this.getImageList(this.MainTask.F_ApplyTable)
					.map(item => this.baseUrl + item)
				uni.previewImage({
					urls: urls,
					current: this.baseUrl + url
				})
			},
			// 显示员工选择弹窗
			showStaffSelect() {
				if(!this.MainTask.F_Server) {
					uni.showToast({
						title: '请先选择公司',
						icon: 'none'
					})
					return
				}
				// 如果还没有加载员工列表，则加载
				if(this.StaffList.length === 0) {
					this.loadContractorStaff(this.MainTask.F_Server)
				}
				this.$refs.staffPopup.open()
			},
			// 隐藏员工选择弹窗
			hideStaffSelect() {
				this.$refs.staffPopup.close()
			},
			// 员工选择变化
			onStaffChange(e) {
				this.selectedStaffIds = e.detail.value
				this.updateSelectedStaff()
			},
			// 更新已选择的员工信息
			updateSelectedStaff() {
				this.MainTask.F_Staff = this.selectedStaffIds.join(',')
				this.MainTask.staffList = this.StaffList.filter(item => 
					this.selectedStaffIds.includes(item.F_Id)
				)
				this.MainTask.staffNames = this.MainTask.staffList
					.map(item => item.F_Name)
					.join(',')
			},
			// 确认员工选择
			confirmStaffSelect() {
				this.hideStaffSelect()
			},
			// 判断员工是否被选中
			isStaffSelected(staffId) {
				return this.selectedStaffIds.includes(staffId)
			},
		}
	}
</script>

<style lang="less" scoped>
	
	.container{
		display: flex;
		flex-direction: column;
		gap: 20rpx;
		min-height: 100vh;
		padding-bottom: 320rpx;
		.info {
			background-color: white;
			.title{
				color: #7F7F7F;
				font-weight: 400;
				font-size: 28rpx;
				margin-bottom: 20rpx;
				background-color: rgba(242, 242, 242, 1);
				padding: 20rpx;
			}
			.content{
				padding: 20rpx;
				display: flex;
				flex-direction: column;
				gap: 20rpx;
				.item{
					display: flex;
					flex-direction: row;
					align-items: center;
					line-height: 66rpx;
					justify-content: space-between;
					border-bottom: 1rpx solid rgba(242, 242, 242, 1);
					.label{
						color: #666666;
						font-size: 28rpx;
					}
					.text{
						color: #666666;
						font-size: 28rpx;
						text-align: right;
					}
					.uni-input{
						text-align: right;
						line-height: 30rpx;
						padding-right: 0;
						color: #666666;
					}
					.photo{
						width: 200rpx;
						height: 160rpx;
					}
				}
			}
		}
		.staff-popup {
			background-color: #fff;
			border-radius: 20rpx 20rpx 0 0;
			padding-bottom: env(safe-area-inset-bottom);
			
			.popup-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 20rpx;
				border-bottom: 1rpx solid #eee;
				
				.title {
					font-size: 32rpx;
					font-weight: 500;
					color: #333;
				}
				
				.close {
					font-size: 40rpx;
					color: #999;
					padding: 10rpx;
				}
			}
			
			.staff-list {
				max-height: 60vh;
				overflow-y: auto;
				padding: 20rpx;
				
				.staff-item {
					display: flex;
					align-items: center;
					padding: 20rpx 0;
					border-bottom: 1rpx solid #f5f5f5;
					
					.staff-name {
						margin-left: 20rpx;
						color: #333;
						font-size: 28rpx;
					}
				}
			}
			
			.selected-staff {
				padding: 20rpx;
				background-color: #f8f8f8;
				
				.label {
					color: #666;
					font-size: 28rpx;
				}
				
				.names {
					color: #333;
					font-size: 28rpx;
				}
			}
			
			.popup-footer {
				padding: 20rpx;
				
				.confirm-btn {
					background-color: #00984a;
					color: #fff;
					border-radius: 8rpx;
					font-size: 32rpx;
					height: 80rpx;
					line-height: 80rpx;
				}
			}
		}
		.botomm-btn {
			padding-bottom: 100rpx;
			height: 230rpx;
			position: fixed; /* 固定在屏幕底部 */
			bottom: 0;
			left: 0;
			width: 100%; /* 占满整个屏幕宽度 */
			background-color: #fff; /* 背景颜色，防止透明 */
			padding: 20rpx 0; /* 上下留白 */
			text-align: center; /* 让按钮居中 */
			align-items: center;
			display: flex;
			justify-content: center;
			box-shadow: 0 -10rpx 20rpx rgba(0, 0, 0, 0.1); /* 添加阴影，提升层次感 */
			z-index: 999;
			/deep/ .dlbutton {
				margin-top: 0rpx !important;
			}
		}
		.image-container {
			background-color: white;
			padding: 20rpx;
			display: flex;
			flex-direction: column;
			.image-list {
				display: flex;
				flex-wrap: wrap;
				gap: 20rpx;
				margin-bottom: 20rpx;
				.image-item {
					position: relative;
					.apply-image {
						width: 200rpx;
						height: 160rpx;
						border-radius: 8rpx;
					}
					.delete-btn {
						position: absolute;
						top: -20rpx;
						right: -20rpx;
						width: 40rpx;
						height: 40rpx;
						background-color: rgba(0,0,0,0.5);
						color: #fff;
						border-radius: 50%;
						display: flex;
						align-items: center;
						justify-content: center;
						font-size: 24rpx;
						z-index: 1;
					}
				}
			}
			.add-image {
				width: 200rpx;
				height: 160rpx;
				border: 2rpx dashed #999;
				border-radius: 8rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				color: #999;
				.add-icon {
					font-size: 40rpx;
					line-height: 1;
					margin-bottom: 10rpx;
				}
				.add-text {
					font-size: 24rpx;
				}
			}
		}
	}
</style>