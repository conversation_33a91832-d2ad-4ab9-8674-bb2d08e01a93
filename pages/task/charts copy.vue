<template>
	<view class="container">
		<view class="filters">
			<view class="item">
				<label>类型</label>
				<picker mode="selector" @change="onStatusChange" :value="StatusIndex" range-key="text" :range="StatusList">
					<view class="uni-input">{{StatusList[StatusIndex].text}}</view>
				</picker>
			</view>
			<view class="item">
				<label>月份</label>
				<picker mode="date" fields="month" @change="onMonthChange" :value="StartDate">
					<view class="uni-input">{{ StartDate }}</view>
				</picker>
			</view>
		</view>
		<view class="total">
			<view class="item">
				<view class="title"><view class="num">58</view>个</view>
				<view class="type">承包商作业</view>
			</view>
			<view class="item">
				<view class="title"><view class="num">58</view>个</view>
				<view class="type">内部作业</view>
			</view>
			<view class="item">
				<view class="title"><view class="num">58</view>个</view>
				<view class="type">动火作业</view>
			</view>
			<view class="item">
				<view class="title"><view class="num">58</view>个</view>
				<view class="type">登高作业</view>
			</view>
			<view class="item">
				<view class="title"><view class="num">58</view>个</view>
				<view class="type">吊装作业</view>
			</view>
			<view class="item">
				<view class="title"><view class="num">58</view>个</view>
				<view class="type">有限空间作业</view>
			</view>
		</view>
		<view class="pie-charts">
			<!-- <canvas canvas-id="canvasColumn" id="canvasColumn" class="charts" :style="{'width':cWidth*pixelRatio+'px','height':cHeight*pixelRatio+'px', 'transform': 'scale('+(1/pixelRatio)+')','margin-left':-cWidth*(pixelRatio-1)/2+'px','margin-top':-cHeight*(pixelRatio-1)/2+'px'}"></canvas> -->
			<canvas canvas-id="TSjHsGOVPxLVJfnBXVSRQRKTtoLjFGxc" id="TSjHsGOVPxLVJfnBXVSRQRKTtoLjFGxc" class="charts" @touchend="tap"/>
			<canvas canvas-id="DRqwIzCqmZNFuaErqugMQxkBJpmNajpw" id="DRqwIzCqmZNFuaErqugMQxkBJpmNajpw" class="charts" @touchend="tap"/>
		</view>
	</view>
</template>

<script>
	import { mapGetters } from 'vuex'
	import uniIcons from '@/components/uni-icons/uni-icons.vue'
	import uniNavBar from '@/components/uni-nav-bar/uni-nav-bar.vue'
	import uCharts from '@/common/lib/u-charts.min.js'
	var _self;
	var canvaColumn = null;
	var pieCharts = {};
	var barCharts = {};
	export default {
		components: {
			uniIcons,
			uniNavBar
		},
		computed: {
			...mapGetters(['themeBgColor', 'darkMode']),
		},
		data() {
			return {
				cWidth: 750,
				cHeight: 500,
				pixelRatio: 1,
				serverData: '',
				pieChartData:{
					series: [
					  {
						data: [{"name":"一班","value":50},{"name":"二班","value":30},{"name":"三班","value":20},{"name":"四班","value":18},{"name":"五班","value":8}]
					  }
					]
				},
				chartData: {
					categories: ["2018","2019","2020","2021","2022","2023"],
					series: [
					  {
						name: "目标值",
						data: [35,36,31,33,13,34]
					  },
					  {
						name: "完成量",
						data: [18,27,21,24,6,28]
					  }
					]
				},
			}
		},
		mounted() {
			_self = this;
			//#ifdef MP-ALIPAY
			uni.getSystemInfo({
				success: function(res) {
					if (res.pixelRatio > 1) {
						//正常这里给2就行，如果pixelRatio=3性能会降低一点
						//_self.pixelRatio =res.pixelRatio;
						_self.pixelRatio = 2;
					}
				}
			});
			//#endif
			this.cWidth = uni.upx2px(750);
			this.cHeight = uni.upx2px(500);
			// this.loadPie();
			
		},
		onReady() {
			uni.setNavigationBarTitle({
			    title: this.$t('承包商作业统计')
			})
			this.setNavBarColor()
		},
		onShow() {
			this.setNavBarColor()
		},
		onLoad(){
			this.drawPieCharts('TSjHsGOVPxLVJfnBXVSRQRKTtoLjFGxc',this.pieChartData);
			this.drawBarCharts('DRqwIzCqmZNFuaErqugMQxkBJpmNajpw',this.chartData);
		},
		methods:{
			setNavBarColor() {
				// navBar-bg-color
				uni.setNavigationBarColor({
				    frontColor: '#000000',
				    backgroundColor: this.themeBgColor,
				    animation: {
				        duration: 400,
				        timingFunc: 'easeIn'
				    }
				})
			},
			drawPieCharts(id,data){
			  const ctx = uni.createCanvasContext(id, this);
			  pieCharts[id] = new uCharts({
				type: "ring",
				context: ctx,
				width: this.cWidth,
				height: this.cHeight,
				series: data.series,
				animation: true,
				rotate: false,
				rotateLock: false,
				background: "#FFFFFF",
				color: ["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],
				padding: [5,5,5,5],
				dataLabel: true,
				enableScroll: false,
				legend: {
				  show: true,
				  position: "right",
				  lineHeight: 25
				},
				title: {
				  name: "收益率",
				  fontSize: 15,
				  color: "#666666"
				},
				subtitle: {
				  name: "70%",
				  fontSize: 25,
				  color: "#7cb5ec"
				},
				extra: {
				  ring: {
					ringWidth: 60,
					activeOpacity: 0.5,
					activeRadius: 10,
					offsetAngle: 0,
					labelWidth: 15,
					border: false,
					borderWidth: 3,
					borderColor: "#FFFFFF"
				  }
				}
			  });
			},
			drawBarCharts(id,data){
				  const ctx = uni.createCanvasContext(id, this);
				  barCharts[id] = new uCharts({
					type: "bar",
					context: ctx,
					width: this.cWidth,
					height: this.cHeight,
					categories: data.categories,
					series: data.series,
					animation: true,
					background: "#FFFFFF",
					color: ["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],
					padding: [15,30,0,5],
					enableScroll: false,
					legend: {},
					xAxis: {
					  boundaryGap: "justify",
					  disableGrid: false,
					  min: 0,
					  axisLine: false,
					  max: 70
					},
					yAxis: {},
					extra: {
					  bar: {
						// type: "stack",
						width: 30,
						meterBorde: 1,
						meterFillColor: "#FFFFFF",
						activeBgColor: "#000000",
						activeBgOpacity: 0.08,
						categoryGap: 2
					  }
					}
				  });
				},
			loadPie(){
				const canvaColumn = new uCharts({
					$this: _self,
					canvasId: 'canvasColumn',
					type: 'column',
					legend: true,
					fontSize: 11,
					background: '#FFFFFF',
					pixelRatio: _self.pixelRatio,
					animation: true,
					categories: this.chartData.categories,
					series: this.chartData.series,
					xAxis: {
						disableGrid: false,
					},
					yAxis: {
						
					},
					dataLabel: true,
					width: _self.cWidth * _self.pixelRatio,
					height: _self.cHeight * _self.pixelRatio,
					extra: {
						column: {
							width: _self.cWidth * _self.pixelRatio * 0.45 / this.chartData.categories.length
						}
					}
				});
			}
		}
	}
</script>

<style lang="less">
	.container{
		height: 100vh;
		background-color: white;
		.filters{
			display: flex;
			flex-direction: row;
			align-items: center;
			background-color: white;
			padding: 20rpx;
			width: 100%;
			flex-wrap: wrap;
			gap: 10rpx;
			.item{
				display: flex;
				flex-direction: row;
				align-items: center;
				flex: 1;
				label{
					color: #4c4c4c;
					// width: 20%;
					padding: 10rpx;
					align-items: center;
					text-align: center;
					line-height: 20rpx;
				}
				.uni-input{
					color: #7c7c7c;
					border: 1px solid #00984a;
					align-items: center;
					text-align: left;
					width: 36vw;
					border-radius: 10rpx !important;
					line-height: 20rpx;
				}
			}
		}
		.total{
			display: flex;
			flex-wrap: wrap;
			padding: 20rpx;
			// gap: 12rpx;
			.item{
				height: 140rpx;
				display: flex;
				flex-direction: column;
				color: #222222;
				font-size: 24rpx;
				align-items: center;
				gap: 2rpx;
				width: 30vw;
				.title{
					align-items: center;
					display: flex;
					flex-direction: row;
					gap: 10rpx;
					.num{
						align-items: center;
						font-size: 34rpx;
						font-weight: 500;
					}
				}
				.type{
					color: #7c7c7c;
					align-items: center;
				}
			}
			.item:nth-child(1),
			.item:nth-child(2),
			.item:nth-child(4),
			.item:nth-child(5) {
			  border-right: 1rpx solid #dddddd;
			}
		}
		
	}
	.charts{
	    width: 750rpx;
	    height: 500rpx;
	  }
</style>