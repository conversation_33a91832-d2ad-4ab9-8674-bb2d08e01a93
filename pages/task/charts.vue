<template>
	<view class="container">
		<view class="filters">
			<view class="item">
				<label>类型</label>
				<picker mode="selector" @change="onStatusChange" :value="StatusIndex" range-key="text" :range="StatusList">
					<view class="uni-input">{{StatusList[StatusIndex].text}}</view>
				</picker>
			</view>
			<view class="item">
				<label>月份</label>
				<picker mode="date" fields="month" @change="onMonthChange" :value="StartDate">
					<view class="uni-input">{{ StartDate }}</view>
				</picker>
			</view>
			<view class="item">
				<label>作业类型</label>
				<view class="uni-input" @click="showTaskTypeSelector">
					{{selectedTaskTypes || '请选择任务类型'}}
				</view>
			</view>
		</view>
		<view class="total">
			<view class="item">
				<view class="title"><view class="num">{{taskCount.ServerCount}}</view>个</view>
				<view class="type">承包商作业</view>
			</view>
			<view class="item">
				<view class="title"><view class="num">{{taskCount.InterlCount}}</view>个</view>
				<view class="type">内部作业</view>
			</view>
			<view class="item">
				<view class="title"><view class="num">{{taskCount.CountFire}}</view>个</view>
				<view class="type">动火作业</view>
			</view>
			<view class="item">
				<view class="title"><view class="num">{{taskCount.CountHight}}</view>个</view>
				<view class="type">登高作业</view>
			</view>
			<view class="item">
				<view class="title"><view class="num">{{taskCount.CountDiaoZhuang}}</view>个</view>
				<view class="type">吊装作业</view>
			</view>
			<view class="item">
				<view class="title"><view class="num">{{taskCount.CountKongjian}}</view>个</view>
				<view class="type">有限空间作业</view>
			</view>
		</view>
		<view class="pie-charts" ref="charts" v-show="showCharts">
			<canvas canvas-id="TSjHsGOVPxLVJfnBXVSRQRKTtoLjFGxc" id="TSjHsGOVPxLVJfnBXVSRQRKTtoLjFGxc" class="charts"/>
			<canvas canvas-id="DRqwIzCqmZNFuaErqugMQxkBJpmNajpw" id="DRqwIzCqmZNFuaErqugMQxkBJpmNajpw" class="charts"/>
		</view>
		<!-- 自定义多选弹窗 -->
		<uni-popup ref="taskTypePopup" type="bottom" :mask-click="false">
			<view class="multi-select-popup">
				<view class="popup-header">
					<view class="title">选择任务类型</view>
					<view class="actions">
						<text class="select-all" @click="selectAllTaskTypes">{{isAllSelected ? '取消全选' : '全选'}}</text>
						<text class="close" @click="hideTaskTypeSelector">×</text>
					</view>
				</view>
				<view class="select-list">
					<checkbox-group @change="onTaskTypeMultiChange">
						<label class="select-item" v-for="(item, index) in TaskTypeList" :key="index">
							<checkbox :value="item.value.toString()" :checked="selectedTaskTypeValues.includes(item.value.toString())" />
							<text class="item-name">{{item.text}}</text>
						</label>
					</checkbox-group>
				</view>
				<view class="popup-footer">
					<button class="confirm-btn" @click="confirmTaskTypeSelect">确定</button>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import { mapGetters } from 'vuex'
	import uniIcons from '@/components/uni-icons/uni-icons.vue'
	import uniNavBar from '@/components/uni-nav-bar/uni-nav-bar.vue'
	import uCharts from '@/common/lib/u-charts.min.js'
	import uniPopup from '@/components/uni-popup/uni-popup.vue'
	import globalConfig from '@/config'
	var _self;
	var canvaColumn = null;
	var pieCharts = {};
	var barCharts = {};
	export default {
		components: {
			uniIcons,
			uniNavBar,
			uniPopup
		},
		computed: {
			...mapGetters(['themeBgColor', 'darkMode']),
		},
		data() {
			return {
				baseUrl: '',
				showCharts: true,
				cWidth: 750,
				cHeight: 500,
				pixelRatio: 1,
				StartDate: this.getCurrentMonth(), // 添加当前月份作为默认值
				StatusList: [
					// { value: '', text: '==请选择==' },
					{ value: 'year', text: 'Year To Date' },
					{ value: 'month', text: 'Single Month' }
				],
				TaskTypeIndex: 0,
				TaskTypeList: [
					// { value: '', text: '==请选择==' },
					{ value: 0, text: '全部' },
					{ value:200, text:"承包商作业" },
					{ value:201, text:"动火作业" },
					{ value:202, text:"登高作业" },
					{ value:203, text:"吊装作业" },
					{ value:204, text:"有限空间作业" }
				],
				StatusIndex: 1, // 默认选中 Year To Date
				pieChartData:{
					series: [
					  {
						data: [{"name":"一班","value":50,"labelText":"四班:50个"},{"name":"二班","value":30},{"name":"三班","value":20},{"name":"四班","value":18},{"name":"五班","value":8}]
					  }
					]
				},
				chartData: {
					categories: ["2018","2019","2020","2021","2022","2023"],
					series: [
					  {
						name: "目标值",
						data: [{"color":"#1890FF",value:35},{"color":"#91CB74",value:15},{"color":"#EE6666",value:5},{"color":"#FAC858",value:25},{"color":"#3CA272",value:18},{"color":"#73C0DE",value:20}]
					  }
					]
				},
				taskCount: {
					ServerCount: 0,
					InterlCount: 0,
					CountFire: 0,
					CountHight: 0,
					CountDiaoZhuang: 0,
					CountKongjian: 0
				},
				selectedTaskTypes: '',
				selectedTaskTypeValues: [],
				isAllSelected: false
			}
		},
		mounted() {
			_self = this;
			//#ifdef MP-ALIPAY
			uni.getSystemInfo({
				success: function(res) {
					if (res.pixelRatio > 1) {
						//正常这里给2就行，如果pixelRatio=3性能会降低一点
						//_self.pixelRatio =res.pixelRatio;
						_self.pixelRatio = 2;
					}
				}
			});
			//#endif
			this.cWidth = uni.upx2px(750);
			this.cHeight = uni.upx2px(500);
			console.log('mouted')
			// this.loadPie();
			// this.drawPieCharts('TSjHsGOVPxLVJfnBXVSRQRKTtoLjFGxc',this.pieChartData);
			// this.drawBarCharts('DRqwIzCqmZNFuaErqugMQxkBJpmNajpw',this.chartData);
			this.loadTaskCharts();
		},
		onReady() {
			uni.setNavigationBarTitle({
			    title: this.$t('承包商作业统计')
			})
			this.setNavBarColor()
		},
		onShow() {
			this.setNavBarColor()
		},
		onLoad(){
			this.baseUrl = globalConfig.baseUrl.replace('/api', '');
			// 初始化默认选中全部
			this.selectedTaskTypeValues = this.TaskTypeList.map(item => item.value.toString());
			this.updateSelectedTaskTypes();
			this.isAllSelected = true;
			
			// 加载图表数据
			this.loadTaskCharts();
		},
		onShareAppMessage() {
		    return {
		      title: 'EHS-掌上观纹', // 自定义分享标题
		      path: '/pages/start/index', // 分享页面路径（默认当前页）
		      imageUrl: this.baseUrl+'/images/nideke.jpg', // 分享图片（可选）
		      success(res) {
		        console.log('分享成功', res);
		      },
		      fail(err) {
		        console.error('分享失败', err);
		      }
		    };
		},
		methods:{
			loadTaskCharts(){
				let typeIds = this.selectedTaskTypeValues.join(',')
				// 如果已经在显示loading，就不再显示
				if(!uni.getStorageSync('loading_shown')) {
					uni.showLoading({
						title: '加载中...'
					});
					uni.setStorageSync('loading_shown', true);
				}
				
				// 记录传入的typeIds
				console.log('loadTaskCharts接收到的typeIds:', typeIds);
				
				// 准备请求参数
				const params = {
					dateType: this.StatusList[this.StatusIndex].value,
					date: this.StartDate,
					type: typeIds || this.TaskTypeList[this.TaskTypeIndex].value
				};
				
				console.log('请求参数:', params);
				
				this.$minApi.loadTaskCharts(params).then(res => {
					uni.hideLoading();
					uni.removeStorageSync('loading_shown');
					
					console.log('加载结果:', res);
					this.taskCount = res.taskCount;

					let pieChartData = {
						series: [
							{
								data: res.pieData
							}
						]
					};
					let barChartData = {
						categories: res.taskType,
						series: [
							{
								name: "任务数",
								data: res.colorData
							}
						]
					};
					
					setTimeout(() => {
						this.drawPieCharts('TSjHsGOVPxLVJfnBXVSRQRKTtoLjFGxc', pieChartData);
						console.log('pieChartData', barChartData);
						this.drawBarCharts('DRqwIzCqmZNFuaErqugMQxkBJpmNajpw', barChartData);
						
						// 显示成功提示
						uni.showToast({
							title: '筛选成功',
							icon: 'success',
							duration: 1500
						});
					}, 500);
				}).catch(err => {
					console.error('加载图表数据失败', err);
					uni.hideLoading();
					uni.removeStorageSync('loading_shown');
					
					uni.showToast({
						title: '加载失败',
						icon: 'none'
					});
				});
			},
			setNavBarColor() {
				// navBar-bg-color
				uni.setNavigationBarColor({
				    frontColor: '#000000',
				    backgroundColor: this.themeBgColor,
				    animation: {
				        duration: 400,
				        timingFunc: 'easeIn'
				    }
				})
			},
			// 获取当前月份
			getCurrentMonth() {
				const date = new Date();
				const year = date.getFullYear();
				const month = (date.getMonth() + 1).toString().padStart(2, '0');
				return `${year}-${month}`;
			},
			// 类型选择改变
			onStatusChange(e) {
				this.StatusIndex = e.detail.value;
				this.loadTaskCharts();
			},
			onTaskTypeChange(e){
				this.TaskTypeIndex = e.detail.value;
				this.loadTaskCharts();
			},
			// 月份选择改变
			onMonthChange(e) {
				this.StartDate = e.detail.value;
				this.loadTaskCharts();
			},
			drawPieCharts(id,data){
			  // 处理数据，为每个数据项添加更友好的标签
			  if (data && data.series && data.series[0] && data.series[0].data) {
			    data.series[0].data = data.series[0].data.map(item => {
			      // 确保每个项目都有一个标准的标签格式：名称+数量
			      item.labelText = `${item.name}:${item.value}个`;
			      return item;
			    });
			  }
			  
			  const ctx = uni.createCanvasContext(id, this);
			  pieCharts[id] = new uCharts({
				$this: _self,
				type: "ring",
				context: ctx,
				width: _self.cWidth * _self.pixelRatio,
				height: _self.cHeight * _self.pixelRatio,
				series: data.series,
				animation: true,
				rotate: false,
				rotateLock: false,
				background: "#FFFFFF",
				color: ["#FFD902","#FF3D3D","#1774FF","#FF7529","#3CA272"],
				// 调整内边距，左右多留空间
				padding: [15, 60, 15, 60],
				dataLabel: true,
				enableScroll: false,
				legend: {
				  show: false,
				  position: "right",
				  lineHeight: 25
				},
				extra: {
				  ring: {
					// 减小圆环宽度
					customRadius: 50,
					ringWidth: 30,
					activeOpacity: 0.5,
					activeRadius: 5,
					offsetAngle: 0,
					// 增加标签宽度，防止文字挤压
					labelWidth: 5,
					border: false,
					borderWidth: 3,
					borderColor: "#FFFFFF"
				  }
				}
			  });
			},
			getNiceScale(maxValue, interval = 5) {
			  // 向上取整到下一个interval的倍数
			  return Math.ceil(maxValue / interval) * interval;
			},

			drawBarCharts(id,data){
				const maxValue = Math.max(...data.series[0].data.map(item => item.value));
				const max = this.getNiceScale(maxValue);
				  const ctx = uni.createCanvasContext(id, this);
				  barCharts[id] = new uCharts({
					$this: _self,
					type: "bar",
					context: ctx,
					width: _self.cWidth * _self.pixelRatio,
					height: _self.cHeight * _self.pixelRatio,
					categories: data.categories,
					series: data.series,
					animation: true,
					background: "#FFFFFF",
					//color: ["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],
					padding: [15,30,0,5],
					enableScroll: false,
					legend: {show: false,},
					xAxis: {
					  boundaryGap: "justify",
					  disableGrid: false,
					  min: 0,
					  axisLine: false,
					  max: max,
					  format: function(val) {
						return parseInt(val); // 将纵轴的值转为整数
					  }
					},
					yAxis: {
						format: function(val) {
							return parseInt(val); // 将纵轴的值转为整数
						}
					},
					extra: {
					  bar: {
						type: "group",
						width: 30,
						meterBorde: 1,
						meterFillColor: "#FFFFFF",
						activeBgColor: "#000000",
						activeBgOpacity: 0.08,
						categoryGap: 2,
						// linearType: "custom",
						// customColor: ["#91CB74","#1890FF","#FAC858","#EE6666","#73C0DE","#3CA272"]
					  }
					}
				  });
				},
			loadPie(){
				const canvaColumn = new uCharts({
					$this: _self,
					canvasId: 'canvasColumn',
					type: 'column',
					legend: true,
					fontSize: 11,
					background: '#FFFFFF',
					pixelRatio: _self.pixelRatio,
					animation: true,
					categories: this.chartData.categories,
					series: this.chartData.series,
					xAxis: {
						disableGrid: false,
					},
					yAxis: {
						
					},
					dataLabel: true,
					width: _self.cWidth * _self.pixelRatio,
					height: _self.cHeight * _self.pixelRatio,
					extra: {
						column: {
							width: _self.cWidth * _self.pixelRatio * 0.45 / this.chartData.categories.length
						}
					}
				});
			},
			showTaskTypeSelector() {
				this.showCharts = false;
				this.$refs.taskTypePopup.open();
			},
			hideTaskTypeSelector() {
				this.showCharts = true;
				this.$refs.taskTypePopup.close();
			},
			onTaskTypeMultiChange(e) {
				this.selectedTaskTypeValues = e.detail.value;
				this.updateSelectedTaskTypes();
				// 判断是否全选
				this.isAllSelected = this.selectedTaskTypeValues.length === this.TaskTypeList.length;
			},
			updateSelectedTaskTypes() {
				const selectedTypes = this.TaskTypeList.filter(item => 
					this.selectedTaskTypeValues.includes(item.value.toString())
				);
				
				if (selectedTypes.length === 0) {
					this.selectedTaskTypes = '请选择任务类型';
				} else if (selectedTypes.length === this.TaskTypeList.length) {
					this.selectedTaskTypes = '全部';
				} else if (selectedTypes.length > 3) {
					this.selectedTaskTypes = `已选择${selectedTypes.length}项`;
				} else {
					this.selectedTaskTypes = selectedTypes.map(item => item.text).join(', ');
				}
				setTimeout(() => {
					// 明确调用 loadTaskCharts 方法
					this.loadTaskCharts();
				}, 100);
			},
			confirmTaskTypeSelect() {
				if(this.selectedTaskTypeValues.length > 0) {
					const typeIds = this.selectedTaskTypeValues.join(',');
					console.log('筛选条件：', typeIds);
					
					// 关闭弹窗
					this.hideTaskTypeSelector();
					
					// 显示加载中
					uni.showLoading({
						title: '加载中...'
					});
					
					// 确保关闭弹窗后再触发筛选
					setTimeout(() => {
						// 明确调用 loadTaskCharts 方法
						this.loadTaskCharts();
					}, 100);
				} else {
					// 如果没有选择，显示提示
					uni.showToast({
						title: '请至少选择一项',
						icon: 'none'
					});
				}
			},
			selectAllTaskTypes() {
				if (this.isAllSelected) {
					// 如果已经全选，则取消全选
					this.selectedTaskTypeValues = [];
					this.isAllSelected = false;
				} else {
					// 否则全选
					this.selectedTaskTypeValues = this.TaskTypeList.map(item => item.value.toString());
					this.isAllSelected = true;
				}
				this.updateSelectedTaskTypes();
			}
		}
	}
</script>

<style lang="less">
	.container{
		height: 110vh;
		background-color: white;
		.filters{
			display: flex;
			flex-direction: row;
			align-items: center;
			background-color: white;
			padding: 20rpx;
			width: 100%;
			flex-wrap: wrap;
			gap: 10rpx;
			.item{
				display: flex;
				flex-direction: row;
				align-items: center;
				flex: 1;
				label{
					color: #4c4c4c;
					// width: 20%;
					padding: 10rpx;
					align-items: center;
					text-align: center;
					line-height: 20rpx;
				}
				.uni-input{
					color: #7c7c7c;
					border: 1px solid #00984a;
					align-items: center;
					text-align: left;
					width: 36vw;
					border-radius: 10rpx !important;
					line-height: 40rpx;
					padding: 10rpx;
					height: 60rpx;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}
			}
		}
		.total{
			display: flex;
			flex-wrap: wrap;
			padding: 20rpx;
			// gap: 12rpx;
			.item{
				height: 140rpx;
				display: flex;
				flex-direction: column;
				color: #222222;
				font-size: 24rpx;
				align-items: center;
				gap: 2rpx;
				width: 30vw;
				.title{
					align-items: center;
					display: flex;
					flex-direction: row;
					gap: 10rpx;
					.num{
						align-items: center;
						font-size: 34rpx;
						font-weight: 500;
					}
				}
				.type{
					color: #7c7c7c;
					align-items: center;
				}
			}
			.item:nth-child(1),
			.item:nth-child(2),
			.item:nth-child(4),
			.item:nth-child(5) {
			  border-right: 1rpx solid #dddddd;
			}
		}
	}
	// .pie-charts{
	// 	padding: 20rpx;
	// 	display: flex;
	// 	flex-direction: column;
	// 	align-items: center;
		
	// 	.charts{
	// 		width: 750rpx;
	// 		height: 500rpx;
	// 		transform-origin: center center;
	// 		// transform: scale(0.85);
	// 	}
	// }
	.charts{
		width: 750rpx;
		height: 500rpx;
	}
	/* 增加弹窗组件的 z-index */
	.uni-popup {
	  z-index: 9999 !important; /* 设置一个足够高的值 */
	}
	/* 确保 canvas 不会超出其层级 */
	canvas {
	  z-index: auto; /* 或较低的具体数值 */
	}
	.multi-select-popup {
		z-index: 9999 !important;
		background-color: #fff;
		border-radius: 20rpx 20rpx 0 0;
		padding-bottom: env(safe-area-inset-bottom);
		
		.popup-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 20rpx;
			border-bottom: 1rpx solid #eee;
			
			.title {
				font-size: 32rpx;
				font-weight: 500;
				color: #333;
			}
			
			.actions {
				display: flex;
				align-items: center;
				gap: 10rpx;
				
				.select-all {
					font-size: 28rpx;
					color: #00984a;
					font-weight: 500;
				}
				
				.close {
					font-size: 40rpx;
					color: #999;
					padding: 10rpx;
				}
			}
		}
		
		.select-list {
			max-height: 60vh;
			overflow-y: auto;
			padding: 20rpx;
			
			.select-item {
				display: flex;
				align-items: center;
				padding: 20rpx 0;
				border-bottom: 1rpx solid #f5f5f5;
				
				.item-name {
					margin-left: 20rpx;
					color: #333;
					font-size: 28rpx;
				}
			}
		}
		
		.popup-footer {
			padding: 20rpx;
			
			.confirm-btn {
				background-color: #00984a;
				color: #fff;
				border-radius: 8rpx;
				font-size: 32rpx;
				height: 80rpx;
				line-height: 80rpx;
			}
		}
	}
</style>