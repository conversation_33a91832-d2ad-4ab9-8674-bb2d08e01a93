<template>
	<view class="container">
		<!-- <view class="header">
			<image class="logo" :src="userAvatar" mode="aspectFit"></image>
			<view class="name">{{ user.F_RealName }}</view>
		</view> -->
		<view class="shizil">
			<!-- <view class="stats-cards">
			  <view class="stat-card">
			    <view class="stat-title">无损工事故</view>
			    <view class="stat-value">{{ DaysSinceLastLoss }}<text class="unit">天</text></view>
			  </view>
			  <view class="stat-card">
			    <view class="stat-title">无损工最高纪录</view>
			    <view class="stat-value">{{ MaxConsecutiveDays }}<text class="unit">天</text></view>
			  </view>
			</view>
			<view style="display: flex;justify-content: center;width: 100vw;">
				<view class="calendar-grid">
				  <view 
					v-for="(cell, index) in gridCells" 
					:key="index"
					:class="['grid-cell', {'hidden': cell.hidden}]"
					:style="{ backgroundColor: cell.color }"
					@click="handleCellClick(cell)"
				  >
					{{ cell.day }}
				  </view>
				</view>
			</view> -->
			<image :src="baseUrl+'/images/nideke.jpg'" mode="aspectFit"></image>
		</view>
		<view class="workbenches">
			<view class="title"><image class="block_blue" src="/static/img/home/<USER>"></image>快捷入口</view>
			<view class="icons">
				<view class="icon">
					<navigator url="/pages/approval/Apply?flowCode=1752672430846" open-type="navigate">
						<image src="/static/img/home/<USER>" mode="aspectFit"></image>
						<view class="icon_name">变更审批</view>
					</navigator>
				</view>
				<view class="icon" v-if="$checkMenuAuth('cbsgl')">
					<navigator url="/pages/approval/Apply?flowCode=1752376955043" open-type="navigate">
						<image src="/static/img/home/<USER>" mode="aspectFit"></image>
						<view class="icon_name">化学品审批</view>
					</navigator>
				</view>
				<view class="icon" v-if="$checkMenuAuth('sggl')">
					<navigator url="/pages/approval/Apply?flowCode=1752377301423" open-type="navigate">
						<image src="/static/img/home/<USER>" mode="aspectFit"></image>
						<view class="icon_name">外部持入审批</view>
					</navigator>
				</view>
				<view class="icon" v-if="$checkMenuAuth('qxgl')">
					<navigator url="/pages/approval/Apply?flowCode=1752377470492" open-type="navigate">
						<image src="/static/img/home/<USER>" mode="aspectFit"></image>
						<view class="icon_name">双休日审批</view>
					</navigator>
				</view>
			</view>
			<view class="title"><image class="block_blue" src="/static/img/home/<USER>"></image>工作台</view>
			<view class="icons">
				<view class="icon">
					<navigator url="/pages/smartscreen/index" open-type="navigate">
						<image src="/static/img/home/<USER>" mode="aspectFit"></image>
						<view class="icon_name">危险作业</view>
					</navigator>
				</view>
				<view class="icon" v-if="$checkMenuAuth('cbsgl')">
					<navigator url="/pages/navigate/index?type=cheng" open-type="navigate">
						<image src="/static/img/home/<USER>" mode="aspectFit"></image>
						<view class="icon_name">承包商管理</view>
					</navigator>
				</view>
				<view class="icon" v-if="$checkMenuAuth('sggl')">
					<navigator url="/pages/navigate/index?type=shigu" open-type="navigate">
						<image src="/static/img/home/<USER>" mode="aspectFit"></image>
						<view class="icon_name">事故管理</view>
					</navigator>
				</view>
				<view class="icon" v-if="$checkMenuAuth('sggl')">
					<navigator url="/pages/flow/index" open-type="navigate">
						<image src="/static/img/home/<USER>" mode="aspectFit"></image>
						<view class="icon_name">审批流程</view>
					</navigator>
				</view>
				<view class="icon" v-if="$checkMenuAuth('qxgl')">
					<navigator url="/pages/user/index" open-type="navigate">
						<image src="/static/img/home/<USER>" mode="aspectFit"></image>
						<view class="icon_name">权限管理</view>
					</navigator>
				</view>
			</view>
			<!-- <wButton style="margin-top: -30rpx;" text="退出登录" :rotate="isRotate" @click.native="startLogout()"></wButton> -->
		</view>
		
	</view>
</template>

<script>
	import { mapGetters } from 'vuex'
	import wButton from '@/components/watch-login/watch-button.vue'
	import globalConfig from '@/config'
	export default {
		components: {
			wButton,
		},
		computed: {
			...mapGetters(['themeBgColor', 'darkMode', 'user']),
			// 处理用户头像，返回默认头像或用户自定义头像
			userAvatar() {
				return this.user?.F_HeadIcon || '/static/img/profile.svg'
			}
		},
		data() {
			return {
				baseUrl: "",
				gridCells: [],
				DaysSinceLastLoss: 0,
				MaxConsecutiveDays: 0,
				StartDate: new Date().toISOString().slice(0,7),
				isRotate: false, //是否加载旋转
				colorMap: {
				  "空": "#ffffff",
				  // "无": "#92D14F",
						"无": "#00984a",
				  "虚惊": "#31849b",
				  "急救": "#4472C4",
				  "医疗处理": "#FFD966",
				  "工作受限": "#ED7D31",
				  "损工": "#FF0000",
				  "致残": "#C00000",
				  "死亡": "#A5A5A5"
				},
			};
		},
		onReady() {
			uni.setNavigationBarTitle({
			    title: this.$t('首页')
			})
			this.setNavBarColor()
		},
		onShow() {
			this.setNavBarColor()
			this.$minApi.checkLoginState().then(res=>{
				if(res.state == 'success'){
					
				} else{
					uni.reLaunch({
						url: '/pages/login/login'
					});
				}
			})
		},
		onLoad() {
			console.log('ss',this.user.F_Id);
			this.loadCalendar();
			this.baseUrl = globalConfig.baseUrl.replace("/api","");
		},
		onShareAppMessage() {
		    return {
		      title: 'EHS-掌上观纹', // 自定义分享标题
		      path: '/pages/start/index', // 分享页面路径（默认当前页）
		      imageUrl: this.baseUrl+'/images/nideke.jpg', // 分享图片（可选）
		      success(res) {
		        console.log('分享成功', res);
		      },
		      fail(err) {
		        console.error('分享失败', err);
		      }
		    };
		},
		methods:{
			loadCalendar(){
					// 初始化日历网格
				this.gridCells = this.generateGridCells()
				this.loadData()
			},
			getDaysInMonth(dateString) {
			    const [year, month] = dateString.split('-').map(Number);
			    return new Date(year, month, 0).getDate();
			},
			generateGridCells() {
			  const cells = []
			  const totalCells = 49 // 7x7 网格
			
			  // 初始化所有单元格为隐藏
			  for (let i = 0; i < totalCells; i++) {
			    cells.push({ hidden: true })
			  }
				const days = this.getDaysInMonth(this.StartDate);
			  // 第一行 - 3个格子 (1,2,3)
			  cells[2] = { day: 1, color: '#ffffff' }
			  cells[3] = { day: 2, color: '#ffffff' }
			  cells[4] = { day: 3, color: '#ffffff' }
			
			  // 第二行 - 3个格子 (4,5,6)
			  cells[9] = { day: 4, color: '#ffffff' }
			  cells[10] = { day: 5, color: '#ffffff' }
			  cells[11] = { day: 6, color: '#ffffff' }
			
			  // 第三行 - 7个格子 (7-13)
			  cells[14] = { day: 7, color: '#ffffff' }
			  cells[15] = { day: 8, color: '#ffffff' }
			  cells[16] = { day: 9, color: '#ffffff' }
			  cells[17] = { day: 10, color: '#ffffff' }
			  cells[18] = { day: 11, color: '#ffffff' }
			  cells[19] = { day: 12, color: '#ffffff' }
			  cells[20] = { day: 13, color: '#ffffff' }
			
			  // 第四行 - 7个格子 (14-20)
			  cells[21] = { day: 14, color: '#ffffff' }
			  cells[22] = { day: 15, color: '#ffffff' }
			  cells[23] = { day: 16, color: '#ffffff' }
			  cells[24] = { day: 17, color: '#ffffff' }
			  cells[25] = { day: 18, color: '#ffffff' }
			  cells[26] = { day: 19, color: '#ffffff' }
			  cells[27] = { day: 20, color: '#ffffff' }
			
			  // 第五行 - 7个格子 (21-27)
			  cells[28] = { day: 21, color: '#ffffff' }
			  cells[29] = { day: 22, color: '#ffffff' }
			  cells[30] = { day: 23, color: '#ffffff' }
			  cells[31] = { day: 24, color: '#ffffff' }
			  cells[32] = { day: 25, color: '#ffffff' }
			  cells[33] = { day: 26, color: '#ffffff' }
			  cells[34] = { day: 27, color: '#ffffff' }
			
			  // 第六行 - 3个格子 (28,29,30)
			  cells[37] = { day: 28, color: '#ffffff' }
			  if(days == 28){
				  cells[38] = { day: '', color: '#ffffff', hidden: false }
				  cells[39] = { day: '', color: '#ffffff', hidden: false }
				  cells[44] = { day: '', color: '#ffffff', hidden: false }
				  cells[45] = { day: '', color: '#ffffff', hidden: false }
				  cells[46] = { day: '', color: '#ffffff', hidden: false }
			  }else{
				  cells[38] = { day: 29, color: '#ffffff' }
				  cells[39] = { day: 30, color: '#ffffff' }
			  }
			
			  // 第七行 - 3个格子 (31和两个空白格)
			  if(days == 31){
				cells[44] = { day: 31, color: '#ffffff' }
			  }else{
				cells[44] = { day: '', color: '#ffffff', hidden: false }
			  }
			  
			  cells[45] = { day: '', color: '#ffffff', hidden: false }
			  cells[46] = { day: '', color: '#ffffff', hidden: false }
			
			  return cells
			},
			async loadData() {
				try{
					let that = this;
					this.$minApi.loadEventByMonth({month: that.StartDate}).then(res => {
						that.updateCalendar(res)
					})
					
					this.$minApi.getEventDays().then(res=>{
						that.DaysSinceLastLoss = res.DaysSinceLastLoss;
						that.MaxConsecutiveDays = res.MaxConsecutiveDays;
					})
				} catch (error) {
					console.error('加载数据失败:', error)
				}
			},
			updateCalendar(data) {
			  this.gridCells = this.gridCells.map(cell => {
			    if (cell.hidden) return cell
			    const dayData = this.findDayData(data, cell.day)
			    return {
			      ...cell,
			      color: this.colorMap[dayData ? dayData.Level : '空']
			    }
			  })
			},
			findDayData(data, day) {
			  if (!data || !Array.isArray(data)) return null
			  const dateStr = this.formatDateString(this.StartDate, day)
			  return data.find(item => item.Day === dateStr)
			},
			formatDateString(monthStr, day) {
			  const date = new Date(monthStr)
			  const year = date.getFullYear()
			  const month = (date.getMonth() + 1).toString().padStart(2, '0')
			  return `${year}-${month}-${day.toString().padStart(2, '0')}`
			},
			setNavBarColor() {
				// navBar-bg-color
				uni.setNavigationBarColor({
				    frontColor: '#ffffff',
				    backgroundColor: '#00984a',
					// backgroundColor: '#FFFFFF',
				    animation: {
				        duration: 400,
				        timingFunc: 'easeIn'
				    }
				})
			},
			startLogout() {
				uni.showModal({
					title: '提示',
					content: '确定要退出登录吗？',
					success: (res) => {
						if (res.confirm) {
							uni.showLoading({
								title: '退出中...'
							})
							this.isRotate = true
							this.$minApi.logout().then(res => {
								this.isRotate = false
								if (res.state === 'success') {
									// 调用 vuex 的登出方法清除用户状态
									this.$store.dispatch('logout')
									uni.showToast({
										title: '已退出登录',
										icon: 'success'
									})
								} else {
									uni.showToast({
										title: res.message || '退出失败',
										icon: 'none'
									})
								}
							}).catch(err => {
								console.error('退出登录失败:', err)
								uni.showToast({
									title: '退出失败，请重试',
									icon: 'none'
								})
							}).finally(() => {
								uni.hideLoading()
							})
						}
					}
				})
			}
		}
	}
</script>


<style lang="less">
	@import url("../../components/watch-login/css/icon.css");
	page{
		// background-color: #00984a;
		background-color: #FFFFFF;
	}
	.container{
		display: flex;
		flex-direction: column;
		background: #FFFFFF;
		margin-top: 60rpx;
	}
	
	.header{
		height: 25vh;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		background-color: #00984a;
		padding-bottom: 40rpx;
		gap: 10rpx;
	}
	.header .logo{
		width: 180rpx;
		height: 180rpx;
		border-radius: 50%;
		object-fit: cover;
	}
	.name{
		color: white;
		font-weight: 500;
	}
	.shizil {
		height: 36vh;
		position: relative;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: center;
		image{
			width: 100%;
			object-fit: cover;
		}
		.stats-cards{
			position: absolute;
			display: flex;
			// flex-direction: column;
			bottom: 0;
			margin-bottom: 30rpx;
			gap: 2rpx;
			padding: 10rpx;
			width: 100vw;
			justify-content: space-between;
			.stat-card {
				background: #fff;
				padding: 20rpx;
				border-radius: 4rpx;
				border: 1rpx solid #eee;
				.stat-title {
					font-size: 22rpx;
					color: #666;
					margin-bottom: 2rpx;
				}
			  
			  .stat-value {
			    font-size: 36rpx;
			    font-weight: bold;
			    color: #333;
			    
			    .unit {
			      font-size: 20rpx;
			      margin-left: 4rpx;
			    }
			  }
			}
		}
		.calendar-grid {
			width: 65vw;
		  display: grid;
		  grid-template-columns: repeat(7, minmax(0, 1fr));
		  // grid-auto-columns: 1fr;
		  gap: 2rpx;
		//   margin-bottom: 15rpx;
		  padding: 20rpx;
		  // max-width: 600rpx;
		  // margin: 0 auto 15rpx;
		  
		  .grid-cell {
			aspect-ratio: 1;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 28rpx;
			color: #555555;
			border-radius: 0;
			min-height: 60rpx;
			max-height: 65rpx;
			
			&.hidden {
			  visibility: hidden;
			  background-color: transparent;
			}
		
			&:not(.hidden) {
			  border: 1rpx solid #eee;
			}
		  }
		}
	}
	.workbenches{
		margin-top: -30rpx;
		background-color: white;
		border-top-left-radius: 0.8rem;
		border-top-right-radius: 0.8rem;
		border-top: 4rpx solid #00984a;
		display: flex;
		flex-direction: column;
		gap:40rpx;
		height: 76vh;
		padding: 20rpx;
		.title{
			font-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30;
			font-size: 34rpx;
			font-weight: normal;
			line-height: 34rpx;
			display: flex;
			gap: 14rpx;
			align-items: center;
			padding-left: 30rpx;
			padding-top: 20rpx;
		}
		.title .block_blue{
			width: 30rpx;
			height: 30rpx;
			// background-color: #00984a;
		}
	}
	.icons{
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
		gap: 10rpx;
		.icon{
			display: flex;
			flex-direction: column;
			align-items: center;
			// width: 24%;
			width: 22vw;
			text-align: center;
			image{
				width: 100rpx;
				height: 100rpx;
			}
			.icon_name{
				text-align: center;
				font-size: 26rpx;
			}
		}
	}
</style>