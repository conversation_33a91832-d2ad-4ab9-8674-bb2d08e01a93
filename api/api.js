import Vue from 'vue'
import MinRequest from '@/utils/MinRequest'
import globalConfig from '@/config'

const minRequest = new MinRequest()

// 请求拦截器
minRequest.interceptors.request((request) => {
	const user = Vue.prototype.$store.getters.user
	if (user && user.ApiToken) {
		request.header = {
			...request.header,
			'WC-Token': user.ApiToken
		}
	}
	return request
})

// 响应拦截器
minRequest.interceptors.response((response) => {
	return response.data
})

// 设置默认配置
minRequest.setConfig((config) => {
	config.baseURL = globalConfig.baseUrl
	return config
})

export const GetUserList = (params) => {
	return minRequest.get('/User/GetUserList', params)
}

export const GetRoleList = () => {
	return minRequest.get('/User/GetRoleList')
}

export const SettingRole = (params) => {
	return minRequest.post('/User/SettingRole', params)
}

export default {
	// 这里统一管理api请求
	apis: {
		login(params) {
			return minRequest.post('/User/Login', params)
		},
		wechatLogin(params) {
			return minRequest.post('/User/WechatLogin', params)
		},
		logout() {
			return minRequest.post('/User/LoginOff')
		},
		checkLoginState() {
			return minRequest.post('/User/CheckLoginState')
		},
		listUser(params) {
			return minRequest.get('/User/GetUserList',params)
		},
		listRole() {
			return minRequest.get('/User/GetRoleList')
		},
		settingRole(params){
			return minRequest.post('/User/SettingRole',params)
		},
		listContractor(params) {
			return minRequest.get('/Task/GetContractorList',params)
		},
		getContractor(params) {
			return minRequest.get('/Task/GetContractorInfo',params)
		},
		listStaff(params) {
			return minRequest.get('/Task/GetStaffList',params)
		},
		getStaff(params) {
			return minRequest.get('/Task/GetStaffInfo',params)
		},
		saveStaff(params){
			return minRequest.post('/Task/StaffSave',params)
		},
		saveContractor(params){
			return minRequest.post('/Task/ContractorSave',params)
		},
		delStaff(params){
			return minRequest.post('/Task/DelStaffInfo',params)
		},
		verifyOldPassword(params) {
			return minRequest.post('/user/verify-password', params)
		},
		resetPassword(params) {
			return minRequest.post('/user/reset-password', params)
		},
		bindEmail(params) {
			return minRequest.post('/user/bind-email', params)
		},
		getUser(id) {
			return minRequest.get(`/user/${id}`)
		},
		addUser(params) {
			return minRequest.post('/user', params)
		},
		updateUser(params) {
			return minRequest.put(`/user/${params.id}`, params)
		},
		deleteUser(id) {
			return minRequest.delete(`/user/${id}`)
		},
		saveUserAvatar(params) {
			return minRequest.post('/user/avatar', params)
		},
		// 项目审批列表
		listAuditProject() {
			return minRequest.get('/get/audit/project/list')
		},
		// 用户审批列表
		listAuditUser() {
			return minRequest.get('/get/audit/user/list')
		},
		// 事故模块接口
		// 获取事故列表
		getEventList(params) {
			return minRequest.get('/Event/GetEventList', params)
		},
		// 获取事故详情
		getEventInfo(eventId) {
			return minRequest.get('/Event/GetEventInfo', { eventId })
		},
		// 保存事故信息
		saveEvent(params) {
			return minRequest.post('/Event/EventSave', params)
		},
		getEventDays() {
			return minRequest.get('/Event/GetDays')
		},
		// 任务模块接口
		// 获取任务列表
		getTaskList(params) {
			return minRequest.get('/Task/GetTaskList', params)
		},
		// 获取分组任务详情
		getGroupTaskInfo(taskId) {
			return minRequest.get('/Task/GetGroupTaskInfo', { taskId })
		},
		// 获取simple任务详情
		getSimpleTaskInfo(taskId) {
			return minRequest.get('/Task/GetSimpleTaskInfo', { taskId })
		},
		// 获取任务详情
		getTaskInfo(taskId) {
			return minRequest.get('/Task/GetTaskInfo', { taskId })
		},
		completeTask(taskId) {
			return minRequest.post('/Task/CompleteTask', { taskId })
		},
		// 保存任务信息
		saveTask(params) {
			return minRequest.post('/Task/SaveTask', params)
		},
		loadBarCharts(params){
			return minRequest.get('/Event/GetChartsJson', params)
		},
		loadTableCharts(params){
			return minRequest.get('/Event/GetChartsByYearJson', params)
		},
		loadEventByMonth(params){
			return minRequest.get('/Event/GetChartsByMonthJson',params)
		},
		loadEventByDay(params){
			return minRequest.get('/Event/GetChartsByDayJson',params)
		},
		loadTaskCharts(params){
			return minRequest.get('/Task/GetChartsJson',params)
		},
		loadIndexData(){
			return minRequest.get('/Task/GetIndexData')
		},
		// 获取事故列表
		getOrgList() {
			return minRequest.get('/Event/GetOrgList')
		},
		/**
		 * 文件上传
		 * @param {Object} params - 上传参数
		 * @param {File} params.filePath - 文件路径
		 * @param {String} params.name - 文件参数名
		 * @param {String} params.fileby - 文件用途
		 * @param {Number} [params.filetype=1] - 文件类型：1图片，2excel，3template，4文档
		 * @returns {Promise}
		 */
		uploadFile(params) {
			const user = Vue.prototype.$store.getters.user
			return new Promise((resolve, reject) => {
				console.log('params', params)

				uni.uploadFile({
					url: globalConfig.baseUrl + '/File/Upload?'+'filetype=4&fileby'+params.fileby,
					filePath: params.filePath,
					name: params.name || 'file',
					header: {
						'WC-Token': user.ApiToken
					},
					formData: {
						fileby: params.fileby || 'default',
						filetype: params.filetype || 1
					},
					success: (res) => {
						if(typeof res.data === 'string') {
							res.data = JSON.parse(res.data)
						}
						if(res.data.code === 0) {
							resolve({
								code: 200,
								msg: '上传成功',
								data: res.data.data[0].src // 返回文件路径
							})
						} else {
							reject({
								code: res.data.code,
								msg: res.data.msg || '上传失败'
							})
						}
					},
					fail: (err) => {
						reject({
							code: 500,
							msg: err.errMsg || '上传失败'
						})
					}
				})
			})
		},

		// 台账相关接口
		getChangeLedgerJson(params) {
			return minRequest.get('/Flow/GetChangeLedgerJson', params)
		},
		getChangeLedgerStatistics() {
			return minRequest.get('/Flow/GetChangeLedgerStatistics')
		},
		getHolidayWorkLedgerJson(params) {
			return minRequest.get('/Flow/GetHolidayWorkLedgerJson', params)
		},
		getHolidayWorkLedgerStatistics() {
			return minRequest.get('/Flow/GetHolidayWorkLedgerStatistics')
		},
		getExternalChemicalLedgerJson(params) {
			return minRequest.get('/Flow/GetExternalChemicalLedgerJson', params)
		},
		getExternalChemicalLedgerStatistics() {
			return minRequest.get('/Flow/GetExternalChemicalLedgerStatistics')
		},
		getChemicalLedgerJson(params) {
			return minRequest.get('/Flow/GetChemicalLedgerJson', params)
		},
		getChemicalLedgerStatistics() {
			return minRequest.get('/Flow/GetChemicalLedgerStatistics')
		},
		getApprovalList(params) {
			return minRequest.get('/Flow/GetApprovalList', params)
		},
		getApprovalCategoryCounts(params) {
			return minRequest.get('/Flow/GetApprovalCategoryCounts', params)
		},
		GetFlowInstanceList(params) {
			return minRequest.post('/Flow/GetFlowInstanceList', params)
		},
		deleteFlow(params) {
			return minRequest.post('/Flow/DeleteFlow?keyValue='+ params)
		},
		// 根据流程编码获取表单
		getFlowForm(params) {
			return minRequest.get('/Flow/GetFlowForm', params)
		},
		// 根据流程实例ID获取表单
		getFlowFormById(params) {
			return minRequest.get('/Flow/GetFlowFormById', params)
		},
		// 获取草稿
		getDraft(params) {
			return minRequest.get('/Flow/GetDraft', params)
		},
		// 提交审批
		submitFlow(params) {
			return minRequest.post('/Flow/SubmitFlow', params)
		},
		// 保存草稿
		saveDraft(params) {
			return minRequest.post('/Flow/SaveDraft', params)
		},
		getUserDrafts() {
			return minRequest.post('/Flow/GetUserDrafts')
		},
		// 删除草稿
		delDraft(params) {
			return minRequest.post('/Flow/DeleteDraft', params)
		},
		// 获取变更流程列表
		getChangeFlowList() {
			return minRequest.get('/Flow/GetChangeFlowList')
		},
		// 获取可选审批人列表
		chooseApprover(params) {
			return minRequest.post('/Flow/ChooseApprover', params)
		},
		// 审批
		auditFlow(params) {
			return minRequest.post('/Flow/Audit', params)
		},
		// 获取当前用户审批节点类型
		getCurrentUserApprovalType(flowInstanceId) {
			return minRequest.get('/Flow/GetCurrentUserApprovalType?flowInstanceId='+ flowInstanceId)
		},
		// 获取流程详情
		getFlowDetails(id) {
			return minRequest.get('/Flow/GetDetails?id='+ id)
		},
		// 获取审批统计数据
		getFlowStatistics(params) {
			return minRequest.get('/Flow/GetStatistics', params)
		}
	}
}
